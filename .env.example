# Description: Example of .env file
# Usage: Copy this file to .env and change the values
#        according to your needs
#        Do not commit .env file to git
#        Do not change .env.example file

# Config directory
# Directory where files, logs and database will be stored
# Example: LANGFLOW_CONFIG_DIR=~/.langflow
LANGFLOW_CONFIG_DIR=

# Save database in the config directory
# Values: true, false
# If false, the database will be saved in Lang<PERSON>'s root directory
# This means that the database will be deleted when <PERSON><PERSON> is uninstalled
# and that the database will not be shared between different virtual environments
# Example: LANGFLOW_SAVE_DB_IN_CONFIG_DIR=true
LANGFLOW_SAVE_DB_IN_CONFIG_DIR=

# Database URL
# Postgres example: LANGFLOW_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/langflow
# SQLite example:
LANGFLOW_DATABASE_URL=sqlite:///./langflow.db

# Database connection retry
# Values: true, false
# If true, the database will retry to connect to the database if it fails
# Example: LANGFLOW_DATABASE_CONNECTION_RETRY=true
LANGFLOW_DATABASE_CONNECTION_RETRY=false

# Cache type
LANGFLOW_LANGCHAIN_CACHE=SQLiteCache

# Server host
# Example: LANGFLOW_HOST=127.0.0.1
LANGFLOW_HOST=

# Worker processes
# Example: LANGFLOW_WORKERS=1
LANGFLOW_WORKERS=

# Server port
# Example: LANGFLOW_PORT=7860
LANGFLOW_PORT=

# Logging level
# Example: LANGFLOW_LOG_LEVEL=critical
LANGFLOW_LOG_LEVEL=

# Path to the log file
# Example: LANGFLOW_LOG_FILE=logs/langflow.log
LANGFLOW_LOG_FILE=

# Path to the frontend directory containing build files
# Example: LANGFLOW_FRONTEND_PATH=/path/to/frontend/build/files
LANGFLOW_FRONTEND_PATH=

# Whether to open the browser after starting the server
# Values: true, false
# Example: LANGFLOW_OPEN_BROWSER=true
LANGFLOW_OPEN_BROWSER=

# Whether to remove API keys from the projects saved in the database
# Values: true, false
# Example: LANGFLOW_REMOVE_API_KEYS=false
LANGFLOW_REMOVE_API_KEYS=

# Whether to use RedisCache or ThreadingInMemoryCache or AsyncInMemoryCache
# Values: async, memory, redis
# Example: LANGFLOW_CACHE_TYPE=memory
# If you want to use redis then the following environment variables must be set:
# LANGFLOW_REDIS_HOST (default: localhost)
# LANGFLOW_REDIS_PORT (default: 6379)
# LANGFLOW_REDIS_DB (default: 0)
# LANGFLOW_REDIS_CACHE_EXPIRE (default: 3600)
LANGFLOW_CACHE_TYPE=

# Set AUTO_LOGIN to false if you want to disable auto login
# and use the login form to login. LANGFLOW_SUPERUSER and LANGFLOW_SUPERUSER_PASSWORD
# must be set if AUTO_LOGIN is set to false
# Values: true, false
LANGFLOW_AUTO_LOGIN=

# Superuser username
# Example: LANGFLOW_SUPERUSER=admin
LANGFLOW_SUPERUSER=

# Superuser password
# Example: LANGFLOW_SUPERUSER_PASSWORD=123456
LANGFLOW_SUPERUSER_PASSWORD=

# Should store environment variables in the database
# Values: true, false
LANGFLOW_STORE_ENVIRONMENT_VARIABLES=

# STORE_URL
# Example: LANGFLOW_STORE_URL=https://api.langflow.store
# LANGFLOW_STORE_URL=

# DOWNLOAD_WEBHOOK_URL
#
# LANGFLOW_DOWNLOAD_WEBHOOK_URL=

# LIKE_WEBHOOK_URL
#
# LANGFLOW_LIKE_WEBHOOK_URL=

# Value must finish with slash /
#BACKEND_URL=http://localhost:7860/
BACKEND_URL=