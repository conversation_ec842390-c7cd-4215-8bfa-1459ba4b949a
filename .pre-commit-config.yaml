repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.1.0
    hooks:
      - id: check-case-conflict
      - id: end-of-file-fixer
        # python, js and ts only
        files: \.(py|js|ts)$
      - id: mixed-line-ending
        files: \.(py|js|ts)$
        args:
          - --fix=lf
      - id: trailing-whitespace
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.3
    hooks:
      - id: ruff
        name: ruff check
        types_or: [python, pyi]
        args: [--fix]
      - id: ruff-format
        types_or: [python, pyi]
        args: [--config, pyproject.toml]
