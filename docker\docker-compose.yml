networks:
  langflow:

services:
  langflow:
    build:
      context: ..
      dockerfile: docker/dev.Dockerfile
    image: dev-langflow
    container_name: dev-langflow
    restart: always
    ports:
      - "7860:7860"
      - "3000:3000"
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - LANGFLOW_DATABASE_URL=********************************************/langflow
      - LANGFLOW_SUPERUSER=langflow
      - LANGFLOW_SUPERUSER_PASSWORD=langflow
      - LANGFLOW_CONFIG_DIR=/var/lib/langflow
      - LANGFLOW_AUTO_LOGIN=False
      - LANGFLOW_SUPERUSER=admin
      - LANGFLOW_SUPERUSER_PASSWORD=securepassword
      - LANGFLOW_SECRET_KEY=dBuuuB_FHLvU8T9eUNlxQF9ppqRxwWpXXQ42kM2_fbg
      - LANGFLOW_NEW_USER_IS_ACTIVE=False
    env_file:
      - ../.env
    volumes:
      - ../:/app
    depends_on:
      - postgres
    networks:
      -  langflow


  postgres:
    container_name: postgres
    image: pgvector/pgvector:pg16
    environment:
      POSTGRES_USER: langflow
      POSTGRES_PASSWORD: langflow
      POSTGRES_DB: langflow
    ports:
      - "5432:5432"
    networks:
      -  langflow

