version: "3.8"

services:
  langflow:
    image: langflow:1.2.0
    restart: unless-stopped
    ports:
      - "7860:7860"
    depends_on:
      - postgres
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - LANGFLOW_DATABASE_URL=********************************************/langflow
      - LANGFLOW_SUPERUSER=langflow
      - LANGFLOW_SUPERUSER_PASSWORD=langflow
      # - LANGFLOW_CONFIG_DIR=app/langflow
      - LANGFLOW_AUTO_LOGIN=False
      - LANGFLOW_SUPERUSER=admin
      - LANGFLOW_SUPERUSER_PASSWORD=securepassword
      - LANGFLOW_SECRET_KEY=dBuuuB_FHLvU8T9eUNlxQF9ppqRxwWpXXQ42kM2_fbg
      - LANGFLOW_NEW_USER_IS_ACTIVE=False
    volumes:
      - langflow-data:/app/langflow

  postgres:
    image: postgres:16
    restart: unless-stopped
    environment:
      POSTGRES_USER: langflow
      POSTGRES_PASSWORD: langflow
      POSTGRES_DB: langflow
    ports:
      - "5432:5432"
    volumes:
      - langflow-postgres:/var/lib/postgresql/data

volumes:
  langflow-postgres:
  langflow-data: