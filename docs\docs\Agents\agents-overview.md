---
title: Agents overview
slug: /agents-overview
---

**Agents** are AI systems that use LLMs as a brain to analyze problems and select external tools.

Instead of developers having to create logical statements to direct every possible path of a program, an agent can operate with autonomy. An agent can leverage external tools and APIs to gather information and take action, demonstrate chain-of-thought reasoning, and generate tailored text for specific purposes.

To simplify the development of agents, <PERSON><PERSON> created a custom [Tool calling agent](/components-agents#agent-component) component that simplifies configuration and lets developers focus on solving problems with agents.

![Tool calling agentcomponent](/img/tool-calling-agent-component.png)

To get started, see [Create a problem solving agent](/agents-tool-calling-agent-component).