---
title: Welcome to Langflow
slug: /
---

Langflow is a new, visual framework for building multi-agent and RAG applications. It is open-source, Python-powered, fully customizable, and LLM and vector store agnostic.

Its intuitive interface allows for easy manipulation of AI building blocks, enabling developers to quickly prototype and turn their ideas into powerful, real-world solutions.

Langflow empowers developers to rapidly prototype and build AI applications with its user-friendly interface and powerful features. Whether you're a seasoned AI developer or just starting out, Langflow provides the tools you need to bring your AI ideas to life.

## Visual flow builder

Lang<PERSON> is an intuitive visual flow builder. This drag-and-drop interface allows developers to create complex AI workflows without writing extensive code. You can easily connect different components, such as prompts, language models, and data sources, to build sophisticated AI applications.

![Langflow in action](/img/playground-response.png)

## Use cases

Langflow can be used for a wide range of AI applications, including:

* [Craft intelligent chatbots](/tutorials-memory-chatbot)
* [Build document analysis systems](/tutorials-document-qa)
* [Generate compelling content](/tutorials-blog-writer)
* [Orchestrate multi-agent applications](/starter-projects-simple-agent)

## Community and support

Join <PERSON>'s vibrant community of developers and AI enthusiasts. See the following resources to join discussions, share your projects, and get support:

* [Contribute to <PERSON><PERSON>](contributing-how-to-contribute)
* [Langflow Discord Server](https://discord.gg/EqksyE2EX9)
* [@langflow_ai](https://twitter.com/langflow_ai) 

## Get started with Langflow

- [Install Langflow](/get-started-installation)
- [Quickstart](/get-started-quickstart)

