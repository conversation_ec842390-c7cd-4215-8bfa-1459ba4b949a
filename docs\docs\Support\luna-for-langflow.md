---
title: Luna for Langflow
slug: /luna-for-langflow
---

With [<PERSON> for Langflow](https://www.datastax.com/products/luna-langflow) support, you can develop and deploy Langflow applications with confidence.

**Luna** is a subscription to the Langflow expertise at DataStax. It's meant for users of Langflow who want all the benefits of running their own open-source deployments, but with the peace of mind that comes with having direct access to the team that has authored the majority of the Langflow code.

**Luna** subscribers can get help with general-purpose and technical questions for their open-source Langflow deployments, and if an issue is encountered, DataStax is there to help.

Ready to subscribe?

Visit the [Luna for Langflow](https://www.datastax.com/products/luna-langflow) product page to get started.

## Supported software

Luna for Langflow support covers only the following software versions for Lang<PERSON>.

Last updated: 2025-03-11

## Core information
- **Langflow Version**: `1.2.0`
- **Python Version Required**: `>=3.10, <3.14`