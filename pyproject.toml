
[project]
name = "langflow"
version = "1.2.0"
description = "A Python package with a built-in web application"
requires-python = ">=3.10,<3.14"
license = "MIT"
keywords = ["nlp", "langchain", "openai", "gpt", "gui"]
readme = "README.md"
maintainers = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "Cristhian Zanforlin", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "Italo dos Anjos", email = "italojohnnydos<PERSON><EMAIL>" },
]
# Define your main dependencies here
dependencies = [
    "langflow-base==0.2.0",
    "beautifulsoup4==4.12.3",
    "google-search-results>=2.4.1,<3.0.0",
    "google-api-python-client==2.154.0",
    "huggingface-hub[inference]>=0.23.2,<1.0.0",
    "networkx==3.4.2",
    "fake-useragent==1.5.1",
    "pyarrow==19.0.0",
    "wikipedia==1.4.0",
    "qdrant-client==1.9.2",
    "weaviate-client==4.10.2",
    "faiss-cpu==1.9.0.post1",
    "types-cachetools==5.5.0.20240820",
    "pymongo==4.10.1",
    "supabase==2.6.0",
    "certifi>=2023.11.17,<2025.0.0",
    "certifi==2024.8.30",
    "fastavro==1.9.7",
    "redis==5.2.1",
    "metaphor-python==0.1.23",
    'pywin32==307; sys_platform == "win32"',
    "langfuse==2.53.9",
    "metal_sdk==2.5.1",
    "MarkupSafe==3.0.2",
    "boto3==1.34.162",
    "numexpr==2.10.2",
    "qianfan==0.3.5",
    "pgvector==0.3.6",
    "langchain==0.3.10",
    "elasticsearch==8.16.0",
    "pytube==15.0.0",
    "dspy-ai==2.5.41",
    "assemblyai==0.35.1",
    "litellm==1.60.2",
    "chromadb==0.5.23",
    "zep-python==2.0.2",
    "youtube-transcript-api==0.6.3",
    "Markdown==3.7",
    "upstash-vector==0.6.0",
    "GitPython==3.1.43",
    "kubernetes==31.0.0",
    "json_repair==0.30.3",
    "langwatch==0.1.16",
    "langsmith==0.1.147",
    "yfinance==0.2.50",
    "wolframalpha==5.1.3",
    "astra-assistants[tools]~=2.2.11",
    "composio-langchain==0.7.1",
    "composio-core==0.7.1",
    "spider-client==0.1.24",
    "nltk==3.9.1",
    "lark==1.2.2",
    "jq==1.8.0",
    "pydantic-settings==2.4.0",
    "ragstack-ai-knowledge-store==0.2.1",
    "duckduckgo_search==7.2.1",
    "opensearch-py==2.8.0",
    "langchain-google-genai==2.0.6",
    "langchain-cohere==0.3.3",
    "langchain-anthropic==0.3.0",
    "langchain-astradb==0.5.3",
    "langchain-openai==0.2.12",
    "langchain-google-vertexai==2.0.7",
    "langchain-groq==0.2.1",
    "langchain-pinecone==0.2.2",
    "langchain-mistralai==0.2.3",
    "langchain-chroma==0.1.4",
    "langchain-aws==0.2.7",
    "langchain-unstructured==0.1.5",
    "langchain-milvus==0.1.7",
    "langchain-mongodb==0.2.0",
    "langchain-nvidia-ai-endpoints==0.3.8",
    "langchain-google-calendar-tools==0.0.1",
    "langchain-google-community==2.0.3",
    "langchain-elasticsearch==0.3.0",
    "langchain-ollama==0.2.1",
    "langchain-sambanova==0.1.0",
    "langchain-community~=0.3.10",
    "sqlalchemy[aiosqlite]>=2.0.38,<3.0.0",
    "atlassian-python-api==3.41.16",
    "mem0ai==0.1.34",
    "needle-python>=0.4.0",
    "aiofile>=3.9.0,<4.0.0",
    "sseclient-py==1.8.0",
    "arize-phoenix-otel>=0.6.1",
    "openinference-instrumentation-langchain>=0.1.29",
    "crewai==0.102.0",
    "mcp>=0.9.1",
    "uv>=0.5.7",
    "webrtcvad>=2.0.10",
    "scipy>=1.14.1",
    "ag2>=0.1.0",
    "scrapegraph-py>=1.12.0",
    "pydantic-ai>=0.0.19",
    "smolagents>=1.8.0",
    "apify-client>=1.8.1",
    "pylint>=3.3.4",
    "ruff>=0.9.7",
    "langchain-graph-retriever==0.6.1",
    "graph-retriever==0.6.1",
    "opik>=1.6.3",
    "psycopg==3.2.6",
    "psycopg2-binary==2.9.10",
]

[dependency-groups]
dev = [
    "pytest-instafail>=0.5.0",
    "types-redis>=*******",
    "ipykernel>=6.29.0",
    "mypy>=1.11.0",
    "ruff>=0.9.7,<0.10",
    "httpx>=0.27.0",
    "pytest>=8.2.0",
    "types-requests>=2.32.0",
    "requests>=2.32.0",
    "pytest-cov>=5.0.0",
    "pandas-stubs>=2.1.4.231227",
    "types-pillow>=10.2.0.20240213",
    "types-pyyaml>=********",
    "types-python-jose>=*******",
    "types-passlib>=********",
    "pytest-mock>=3.14.0",
    "pytest-xdist>=3.6.0",
    "types-pywin32>=306.0.0.4",
    "types-google-cloud-ndb>=*******",
    "pytest-sugar>=1.0.0",
    "respx>=0.21.1",
    "pytest-asyncio>=0.23.0",
    "pytest-profiling>=1.7.0",
    "pre-commit>=3.7.0",
    "vulture>=2.11",
    "dictdiffer>=0.9.0",
    "pytest-split>=0.9.0",
    "pytest-flakefinder>=1.1.0",
    "types-markdown>=3.7.0.20240822",
    "packaging>=24.1,<25.0",
    "asgi-lifespan>=2.1.0",
    "pytest-github-actions-annotate-failures>=0.2.0",
    "pytest-codspeed>=3.0.0",
    "blockbuster>=1.5.20,<1.6",
    "types-aiofiles>=24.1.0.20240626",
    "codeflash>=0.8.4",
    "hypothesis>=6.123.17",
    "locust>=2.32.9",
    "pytest-rerunfailures>=15.0",
    "scrapegraph-py>=1.10.2",
    "pydantic-ai>=0.0.19",
    "elevenlabs>=1.52.0",
    "faker>=37.0.0",
]

[tool.uv.sources]
langflow-base = { workspace = true }
langflow = { workspace = true }

[tool.uv.workspace]
members = ["src/backend/base", "."]

[tool.hatch.build.targets.wheel]
packages = ["src/backend/langflow"]


[project.urls]
Repository = "https://github.com/langflow-ai/langflow"
Documentation = "https://docs.langflow.org"

[project.optional-dependencies]
deploy = [
    "celery[redis]>=5.3.6",
    "flower>=2.0.0"
]
couchbase = [
    "couchbase>=4.2.1"
]
cassio = [
    "cassio>=0.1.7"
]
local = [
    "llama-cpp-python~=0.2.0",
    "sentence-transformers>=2.3.1",
    "ctransformers>=0.2.10"
]
clickhouse-connect = [
    "clickhouse-connect==0.7.19"
]

nv-ingest = [
    # nv-ingest-client 2025.2.7.dev0 does not correctly install its
    # dependencies, so we need to install some manually.
    "nv-ingest-client==2025.2.7.dev0",
    "python-pptx==0.6.23",
    "pymilvus[bulk_writer,model]==2.5.0",
    "llama-index-embeddings-nvidia==0.1.5",
]

postgresql = [
  "sqlalchemy[postgresql_psycopg2binary]",
    "sqlalchemy[postgresql_psycopg]",

]

[project.scripts]
langflow = "langflow.__main__:main"

[tool.codespell]
skip = '.git,*.pdf,*.svg,*.pdf,*.yaml,*.ipynb,poetry.lock,*.min.js,*.css,package-lock.json,*.trig.,**/node_modules/**,./stuff/*,*.csv'
# Ignore latin etc
ignore-regex = '.*(Stati Uniti|Tense=Pres).*'


[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests", "integration"]
console_output_style = "progress"
filterwarnings = ["ignore::DeprecationWarning", "ignore::ResourceWarning"]
log_cli = true
log_cli_format = "%(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
markers = ["async_test", "api_key_required"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.coverage.run]
command_line = """
    -m pytest --ignore=tests/integration
    --cov --cov-report=term --cov-report=html
    --instafail -ra -n auto -m "not api_key_required"
"""
source = ["src/backend/base/langflow/"]
omit = ["*/alembic/*", "tests/*", "*/__init__.py"]


[tool.coverage.report]
sort = "Stmts"
skip_empty = true
show_missing = false
ignore_errors = true


[tool.coverage.html]
directory = "coverage"


[tool.ruff]
exclude = ["src/backend/base/langflow/alembic/*", "src/frontend/tests/assets/*"]
line-length = 120

[tool.ruff.lint]
pydocstyle.convention = "google"
select = ["ALL"]
ignore = [
    "C90", # McCabe complexity
    "CPY", # Missing copyright
    "COM812", # Messes with the formatter
    "ERA", # Eradicate commented-out code
    "FIX002", # Line contains TODO
    "ISC001", # Messes with the formatter
    "PERF203", # Rarely useful
    "PLR09", # Too many something (arg, statements, etc)
    "RUF012", # Pydantic models are currently not well detected. See https://github.com/astral-sh/ruff/issues/13630
    "TD002", # Missing author in TODO
    "TD003", # Missing issue link in TODO
    "TRY301", # A bit too harsh (Abstract `raise` to an inner function)

    # Rules that are TODOs
    "ANN",
]

# Preview rules that are not yet activated
external = ["RUF027"]

[tool.ruff.lint.per-file-ignores]
"scripts/*" = [
    "D1",
    "INP",
    "T201",
]
"src/backend/tests/*" = [
    "D1",
    "PLR2004",
    "S101",
    "SLF001",
]

[tool.ruff.lint.flake8-builtins]
builtins-allowed-modules = [ "io", "logging", "socket"]

[tool.mypy]
plugins = ["pydantic.mypy"]
follow_imports = "skip"
disable_error_code = ["type-var"]
namespace_packages = true
mypy_path = "langflow"
ignore_missing_imports = true

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
