import re
from abc import abstractmethod
from typing import TYPE_CHECKING, cast

from langchain.agents import <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseMultiActionAgent, BaseSingleActionAgent
from langchain.agents.agent import RunnableAgent
from langchain_core.runnables import Runnable

from langflow.base.agents.callback import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langflow.base.agents.events import ExceptionWithMessageError, process_agent_events
from langflow.base.agents.utils import data_to_messages
from langflow.custom import Component
from langflow.custom.custom_component.component import _get_component_toolkit
from langflow.field_typing import Tool
from langflow.inputs.inputs import InputTypes, MultilineInput
from langflow.io import BoolInput, HandleInput, IntInput, MessageTextInput
from langflow.logging import logger
from langflow.memory import delete_message
from langflow.schema import Data
from langflow.schema.content_block import ContentBlock
from langflow.schema.message import Message
from langflow.template import Output
from langflow.utils.constants import MESSAGE_SENDER_AI

if TYPE_CHECKING:
    from langchain_core.messages import BaseMessage

    from langflow.schema.log import SendMessageFunctionType


DEFAULT_TOOLS_DESCRIPTION = "一个有用的助手，可以访问以下工具："  # "A helpful assistant with access to the following tools:"
DEFAULT_AGENT_NAME = "代理 ({tools_names})"  # "Agent ({tools_names})"


class LCAgentComponent(Component):
    trace_type = "agent"
    _base_inputs: list[InputTypes] = [
        MessageTextInput(
            name="input_value",
            display_name="输入",  # "Input"
            info="用户提供给代理处理的输入。",  # "The input provided by the user for the agent to process."
            tool_mode=True,
        ),
        BoolInput(
            name="handle_parsing_errors",
            display_name="处理解析错误",  # "Handle Parse Errors"
            value=True,
            advanced=True,
            info="代理是否应修复读取用户输入时的错误以便更好地处理？",  # "Should the Agent fix errors when reading user input for better processing?"
        ),
        BoolInput(name="verbose", display_name="详细模式", value=True, advanced=True),  # "Verbose"
        IntInput(
            name="max_iterations",
            display_name="最大迭代次数",  # "Max Iterations"
            value=15,
            advanced=True,
            info="代理在停止之前完成任务的最大尝试次数。",  # "The maximum number of attempts the agent can make to complete its task before it stops."
        ),
        MultilineInput(
            name="agent_description",
            display_name="代理描述 [已弃用]",  # "Agent Description [Deprecated]"
            info=(
                "代理的描述。这仅在工具模式下使用。"
                f"默认为 '{DEFAULT_TOOLS_DESCRIPTION}'，工具会动态添加。"
                "此功能已弃用，将在未来版本中移除。"  # "The description of the agent. This is only used when in Tool Mode. Defaults to '{DEFAULT_TOOLS_DESCRIPTION}' and tools are added dynamically. This feature is deprecated and will be removed in future versions."
            ),
            advanced=True,
            value=DEFAULT_TOOLS_DESCRIPTION,
        ),
    ]

    outputs = [
        Output(display_name="代理", name="agent", method="build_agent", hidden=True, tool_mode=False),  # "Agent"
        Output(display_name="响应", name="response", method="message_response"),  # "Response"
    ]

    @abstractmethod
    def build_agent(self) -> AgentExecutor:
        """Create the agent."""

    async def message_response(self) -> Message:
        """Run the agent and return the response."""
        agent = self.build_agent()
        message = await self.run_agent(agent=agent)

        self.status = message
        return message

    def _validate_outputs(self) -> None:
        required_output_methods = ["build_agent"]
        output_names = [output.name for output in self.outputs]
        for method_name in required_output_methods:
            if method_name not in output_names:
                msg = f"必须定义名称为 '{method_name}' 的输出。"  # "Output with name '{method_name}' must be defined."
                raise ValueError(msg)
            if not hasattr(self, method_name):
                msg = f"必须定义方法 '{method_name}'。"  # "Method '{method_name}' must be defined."
                raise ValueError(msg)

    def get_agent_kwargs(self, *, flatten: bool = False) -> dict:
        base = {
            "handle_parsing_errors": self.handle_parsing_errors,
            "verbose": self.verbose,
            "allow_dangerous_code": True,
        }
        agent_kwargs = {
            "handle_parsing_errors": self.handle_parsing_errors,
            "max_iterations": self.max_iterations,
        }
        if flatten:
            return {
                **base,
                **agent_kwargs,
            }
        return {**base, "agent_executor_kwargs": agent_kwargs}

    def get_chat_history_data(self) -> list[Data] | None:
        # might be overridden in subclasses
        return None

    def build_tool_prompt(self, tools) -> str:
        tool_descs = ""
        for tool in tools:
            tool_descs += f"工具名称：'{tool.name}'\n"
            tool_descs += f"描述：'{tool.description}'\n"
            tool_descs += f"参数：\n"
            # 处理 StructuredTool 的参数信息
            if hasattr(tool, "args_schema"):
                if isinstance(tool.args_schema, dict):
                    # 处理字典类型的 schema
                    for name, field_info in tool.args_schema["properties"].items():
                        type_desc = field_info.get("description", str(field_info.get("type", "unknown")))
                        tool_descs += f" - {name}: {type_desc}\n"
                else:
                    # 处理 Pydantic 模型类型的 schema
                    for name, field in tool.args_schema.__fields__.items():
                        # 获取参数类型描述，如果不存在则使用类型本身
                        type_desc = getattr(field, "description", None)
                        tool_descs += f" - {name}: {type_desc}\n"
            tool_descs += "\n"

        prompt = f"""
# 角色
你是一个智能助手，能够通过调用工具或自身知识解决用户问题。遵循以下规则：
# 工作流程
1. **问题分析**：理解用户问题的核心需求，判断是否需要工具辅助。
2. **工具调用**：
   - 若需工具，按格式调用，等待工具返回结果。
   - 若无可用工具，直接回答。
3. **结果整合**：将工具返回结果与用户问题结合，生成最终回答。
# 可用工具如下：
{tool_descs}
当你需要调用工具时，请直接输出如下 JSON 格式：
{{
    "tool_calls": [
        {{
            "name": "工具名称",
            "id": "工具名称",
            "args": {{
                "参数key": "参数值"
            }}
        }}
    ]
}}
注意：调用工具时不要添加任何额外的标签或符号，直接输出纯 JSON 格式。
        """
        return prompt.strip()

    async def run_agent(
        self,
        agent: Runnable | BaseSingleActionAgent | BaseMultiActionAgent | AgentExecutor,
    ) -> Message:
        
        if not (self.model_name.startswith("gpt-") or "claude" in self.model_name or "gemini" in self.model_name):
            self.system_prompt = self.build_tool_prompt(self.tools)

        if isinstance(agent, AgentExecutor):
            runnable = agent
        else:
            if not hasattr(self, "tools") or not self.tools:
                msg = "运行代理需要工具。"  # "Tools are required to run the agent."
                raise ValueError(msg)
            handle_parsing_errors = hasattr(self, "handle_parsing_errors") and self.handle_parsing_errors
            verbose = hasattr(self, "verbose") and self.verbose
            max_iterations = hasattr(self, "max_iterations") and self.max_iterations
            runnable = AgentExecutor.from_agent_and_tools(
                agent=agent,
                tools=self.tools,
                handle_parsing_errors=handle_parsing_errors,
                verbose=verbose,
                max_iterations=max_iterations,
            )
        input_dict: dict[str, str | list[BaseMessage]] = {"input": self.input_value}
        if hasattr(self, "system_prompt"):
            input_dict["system_prompt"] = self.system_prompt
        if hasattr(self, "chat_history") and self.chat_history:
            input_dict["chat_history"] = data_to_messages(self.chat_history)

        if hasattr(self, "graph"):
            session_id = self.graph.session_id
        elif hasattr(self, "_session_id"):
            session_id = self._session_id
        else:
            session_id = None

        agent_message = Message(
            sender=MESSAGE_SENDER_AI,
            sender_name=self.display_name or "代理",  # "Agent"
            properties={"icon": "Bot", "state": "partial"},
            content_blocks=[ContentBlock(title="代理步骤", contents=[])],  # "Agent Steps"
            session_id=session_id,
        )
        try:
            result = await process_agent_events(
                runnable.astream_events(
                    input_dict,
                    config={"callbacks": [AgentAsyncHandler(self.log), *self.get_langchain_callbacks()]},
                    version="v2",
                ),
                agent_message,
                cast("SendMessageFunctionType", self.send_message),
            )
        except ExceptionWithMessageError as e:
            if hasattr(e, "agent_message") and hasattr(e.agent_message, "id"):
                msg_id = e.agent_message.id
                await delete_message(id_=msg_id)
            await self._send_message_event(e.agent_message, category="remove_message")
            logger.error(f"ExceptionWithMessageError: {e}")
            raise
        except Exception as e:
            # 记录或处理其他异常
            # "Log or handle any other exceptions"
            logger.error(f"Error: {e}")
            raise

        self.status = result
        return result

    @abstractmethod
    def create_agent_runnable(self) -> Runnable:
        """Create the agent."""

    def validate_tool_names(self) -> None:
        """验证工具名称以确保它们符合所需的模式。"""
        pattern = re.compile(r"^[a-zA-Z0-9_-]+$")
        if hasattr(self, "tools") and self.tools:
            for tool in self.tools:
                if not pattern.match(tool.name):
                    msg = (
                        f"无效的工具名称 '{tool.name}'：只能包含字母、数字、下划线、短横线，"
                        "且不能包含空格。"  # "Invalid tool name '{tool.name}': must only contain letters, numbers, underscores, dashes, and cannot contain spaces."
                    )
                    raise ValueError(msg)


class LCToolsAgentComponent(LCAgentComponent):
    _base_inputs = [
        HandleInput(
            name="tools",
            display_name="工具",  # "Tools"
            input_types=["Tool"],
            is_list=True,
            required=False,
            info="这些是代理可以用来帮助完成任务的工具。",    # These are the tools that the agent can use to help with tasks.
        ),
        *LCAgentComponent._base_inputs,
    ]

    def build_agent(self) -> AgentExecutor:
        self.validate_tool_names()
        agent = self.create_agent_runnable()
        return AgentExecutor.from_agent_and_tools(
            agent=RunnableAgent(runnable=agent, input_keys_arg=["input"], return_keys_arg=["output"]),
            tools=self.tools,
            **self.get_agent_kwargs(flatten=True),
        )

    @abstractmethod
    def create_agent_runnable(self) -> Runnable:
        """Create the agent."""

    def get_tool_name(self) -> str:
        return self.display_name or "Agent"

    def get_tool_description(self) -> str:
        return self.agent_description or DEFAULT_TOOLS_DESCRIPTION

    def _build_tools_names(self):
        tools_names = ""
        if self.tools:
            tools_names = ", ".join([tool.name for tool in self.tools])
        return tools_names

    async def to_toolkit(self) -> list[Tool]:
        component_toolkit = _get_component_toolkit()
        tools_names = self._build_tools_names()
        agent_description = self.get_tool_description()
        # TODO: Agent Description Depreciated Feature to be removed
        description = f"{agent_description}{tools_names}"
        tools = component_toolkit(component=self).get_tools(
            tool_name=self.get_tool_name(), tool_description=description, callbacks=self.get_langchain_callbacks()
        )
        if hasattr(self, "tools_metadata"):
            tools = component_toolkit(component=self, metadata=self.tools_metadata).update_tools_metadata(tools=tools)
        return tools
