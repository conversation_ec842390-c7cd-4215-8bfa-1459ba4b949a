from langflow.custom import Component
from langflow.template import Output


class L<PERSON>hainComponent(Component):
    trace_type = "chain"

    outputs = [Output(display_name="文本", name="text", method="invoke_chain")]  # "Text"

    def _validate_outputs(self) -> None:
        required_output_methods = ["invoke_chain"]
        output_names = [output.name for output in self.outputs]
        for method_name in required_output_methods:
            if method_name not in output_names:
                msg = f"必须定义名称为 '{method_name}' 的输出。"  # "Output with name '{method_name}' must be defined."
                raise ValueError(msg)
            if not hasattr(self, method_name):
                msg = f"必须定义方法 '{method_name}'。"  # "Method '{method_name}' must be defined."
                raise ValueError(msg)
