from abc import abstractmethod

from langflow.custom import Component
from langflow.field_typing import BaseDocumentCompressor
from langflow.io import DataInput, IntInput, MultilineInput
from langflow.schema import Data
from langflow.schema.dataframe import DataFrame
from langflow.template.field.base import Output


class LCCompressorComponent(Component):
    inputs = [
        MultilineInput(
            name="search_query",
            display_name="搜索查询",  # "Search Query"
            tool_mode=True,
        ),
        DataInput(
            name="search_results",
            display_name="搜索结果",  # "Search Results"
            info="来自向量存储的搜索结果。",  # "Search Results from a Vector Store."
            is_list=True,
        ),
        IntInput(
            name="top_n",
            display_name="前 N 个",  # "Top N"
            value=3,
            advanced=True,
            ),
    ]

    outputs = [
        Output(
            display_name="数据",  # "Data"
            name="compressed_documents",
            method="Compressed Documents",
        ),
        Output(
            display_name="数据表",  # "DataFrame"
            name="compressed_documents_as_dataframe",
            method="Compressed Documents as DataFrame",
        ),
    ]

    @abstractmethod
    def build_compressor(self) -> BaseDocumentCompressor:
        """构建基础文档压缩器对象。"""  # "Builds the Base Document Compressor object."
        msg = "必须实现 build_compressor 方法。"  # "build_compressor method must be implemented."
        raise NotImplementedError(msg)

    async def compress_documents(self) -> list[Data]:
        """Compresses the documents retrieved from the vector store."""
        compressor = self.build_compressor()
        documents = compressor.compress_documents(
            query=self.search_query,
            documents=[passage.to_lc_document() for passage in self.search_results if isinstance(passage, Data)],
        )
        data = self.to_data(documents)
        self.status = data
        return data

    async def compress_documents_as_dataframe(self) -> DataFrame:
        """Compresses the documents retrieved from the vector store and returns a pandas DataFrame."""
        data_objs = await self.compress_documents()
        return DataFrame(data=data_objs)
