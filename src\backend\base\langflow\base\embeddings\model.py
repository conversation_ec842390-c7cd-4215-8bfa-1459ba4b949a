from langflow.custom import Component
from langflow.field_typing import Embeddings
from langflow.io import Output


class LCEmbeddingsModel(Component):
    trace_type = "embedding"

    outputs = [
        Output(display_name="嵌入向量", name="embeddings", method="build_embeddings"),  # "Embeddings"
    ]

    def _validate_outputs(self) -> None:
        required_output_methods = ["build_embeddings"]
        output_names = [output.name for output in self.outputs]
        for method_name in required_output_methods:
            if method_name not in output_names:
                msg = f"必须定义名称为 '{method_name}' 的输出。"  # "Output with name '{method_name}' must be defined."
                raise ValueError(msg)
            if not hasattr(self, method_name):
                msg = f"必须定义方法 '{method_name}'。"  # "Method '{method_name}' must be defined."
                raise ValueError(msg)

    def build_embeddings(self) -> Embeddings:
        msg = "您必须在类中实现 build_embeddings 方法。"  # "You must implement the build_embeddings method in your class."
        raise NotImplementedError(msg)
