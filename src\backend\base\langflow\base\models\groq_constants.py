GROQ_MODELS = [
    "distil-whisper-large-v3-en",  # Hugging<PERSON>ace
    "gemma2-9b-it",  # Google
    "gemma-7b-it",  # Google
    "llama3-groq-70b-8192-tool-use-preview",  # Groq
    "llama3-groq-8b-8192-tool-use-preview",  # Groq
    "llama-3.1-70b-versatile",  # Meta
    "llama-3.1-8b-instant",  # Meta
    "llama-3.2-1b-preview",  # Meta
    "llama-3.2-3b-preview",  # Meta
    "llama-3.2-11b-vision-preview",  # Meta
    "llama-3.2-90b-vision-preview",  # Meta
    "llama-3.3-70b-specdec",  # Meta
    "llama-3.3-70b-versatile",  # Meta
    "deepseek-r1-distill-llama-70b"  # DeepSeek
    "llama-guard-3-8b",  # Meta
    "llama3-70b-8192",  # Meta
    "llama3-8b-8192",  # Meta
    "mixtral-8x7b-32768",  # Mistral
    "whisper-large-v3",  # OpenA<PERSON>
    "whisper-large-v3-turbo",  # OpenAI
]
MODEL_NAMES = GROQ_MODELS  # reverse compatibility
