from langflow.schema.table import EditMode

TOOL_OUTPUT_NAME = "component_as_tool"
TOOL_OUTPUT_DISPLAY_NAME = "工具集"  # "Toolset"
TOOLS_METADATA_INPUT_NAME = "tools_metadata"
TOOL_TABLE_SCHEMA = [
    {
        "name": "name",
        "display_name": "工具名称",  # "Tool Name"
        "type": "str",
        "description": "指定工具的名称。",  # "Specify the name of the tool."
        "sortable": False,
        "filterable": False,
        "edit_mode": EditMode.INLINE,
        "hidden": False,
    },
    {
        "name": "description",
        "display_name": "工具描述",  # "Tool Description"
        "type": "str",
        "description": "描述工具的用途。",  # "Describe the purpose of the tool."
        "sortable": False,
        "filterable": False,
        "edit_mode": EditMode.POPOVER,
        "hidden": False,
    },
    {
        "name": "tags",
        "display_name": "工具标识符",  # "Tool Identifiers"
        "type": "str",
        "description": "工具的默认标识符，无法更改。",  # "The default identifiers for the tools and cannot be changed."
        "disable_edit": True,
        "sortable": False,
        "filterable": False,
        "edit_mode": EditMode.INLINE,
        "hidden": True,
    },
    {
        "name": "status",
        "display_name": "启用",  # "Enable"
        "type": "boolean",
        "description": "指示工具当前是否处于激活状态。设置为 True 以激活此工具。",  # "Indicates whether the tool is currently active. Set to True to activate this tool."
        "default": True,
    },
]

TOOLS_METADATA_INFO = "修改工具名称和描述，以帮助代理了解何时使用每个工具。"  # "Modify tool names and descriptions to help agents understand when to use each tool."

TOOL_UPDATE_CONSTANTS = ["tool_mode", "tool_actions", TOOLS_METADATA_INPUT_NAME, "flow_name_selected"]
