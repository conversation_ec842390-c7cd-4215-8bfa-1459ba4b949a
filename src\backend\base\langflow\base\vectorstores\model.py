from abc import abstractmethod
from functools import wraps
from typing import TYPE_CHECKING, Any

from langflow.custom import Component
from langflow.field_typing import Text, VectorStore
from langflow.helpers.data import docs_to_data
from langflow.inputs.inputs import BoolInput
from langflow.io import HandleInput, MultilineInput, Output
from langflow.schema import Data, DataFrame

if TYPE_CHECKING:
    from langchain_core.documents import Document


def check_cached_vector_store(f):
    """检查是否存在缓存的向量存储，如果存在则返回它们的装饰器。"""  # "Decorator to check for cached vector stores, and returns them if they exist."

    @wraps(f)
    def check_cached(self, *args, **kwargs):
        should_cache = getattr(self, "should_cache_vector_store", True)

        if should_cache and self._cached_vector_store is not None:
            return self._cached_vector_store

        result = f(self, *args, **kwargs)
        self._cached_vector_store = result
        return result

    check_cached.is_cached_vector_store_checked = True
    return check_cached


class LCVectorStoreComponent(Component):
    # 用于确保每次运行 Flow 时只构建一个向量存储
    # "Used to ensure a single vector store is built for each run of the flow"
    _cached_vector_store: VectorStore | None = None

    def __init_subclass__(cls, **kwargs):
        """强制所有子类在 build_vector_store 方法上使用检查缓存的装饰器。"""  # "Enforces the check cached decorator on all subclasses."
        super().__init_subclass__(**kwargs)
        if hasattr(cls, "build_vector_store"):
            method = cls.build_vector_store
            if not hasattr(method, "is_cached_vector_store_checked"):
                msg = (
                    f"类 {cls.__name__} 中的方法 'build_vector_store' 必须使用 @check_cached_vector_store 装饰器。"  # "The method 'build_vector_store' in class {cls.__name__} must be decorated with @check_cached_vector_store"
                )
                raise TypeError(msg)

    trace_type = "retriever"

    inputs = [
        HandleInput(
            name="ingest_data",
            display_name="导入数据",  # "Ingest Data"
            input_types=["Data", "DataFrame"],
            is_list=True,
        ),
        MultilineInput(
            name="search_query",
            display_name="搜索查询",  # "Search Query"
            tool_mode=True,
        ),
        BoolInput(
            name="should_cache_vector_store",
            display_name="缓存向量存储",  # "Cache Vector Store"
            value=True,
            advanced=True,
            info="如果为 True，则向量存储将在组件的当前构建中被缓存。"
            "这对于具有多个输出方法并希望共享相同向量存储的组件非常有用。",  # "If True, the vector store will be cached for the current build of the component. This is useful for components that have multiple output methods and want to share the same vector store."
        ),
    ]

    outputs = [
        Output(
            display_name="搜索结果",  # "Search Results"
            name="search_results",
            method="search_documents",
        ),
        Output(display_name="数据框", name="dataframe", method="as_dataframe"),  # "DataFrame"
    ]

    def _validate_outputs(self) -> None:
        # 至少需要定义以下三个输出
        # "At least these three outputs must be defined"
        required_output_methods = [
            "search_documents",
            "build_vector_store",
        ]
        output_names = [output.name for output in self.outputs]
        for method_name in required_output_methods:
            if method_name not in output_names:
                msg = f"必须定义名称为 '{method_name}' 的输出。"  # "Output with name '{method_name}' must be defined."
                raise ValueError(msg)
            if not hasattr(self, method_name):
                msg = f"必须定义方法 '{method_name}'。"  # "Method '{method_name}' must be defined."
                raise ValueError(msg)

    def _prepare_ingest_data(self) -> list[Any]:
        """通过将 DataFrame 转换为 Data（如果需要）来准备导入数据。"""  # "Prepares ingest_data by converting DataFrame to Data if needed."
        ingest_data: list | Data | DataFrame = self.ingest_data
        if not ingest_data:
            return []

        if not isinstance(ingest_data, list):
            ingest_data = [ingest_data]

        result = []

        for _input in ingest_data:
            if isinstance(_input, DataFrame):
                result.extend(_input.to_data_list())
            else:
                result.append(_input)
        return result

    def search_with_vector_store(
        self,
        input_value: Text,
        search_type: str,
        vector_store: VectorStore,
        k=10,
        **kwargs,
    ) -> list[Data]:
        """根据输入值和搜索类型在向量存储中搜索数据。"""  # "Search for data in the vector store based on the input value and search type."
        docs: list[Document] = []
        if input_value and isinstance(input_value, str) and hasattr(vector_store, "search"):
            docs = vector_store.search(query=input_value, search_type=search_type.lower(), k=k, **kwargs)
        else:
            msg = "提供的输入无效。"  # "Invalid inputs provided."
            raise ValueError(msg)
        data = docs_to_data(docs)
        self.status = data
        return data

    def search_documents(self) -> list[Data]:
        """在向量存储中搜索文档。"""  # "Search for documents in the vector store."
        if self._cached_vector_store is not None:
            vector_store = self._cached_vector_store
        else:
            vector_store = self.build_vector_store()
            self._cached_vector_store = vector_store

        search_query: str = self.search_query
        if not search_query:
            self.status = ""
            return []

        self.log(f"搜索输入：{search_query}")  # "Search input: {search_query}"
        self.log(f"搜索类型：{self.search_type}")  # "Search type: {self.search_type}"
        self.log(f"结果数量：{self.number_of_results}")  # "Number of results: {self.number_of_results}"

        search_results = self.search_with_vector_store(
            search_query, self.search_type, vector_store, k=self.number_of_results
        )
        self.status = search_results
        return search_results

    def as_dataframe(self) -> DataFrame:
        return DataFrame(self.search_documents())

    def get_retriever_kwargs(self):
        """获取检索器的关键字参数。实现可以覆盖此方法以提供自定义检索器参数。"""  # "Get the retriever kwargs. Implementations can override this method to provide custom retriever kwargs."
        return {}

    @abstractmethod
    @check_cached_vector_store
    def build_vector_store(self) -> VectorStore:
        """构建向量存储对象。"""  # "Builds the Vector Store object."
        msg = "必须实现 build_vector_store 方法。"  # "build_vector_store method must be implemented."
        raise NotImplementedError(msg)
