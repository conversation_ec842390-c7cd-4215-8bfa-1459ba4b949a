from crewai import Agent

from langflow.base.agents.crewai.crew import convert_llm, convert_tools
from langflow.custom import Component
from langflow.io import BoolInput, DictInput, HandleInput, MultilineInput, Output


class CrewAIAgentComponent(Component):
    """Component for creating a CrewAI agent.

    This component allows you to create a CrewAI agent with the specified role, goal, backstory, tools,
    and language model.

    Args:
        Component (Component): Base class for all components.

    Returns:
        Agent: CrewAI agent.
    """

    display_name = "CrewAI 代理"
    description = "表示一个 CrewAI 的代理。"  # "Represents an agent of CrewAI."
    documentation: str = "https://docs.crewai.com/how-to/LLM-Connections/"
    icon = "CrewAI"

    inputs = [
        MultilineInput(
            name="role",
            display_name="角色",  # "Role"
            info="代理的角色。",  # "The role of the agent."
        ),
        MultilineInput(
            name="goal",
            display_name="目标",  # "Goal"
            info="代理的目标。",  # "The objective of the agent."
        ),
        MultilineInput(
            name="backstory",
            display_name="背景故事",  # "Backstory"
            info="代理的背景故事。",  # "The backstory of the agent."
        ),
        HandleInput(
            name="tools",
            display_name="工具",  # "Tools"
            input_types=["Tool"],
            is_list=True,
            info="代理可用的工具。",  # "Tools at agents disposal."
            value=[],
        ),
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            info="运行代理的语言模型。",  # "Language model that will run the agent."
            input_types=["LanguageModel"],
        ),
        BoolInput(
            name="memory",
            display_name="记忆",  # "Memory"
            info="代理是否应该具有记忆功能。",  # "Whether the agent should have memory or not."
            advanced=True,
            value=True,
        ),
        BoolInput(
            name="verbose",
            display_name="详细模式",  # "Verbose"
            advanced=True,
            value=False,
        ),
        BoolInput(
            name="allow_delegation",
            display_name="允许委派",  # "Allow Delegation"
            info="代理是否被允许将任务委派给其他代理。",  # "Whether the agent is allowed to delegate tasks to other agents."
            value=True,
        ),
        BoolInput(
            name="allow_code_execution",
            display_name="允许代码执行",  # "Allow Code Execution"
            info="代理是否被允许执行代码。",  # "Whether the agent is allowed to execute code."
            value=False,
            advanced=True,
        ),
        DictInput(
            name="kwargs",
            display_name="额外参数",  # "kwargs"
            info="代理的额外参数。",  # "kwargs of agent."
            is_list=True,
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="代理", name="output", method="build_output"),  # "Agent"
    ]

    def build_output(self) -> Agent:
        kwargs = self.kwargs or {}

        # 定义代理  # "Define the Agent"
        agent = Agent(
            role=self.role,
            goal=self.goal,
            backstory=self.backstory,
            llm=convert_llm(self.llm),
            verbose=self.verbose,
            memory=self.memory,
            tools=convert_tools(self.tools),
            allow_delegation=self.allow_delegation,
            allow_code_execution=self.allow_code_execution,
            **kwargs,
        )

        self.status = repr(agent)

        return agent
