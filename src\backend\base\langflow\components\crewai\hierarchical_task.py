from langflow.base.agents.crewai.tasks import HierarchicalTask
from langflow.custom import Component
from langflow.io import HandleInput, MultilineInput, Output


class HierarchicalTaskComponent(Component):
    display_name: str = "分层任务"  # "Hierarchical Task"
    description: str = "每个任务必须有一个描述、一个预期输出以及一个负责执行的代理。"  # "Each task must have a description, an expected output and an agent responsible for execution."
    icon = "CrewAI"
    inputs = [
        MultilineInput(
            name="task_description",
            display_name="描述",  # "Description"
            info="详细说明任务目的和执行的描述性文本。",  # "Descriptive text detailing task's purpose and execution."
        ),
        MultilineInput(
            name="expected_output",
            display_name="预期输出",  # "Expected Output"
            info="明确定义任务的预期结果。",  # "Clear definition of expected task outcome."
        ),
        HandleInput(
            name="tools",
            display_name="工具",  # "Tools"
            input_types=["Tool"],
            is_list=True,
            info="任务执行所限的工具/资源列表。默认使用代理工具。",  # "List of tools/resources limited for task execution. Uses the Agent tools by default."
            required=False,
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="任务", name="task_output", method="build_task"),  # "Task"
    ]

    def build_task(self) -> HierarchicalTask:
        task = HierarchicalTask(
            description=self.task_description,
            expected_output=self.expected_output,
            tools=self.tools or [],
        )
        self.status = task
        return task
