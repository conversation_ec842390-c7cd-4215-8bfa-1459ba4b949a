from langflow.base.agents.crewai.tasks import SequentialTask
from langflow.custom import Component
from langflow.io import BoolInput, HandleInput, MultilineInput, Output


class SequentialTaskComponent(Component):
    display_name: str = "顺序任务"  # "Sequential Task"
    description: str = "每个任务必须有一个描述、一个预期输出以及一个负责执行的代理。"  # "Each task must have a description, an expected output and an agent responsible for execution."
    icon = "CrewAI"
    inputs = [
        MultilineInput(
            name="task_description",
            display_name="描述",  # "Description"
            info="详细说明任务目的和执行的描述性文本。",  # "Descriptive text detailing task's purpose and execution."
        ),
        MultilineInput(
            name="expected_output",
            display_name="预期输出",  # "Expected Output"
            info="明确定义任务的预期结果。",  # "Clear definition of expected task outcome."
        ),
        HandleInput(
            name="tools",
            display_name="工具",  # "Tools"
            input_types=["Tool"],
            is_list=True,
            info="任务执行所限的工具/资源列表。默认使用代理工具。",  # "List of tools/resources limited for task execution. Uses the Agent tools by default."
            required=False,
            advanced=True,
        ),
        HandleInput(
            name="agent",
            display_name="代理",  # "Agent"
            input_types=["Agent"],
            info="将执行任务的 CrewAI 代理。",  # "CrewAI Agent that will perform the task."
            required=True,
        ),
        HandleInput(
            name="task",
            display_name="任务",  # "Task"
            input_types=["SequentialTask"],
            info="将执行任务的 CrewAI 任务。",  # "CrewAI Task that will perform the task."
        ),
        BoolInput(
            name="async_execution",
            display_name="异步执行",  # "Async Execution"
            value=True,
            advanced=True,
            info="指示任务是否异步执行的布尔标志。",  # "Boolean flag indicating asynchronous task execution."
        ),
    ]

    outputs = [
        Output(display_name="任务", name="task_output", method="build_task"),  # "Task"
    ]

    def build_task(self) -> list[SequentialTask]:
        tasks: list[SequentialTask] = []
        task = SequentialTask(
            description=self.task_description,
            expected_output=self.expected_output,
            tools=self.agent.tools,
            async_execution=False,
            agent=self.agent,
        )
        tasks.append(task)
        self.status = task
        if self.task:
            if isinstance(self.task, list) and all(isinstance(task, SequentialTask) for task in self.task):
                tasks = self.task + tasks
            elif isinstance(self.task, SequentialTask):
                tasks = [self.task, *tasks]
        return tasks
