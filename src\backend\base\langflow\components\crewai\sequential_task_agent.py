from crewai import Agent, Task

from langflow.base.agents.crewai.tasks import SequentialTask
from langflow.custom import Component
from langflow.io import BoolInput, DictInput, HandleInput, MultilineInput, Output


class SequentialTaskAgentComponent(Component):
    display_name = "顺序任务代理"  # "Sequential Task Agent"
    description = "创建一个 CrewAI 任务及其关联的代理。"  # "Creates a CrewAI Task and its associated Agent."
    documentation = "https://docs.crewai.com/how-to/LLM-Connections/"
    icon = "CrewAI"

    inputs = [
        # Agent inputs
        MultilineInput(name="role", display_name="角色", info="代理的角色。"),  # "Role" -> "The role of the agent."
        MultilineInput(name="goal", display_name="目标", info="代理的目标。"),  # "Goal" -> "The objective of the agent."
        MultilineInput(
            name="backstory",
            display_name="背景故事",  # "Backstory"
            info="代理的背景故事。",  # "The backstory of the agent."
        ),
        HandleInput(
            name="tools",
            display_name="工具",  # "Tools"
            input_types=["Tool"],
            is_list=True,
            info="代理可用的工具。",  # "Tools at agent's disposal."
            value=[],
        ),
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            info="运行代理的语言模型。",  # "Language model that will run the agent."
            input_types=["LanguageModel"],
        ),
        BoolInput(
            name="memory",
            display_name="记忆",  # "Memory"
            info="代理是否应该具有记忆功能。",  # "Whether the agent should have memory or not."
            advanced=True,
            value=True,
        ),
        BoolInput(
            name="verbose",
            display_name="详细模式",  # "Verbose"
            advanced=True,
            value=True,
        ),
        BoolInput(
            name="allow_delegation",
            display_name="允许委派",  # "Allow Delegation"
            info="代理是否被允许将任务委派给其他代理。",  # "Whether the agent is allowed to delegate tasks to other agents."
            value=False,
            advanced=True,
        ),
        BoolInput(
            name="allow_code_execution",
            display_name="允许代码执行",  # "Allow Code Execution"
            info="代理是否被允许执行代码。",  # "Whether the agent is allowed to execute code."
            value=False,
            advanced=True,
        ),
        DictInput(
            name="agent_kwargs",
            display_name="代理额外参数",  # "Agent kwargs"
            info="代理的额外参数。",  # "Additional kwargs for the agent."
            is_list=True,
            advanced=True,
        ),
        # Task inputs
        MultilineInput(
            name="task_description",
            display_name="任务描述",  # "Task Description"
            info="详细说明任务目的和执行的描述性文本。",  # "Descriptive text detailing task's purpose and execution."
        ),
        MultilineInput(
            name="expected_output",
            display_name="预期任务输出",  # "Expected Task Output"
            info="明确定义任务的预期结果。",  # "Clear definition of expected task outcome."
        ),
        BoolInput(
            name="async_execution",
            display_name="异步执行",  # "Async Execution"
            value=False,
            advanced=True,
            info="指示任务是否异步执行的布尔标志。",  # "Boolean flag indicating asynchronous task execution."
        ),
        # Chaining input
        HandleInput(
            name="previous_task",
            display_name="前置任务",  # "Previous Task"
            input_types=["SequentialTask"],
            info="任务序列中的前置任务（用于链式调用）。",  # "The previous task in the sequence (for chaining)."
            required=False,
        ),
    ]

    outputs = [
        Output(
            display_name="顺序任务",  # "Sequential Task"
            name="task_output",
            method="build_agent_and_task",
        ),
    ]

    def build_agent_and_task(self) -> list[SequentialTask]:
        # 构建代理  # "Build the agent"
        agent_kwargs = self.agent_kwargs or {}
        agent = Agent(
            role=self.role,
            goal=self.goal,
            backstory=self.backstory,
            llm=self.llm,
            verbose=self.verbose,
            memory=self.memory,
            tools=self.tools or [],
            allow_delegation=self.allow_delegation,
            allow_code_execution=self.allow_code_execution,
            **agent_kwargs,
        )

        # 构建任务  # "Build the task"
        task = Task(
            description=self.task_description,
            expected_output=self.expected_output,
            agent=agent,
            async_execution=self.async_execution,
        )

        # 如果有前置任务，则创建任务列表  # "If there's a previous task, create a list of tasks"
        if self.previous_task:
            tasks = [*self.previous_task, task] if isinstance(self.previous_task, list) else [self.previous_task, task]
        else:
            tasks = [task]

        self.status = f"Agent: {agent!r}\nTask: {task!r}"
        return tasks
