# from langflow.field_typing import Data
from langflow.custom import Component
from langflow.io import MessageTextInput, Output
from langflow.schema import Data


class CustomComponent(Component):
    display_name = "自定义组件"  # "Custom Component"
    description = "用作创建自定义组件的模板。"  # "Use as a template to create your own component."
    documentation: str = "https://docs.langflow.org/components-custom-components"
    icon = "code"
    name = "CustomComponent"

    inputs = [
        MessageTextInput(
            name="input_value",
            display_name="输入值",  # "Input Value"
            info="这是一个自定义组件输入。",  # "This is a custom component Input."
            value="你好，世界！",  # "Hello, World!"
            tool_mode=True,
        ),
    ]

    outputs = [
        Output(display_name="输出", name="output", method="build_output"),  # "Output"
    ]

    def build_output(self) -> Data:
        data = Data(value=self.input_value)
        self.status = data
        return data
