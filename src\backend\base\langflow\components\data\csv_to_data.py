import csv
import io
from pathlib import Path

from langflow.custom import Component
from langflow.io import FileInput, MessageTextInput, MultilineInput, Output
from langflow.schema import Data


class CSVToDataComponent(Component):
    display_name = "加载 CSV"  # "Load CSV"
    description = "加载 CSV 文件、文件路径中的 CSV 或有效的 CSV 字符串，并将其转换为 Data 列表。"  # "Load a CSV file, CSV from a file path, or a valid CSV string and convert it to a list of Data."
    icon = "file-spreadsheet"
    name = "CSVtoData"
    legacy = True

    inputs = [
        FileInput(
            name="csv_file",
            display_name="CSV 文件",  # "CSV File"
            file_types=["csv"],
            info="上传一个 CSV 文件以转换为 Data 对象列表。",  # "Upload a CSV file to convert to a list of Data objects."
        ),
        MessageTextInput(
            name="csv_path",
            display_name="CSV 文件路径",  # "CSV File Path"
            info="提供 CSV 文件的路径作为纯文本。",  # "Provide the path to the CSV file as pure text."
        ),
        MultilineInput(
            name="csv_string",
            display_name="CSV 字符串",  # "CSV String"
            info="直接粘贴一个 CSV 字符串以转换为 Data 对象列表。",  # "Paste a CSV string directly to convert to a list of Data objects."
        ),
        MessageTextInput(
            name="text_key",
            display_name="文本键",  # "Text Key"
            info="用于文本列的键。默认为 'text'。",  # "The key to use for the text column. Defaults to 'text'."
            value="text",
        ),
    ]

    outputs = [
        Output(name="data_list", display_name="数据列表", method="load_csv_to_data"),  # "Data List"
    ]

    def load_csv_to_data(self) -> list[Data]:
        if sum(bool(field) for field in [self.csv_file, self.csv_path, self.csv_string]) != 1:
            msg = "请仅提供以下之一：CSV 文件、文件路径或 CSV 字符串。"  # "Please provide exactly one of: CSV file, file path, or CSV string."
            raise ValueError(msg)

        csv_data = None
        try:
            if self.csv_file:
                resolved_path = self.resolve_path(self.csv_file)
                file_path = Path(resolved_path)
                if file_path.suffix.lower() != ".csv":
                    self.status = "提供的文件必须是 CSV 文件。"  # "The provided file must be a CSV file."
                else:
                    with file_path.open(newline="", encoding="utf-8") as csvfile:
                        csv_data = csvfile.read()

            elif self.csv_path:
                file_path = Path(self.csv_path)
                if file_path.suffix.lower() != ".csv":
                    self.status = "提供的文件必须是 CSV 文件。"  # "The provided file must be a CSV file."
                else:
                    with file_path.open(newline="", encoding="utf-8") as csvfile:
                        csv_data = csvfile.read()

            else:
                csv_data = self.csv_string

            if csv_data:
                csv_reader = csv.DictReader(io.StringIO(csv_data))
                result = [Data(data=row, text_key=self.text_key) for row in csv_reader]

                if not result:
                    self.status = "CSV 数据为空。"  # "The CSV data is empty."
                    return []

                self.status = result
                return result

        except csv.Error as e:
            error_message = f"CSV 解析错误：{e}"  # "CSV parsing error: {e}"
            self.status = error_message
            raise ValueError(error_message) from e

        except Exception as e:
            error_message = f"发生错误：{e}"  # "An error occurred: {e}"
            self.status = error_message
            raise ValueError(error_message) from e

        # An error occurred
        raise ValueError(self.status)
