from langflow.base.data.utils import TEXT_FILE_TYPES, parallel_load_data, parse_text_file_to_data, retrieve_file_paths
from langflow.custom import Component
from langflow.io import BoolInput, IntInput, MessageTextInput, MultiselectInput
from langflow.schema import Data
from langflow.schema.dataframe import DataFrame
from langflow.template import Output


class DirectoryComponent(Component):
    display_name = "目录"  # "Directory"
    description = "递归加载目录中的文件。"  # "Recursively load files from a directory."
    icon = "folder"
    name = "Directory"

    inputs = [
        MessageTextInput(
            name="path",
            display_name="路径",  # "Path"
            info="要加载文件的目录路径。默认为当前目录 ('.')",  # "Path to the directory to load files from. Defaults to current directory ('.')"
            value=".",
            tool_mode=True,
        ),
        MultiselectInput(
            name="types",
            display_name="文件类型",  # "File Types"
            info="要加载的文件类型。选择一个或多个类型，或留空以加载所有支持的类型。",  # "File types to load. Select one or more types or leave empty to load all supported types."
            options=TEXT_FILE_TYPES,
            value=[],
        ),
        IntInput(
            name="depth",
            display_name="深度",  # "Depth"
            info="搜索文件的深度。",  # "Depth to search for files."
            value=0,
        ),
        IntInput(
            name="max_concurrency",
            display_name="最大并发数",  # "Max Concurrency"
            advanced=True,
            info="加载文件的最大并发数。",  # "Maximum concurrency for loading files."
            value=2,
        ),
        BoolInput(
            name="load_hidden",
            display_name="加载隐藏文件",  # "Load Hidden"
            advanced=True,
            info="如果为 true，将加载隐藏文件。",  # "If true, hidden files will be loaded."
        ),
        BoolInput(
            name="recursive",
            display_name="递归加载",  # "Recursive"
            advanced=True,
            info="如果为 true，将进行递归搜索。",  # "If true, the search will be recursive."
        ),
        BoolInput(
            name="silent_errors",
            display_name="忽略错误",  # "Silent Errors"
            advanced=True,
            info="如果为 true，错误将不会引发异常。",  # "If true, errors will not raise an exception."
        ),
        BoolInput(
            name="use_multithreading",
            display_name="使用多线程",  # "Use Multithreading"
            advanced=True,
            info="如果为 true，将使用多线程。",  # "If true, multithreading will be used."
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="load_directory"),  # "Data"
        Output(display_name="数据表", name="dataframe", method="as_dataframe"),  # "DataFrame"
    ]

    def load_directory(self) -> list[Data]:
        path = self.path
        types = self.types
        depth = self.depth
        max_concurrency = self.max_concurrency
        load_hidden = self.load_hidden
        recursive = self.recursive
        silent_errors = self.silent_errors
        use_multithreading = self.use_multithreading

        resolved_path = self.resolve_path(path)

        # If no types are specified, use all supported types
        if not types:
            types = TEXT_FILE_TYPES

        # Check if all specified types are valid
        invalid_types = [t for t in types if t not in TEXT_FILE_TYPES]
        if invalid_types:
            msg = f"指定的文件类型无效：{invalid_types}。有效类型为：{TEXT_FILE_TYPES}"  # "Invalid file types specified: {invalid_types}. Valid types are: {TEXT_FILE_TYPES}"
            raise ValueError(msg)

        valid_types = types

        file_paths = retrieve_file_paths(
            resolved_path, load_hidden=load_hidden, recursive=recursive, depth=depth, types=valid_types
        )

        loaded_data = []
        if use_multithreading:
            loaded_data = parallel_load_data(file_paths, silent_errors=silent_errors, max_concurrency=max_concurrency)
        else:
            loaded_data = [parse_text_file_to_data(file_path, silent_errors=silent_errors) for file_path in file_paths]

        valid_data = [x for x in loaded_data if x is not None and isinstance(x, Data)]
        self.status = valid_data
        return valid_data

    def as_dataframe(self) -> DataFrame:
        return DataFrame(self.load_directory())
