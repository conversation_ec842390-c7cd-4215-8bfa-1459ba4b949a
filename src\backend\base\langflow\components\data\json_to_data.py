import json
from pathlib import Path

from json_repair import repair_json

from langflow.custom import Component
from langflow.io import FileInput, MessageTextInput, MultilineInput, Output
from langflow.schema import Data


class JSONToDataComponent(Component):
    display_name = "加载 JSON"  # "Load JSON"
    description = (
        "将 JSON 文件、文件路径中的 JSON 或 JSON 字符串转换为 Data 对象或 Data 对象列表。"  # "Convert a JSON file, JSON from a file path, or a JSON string to a Data object or a list of Data objects."
    )
    icon = "braces"
    name = "JSONtoData"
    legacy = True

    inputs = [
        FileInput(
            name="json_file",
            display_name="JSON 文件",  # "JSON File"
            file_types=["json"],
            info="上传一个 JSON 文件以转换为 Data 对象或 Data 对象列表。",  # "Upload a JSON file to convert to a Data object or list of Data objects."
        ),
        MessageTextInput(
            name="json_path",
            display_name="JSON 文件路径",  # "JSON File Path"
            info="提供 JSON 文件的路径作为纯文本。",  # "Provide the path to the JSON file as pure text."
        ),
        MultilineInput(
            name="json_string",
            display_name="JSON 字符串",  # "JSON String"
            info="输入一个有效的 JSON 字符串（对象或数组）以转换为 Data 对象或 Data 对象列表。",  # "Enter a valid JSON string (object or array) to convert to a Data object or list of Data objects."
        ),
    ]

    outputs = [
        Output(name="data", display_name="数据", method="convert_json_to_data"),  # "Data"
    ]

    def convert_json_to_data(self) -> Data | list[Data]:
        if sum(bool(field) for field in [self.json_file, self.json_path, self.json_string]) != 1:
            msg = "请仅提供以下之一：JSON 文件、文件路径或 JSON 字符串。"  # "Please provide exactly one of: JSON file, file path, or JSON string."
            self.status = msg
            raise ValueError(msg)

        json_data = None

        try:
            if self.json_file:
                resolved_path = self.resolve_path(self.json_file)
                file_path = Path(resolved_path)
                if file_path.suffix.lower() != ".json":
                    self.status = "提供的文件必须是 JSON 文件。"  # "The provided file must be a JSON file."
                else:
                    json_data = file_path.read_text(encoding="utf-8")

            elif self.json_path:
                file_path = Path(self.json_path)
                if file_path.suffix.lower() != ".json":
                    self.status = "提供的文件必须是 JSON 文件。"  # "The provided file must be a JSON file."
                else:             
                    json_data = file_path.read_text(encoding="utf-8")

            else:
                json_data = self.json_string

            if json_data:
                # Try to parse the JSON string
                try:
                    parsed_data = json.loads(json_data)
                except json.JSONDecodeError:
                    # If JSON parsing fails, try to repair the JSON string
                    repaired_json_string = repair_json(json_data)
                    parsed_data = json.loads(repaired_json_string)

                # Check if the parsed data is a list
                if isinstance(parsed_data, list):
                    result = [Data(data=item) for item in parsed_data]
                else:
                    result = Data(data=parsed_data)
                self.status = result
                return result

        except (json.JSONDecodeError, SyntaxError, ValueError) as e:
            error_message = f"无效的 JSON 或 Python 字面量：{e}"  # "Invalid JSON or Python literal: {e}"
            self.status = error_message
            raise ValueError(error_message) from e

        except Exception as e:
            error_message = f"发生错误：{e}"  # "An error occurred: {e}"
            self.status = error_message
            raise ValueError(error_message) from e

        # An error occurred
        raise ValueError(self.status)
