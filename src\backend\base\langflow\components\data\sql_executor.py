from langchain_community.tools.sql_database.tool import QuerySQLDataBaseTool
from langchain_community.utilities import SQLDatabase

from langflow.custom import CustomComponent
from langflow.field_typing import Text
from langflow.io import (
    Output,
)


class SQLExecutorComponent(CustomComponent):
    display_name = "SQL 查询"  # "SQL Query"
    description = "执行 SQL 查询。"  # "Execute SQL query."
    name = "SQLExecutor"
    beta: bool = True

    def build_config(self):
        return {
            "database_url": {
                "display_name": "数据库 URL",  # "Database URL"
                "info": "数据库的 URL。",  # "The URL of the database."
            },
            "include_columns": {
                "display_name": "包含列",  # "Include Columns"
                "info": "在结果中包含列。",  # "Include columns in the result."
            },
            "passthrough": {
                "display_name": "透传模式",  # "Passthrough"
                "info": "如果发生错误，返回查询而不是抛出异常。",  # "If an error occurs, return the query instead of raising an exception."
            },
            "add_error": {
                "display_name": "添加错误信息",  # "Add Error"
                "info": "将错误添加到结果中。",  # "Add the error to the result."
            },
            "query": {
                "display_name": "查询",  # "Query"
                "info": "要执行的 SQL 查询。",  # "The SQL query to execute."
            },
        }
    
    outputs = [
        Output(display_name="文本", name="text", types=["Text"], selected="Text"),  # "Text"
    ]

    def clean_up_uri(self, uri: str) -> str:
        if uri.startswith("postgresql://"):
            uri = uri.replace("postgresql://", "postgres://")
        return uri.strip()

    def build(
        self,
        query: str,
        database_url: str,
        *,
        include_columns: bool = False,
        passthrough: bool = False,
        add_error: bool = False,
        **kwargs,
    ) -> Text:
        _ = kwargs
        error = None
        try:
            database = SQLDatabase.from_uri(database_url)
        except Exception as e:
            msg = f"连接到数据库时发生错误：{e}"  # "An error occurred while connecting to the database: {e}"
            raise ValueError(msg) from e
        try:
            tool = QuerySQLDataBaseTool(db=database)
            result = tool.run(query, include_columns=include_columns)
            self.status = result
        except Exception as e:
            result = str(e)
            self.status = result
            if not passthrough:
                raise
            error = repr(e)

        if add_error and error is not None:
            result = f"{result}\n\n错误：{error}\n\n查询：{query}"  # "Error: {error}\n\nQuery: {query}"
        elif error is not None:
            # Then we won't add the error to the result
            # but since we are in passthrough mode, we will return the query
            result = query

        return result
