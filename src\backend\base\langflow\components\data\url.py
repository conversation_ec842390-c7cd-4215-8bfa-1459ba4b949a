import logging
import re

from bs4 import BeautifulSoup
from langchain_community.document_loaders import RecursiveUrlLoader

from langflow.custom.custom_component.component import Component
from langflow.helpers.data import data_to_text
from langflow.io import BoolInput, DropdownInput, IntInput, MessageTextInput, Output
from langflow.schema import Data
from langflow.schema.dataframe import DataFrame
from langflow.schema.message import Message

logger = logging.getLogger(__name__)


class URLComponent(Component):
    """A component that loads and parses child links from a root URL recursively."""

    display_name = "URL"  # "URL"
    description = "递归加载和解析根 URL 的子链接。"  # "Load and parse child links from a root URL recursively."
    icon = "layout-template"
    name = "URLComponent"

    inputs = [
        MessageTextInput(
            name="urls",
            display_name="URL 列表",  # "URLs"
            info="输入一个或多个 URL 以递归爬取，点击 '+' 按钮添加。",  # "Enter one or more URLs to crawl recursively, by clicking the '+' button."
            is_list=True,
            tool_mode=True,
            placeholder="输入一个 URL...",  # "Enter a URL..."
            list_add_label="添加 URL",  # "Add URL"
        ),
        IntInput(
            name="max_depth",
            display_name="最大深度",  # "Max Depth"
            info=(
"控制爬虫离初始页面的点击深度：\n"
                "- 深度 1：仅爬取初始页面\n"
                "- 深度 2：初始页面 + 所有直接链接的页面\n"
                "- 深度 3：初始页面 + 直接链接 + 直接链接页面中的链接\n"
                "注意：这是关于链接遍历的深度，而不是 URL 路径深度。"  # "Controls how many 'clicks' away from the initial page the crawler will go:\n"
# "- depth 1: only the initial page\n"
# "- depth 2: initial page + all pages linked directly from it\n"
# "- depth 3: initial page + direct links + links found on those direct link pages\n"
# "Note: This is about link traversal, not URL path depth."
            ),
            value=1,
            required=False,
        ),
        BoolInput(
            name="prevent_outside",
            display_name="限制域外链接",  # "Prevent Outside"
            info=(
"如果启用，仅爬取与根 URL 相同域名的链接。"
                "这有助于防止爬虫访问外部网站。"  # "If enabled, only crawls URLs within the same domain as the root URL. "
# "This helps prevent the crawler from going to external websites."
            ),
            value=True,
            required=False,
            advanced=True,
        ),
        BoolInput(
            name="use_async",
            display_name="使用异步加载",  # "Use Async"
            info=(
"如果启用，将使用异步加载，这可能显著加快速度，"
                "但可能会使用更多的系统资源。"  # "If enabled, uses asynchronous loading which can be significantly faster "
# "but might use more system resources."
            ),
            value=True,
            required=False,
            advanced=True,
        ),
        DropdownInput(
            name="format",
            display_name="输出格式",  # "Output Format"
            info="输出格式。选择 '文本' 提取 HTML 中的文本，或选择 'HTML' 获取原始 HTML 内容。",  # "Output Format. Use 'Text' to extract the text from the HTML or 'HTML' for the raw HTML content."
            options=["文本", "HTML"],  # ["Text", "HTML"]
            value="文本",  # "Text"
advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="fetch_content"),  # "Data"
        Output(display_name="消息", name="text", method="fetch_content_text"),  # "Message"
        Output(display_name="数据表", name="dataframe", method="as_dataframe"),  # "DataFrame"
    ]

    def validate_url(self, string: str) -> bool:
        """验证给定的字符串是否符合 URL 模式。"""  # "Validates if the given string matches URL pattern."
        url_regex = re.compile(
            r"^(https?:\/\/)?" r"(www\.)?" r"([a-zA-Z0-9.-]+)" r"(\.[a-zA-Z]{2,})?" r"(:\d+)?" r"(\/[^\s]*)?$",
            re.IGNORECASE,
        )
        return bool(url_regex.match(string))

    def ensure_url(self, url: str) -> str:
        """Ensures the given string is a valid URL."""
        if not url.startswith(("http://", "https://")):
            url = "http://" + url

        if not self.validate_url(url):
            error_msg = "无效的 URL - " + url  # "Invalid URL - "
            raise ValueError(error_msg)

        return url

    def fetch_content(self) -> list[Data]:
        """从 URL 加载文档。"""  # "Load documents from the URLs."
        all_docs = []
        data = []
        try:
            urls = list({self.ensure_url(url.strip()) for url in self.urls if url.strip()})

            no_urls_msg = "未提供有效的 URL。"  # "No valid URLs provided."
            if not urls:
                raise ValueError(no_urls_msg)

            for processed_url in urls:
                msg = f"从 {processed_url} 加载文档"  # "Loading documents from {processed_url}"
                logger.info(msg)

                extractor = (lambda x: x) if self.format == "HTML" else (lambda x: BeautifulSoup(x, "lxml").get_text())
                loader = RecursiveUrlLoader(
                    url=processed_url,
                    max_depth=self.max_depth,
                    prevent_outside=self.prevent_outside,
                    use_async=self.use_async,
                    extractor=extractor,
                )

                docs = loader.load()
                msg = f"从 {processed_url} 找到 {len(docs)} 个文档"  # "Found {len(docs)} documents from {processed_url}"
                logger.info(msg)
                all_docs.extend(docs)

            data = [Data(text=doc.page_content, **doc.metadata) for doc in all_docs]
            self.status = data

        except Exception as e:
            msg = f"加载文档时出错：{e!s}"  # "Error loading documents: {e!s}"
            logger.exception(msg)
            raise ValueError(msg) from e

        self.status = data
        return data

    def fetch_content_text(self) -> Message:
        """Load documents and return their text content."""
        data = self.fetch_content()
        result_string = data_to_text("{text}", data)
        self.status = result_string
        return Message(text=result_string)

    def as_dataframe(self) -> DataFrame:
        """Convert the documents to a DataFrame."""
        data_frame = DataFrame(self.fetch_content())
        self.status = data_frame
        return data_frame
