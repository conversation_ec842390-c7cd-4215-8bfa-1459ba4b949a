import json

from langflow.custom import Component
from langflow.io import MultilineInput, Output
from langflow.schema import Data


class WebhookComponent(Component):
    display_name = "Webhook"
    name = "Webhook"
    icon = "webhook"

    inputs = [
        MultilineInput(
            name="data",
            display_name="有效负载",  # "Payload"
            info="通过 HTTP POST 从外部系统接收有效负载。",  # "Receives a payload from external systems via HTTP POST."
            advanced=True,
        ),
        MultilineInput(
            name="curl",
            display_name="cURL",
            value="CURL_WEBHOOK",
            advanced=True,
            input_types=[],
        ),
        MultilineInput(
            name="endpoint",
            display_name="端点",  # "Endpoint"
            value="BACKEND_URL",
            advanced=False,
            copy_field=True,
            input_types=[],
        ),
    ]
    outputs = [
        Output(display_name="数据", name="output_data", method="build_data"),  # "Data"
    ]

    def build_data(self) -> Data:
        message: str | Data = ""
        if not self.data:
            self.status = "未提供数据。"  # "No data provided."
            return Data(data={})
        try:
            body = json.loads(self.data or "{}")
        except json.JSONDecodeError:
            body = {"payload": self.data}
            message = f"无效的 JSON 有效负载。请检查格式。\n\n{self.data}"  # "Invalid JSON payload. Please check the format.\n\n{self.data}"
        data = Data(data=body)
        if not message:
            message = data
        self.status = message
        return data
