from typing import Any
from langflow.custom import Component
from langflow.io import DropdownInput, Output, MultiselectInput
from langflow.inputs import MessageInput, StrInput
from langflow.schema.dataframe import DataFrame
from langflow.schema.message import Message
from langflow.schema import Data
from langflow.utils.wiseflow_auth import auth
import aiohttp
import json



class WiseAgentRAGComponent(Component):
    """
    查询WiseAgent的RAG服务。
    """

    display_name = "RAG查询(灵童RAG)"
    description = "查询灵童平台的RAG服务。"
    icon = "file-text"
    name = "WiseAgentRAG"


    inputs = [
        MessageInput(name="input_value", display_name="输入"), 
        MultiselectInput(
            name="rag",
            display_name="RAG",
            info="选择要查询的RAG知识库",
            options=[],
            value=[],
            refresh_button=True,
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="as_data"),
        Output(display_name="数据表", name="dataframe", method="as_dataframe"),
        Output(display_name="消息", name="message", method="as_message"),
    ]

    async def update_build_config(self, build_config: dict, field_value: Any, field_name: str | None = None):
        if field_name == "rag":
            if not build_config.get("agent_auth_token"):
                build_config["rag"]["options"] = []
                build_config["rag"]["options_metadata"] = []
            else:
                if not build_config.get("agent_auth_token"):
                    raise ValueError("当前组件需要使用agent平台的知识库，未探测到相关服务信息")
                    
                async with aiohttp.ClientSession() as session:
                    headers = {
                        "Authorization": f"Bearer {build_config['agent_auth_token']}",
                        "Content-Type": "application/json"
                    }
                    async with session.get("http://wiseagent:8800/api/currentUser", headers=headers) as response:
                        response = await response.json(content_type=None)
                        if response['success']:
                            user_data = response['data'];
                            uid = user_data['id']
                        else:
                            uid = None

                async with aiohttp.ClientSession() as session:
                    headers = {"Content-Type": "application/json"}
                    async with session.post("http://wiseagent:8800/api/app/get_knowledge_bases", json={"user_id": int(uid)}, headers=headers) as response:
                        res = await response.json(content_type=None)
                        if res['status'] == 'success':
                            rags = res['data']
                            build_config["rag"]["options"] = [rag["name"] for rag in rags]
                            build_config["rag"]["options_metadata"] = [
                                {"id": str(rag["_id"]),"name": str(rag["name"])} for rag in rags
                            ]
                        else:
                            build_config["rag"]["options"] = []
                            build_config["rag"]["options_metadata"] = []


    async def as_data(self) -> Data:
        """
        Make a request to the WiseAgent RAG service.

        Returns:
            Data: The response data from the WiseAgent RAG service.
        """
        
        # 获取选中的RAG ID
        rag_ids = []
        if self.rag:
            for rag_name in self.rag:
                for metadata in self._vertex.data["node"]["template"]["rag"]["options_metadata"]:
                    if metadata["name"] == rag_name:
                        rag_ids.append(metadata["id"])
                    
        if len(rag_ids) == 0:
            raise ValueError("请选择RAG知识库")
            
        async with aiohttp.ClientSession() as session:
            params = {
                "params": rag_ids,
                "client_signature": auth.generate_signature(json.dumps(rag_ids)),
                "query": self.input_value.text
            }
            headers = {"Content-Type": "application/json"}
            async with session.post("http://wiseagent:8800/api/app/getContextFromLangflow", json=params, headers=headers) as response:
                response = await response.json(content_type=None)
                if not response['merged_results']:
                    raise ValueError(f"请求失败: {response}")
                    
                # 提取知识块并格式化
                knowledge_blocks = []
                for result in response['merged_results']:
                    if "additional_kwargs" in result:
                        knowledge_blocks.append(result["additional_kwargs"])
                
                # 将知识块转换为字符串格式
                formatted_knowledge = "\n\n".join([str(block) for block in knowledge_blocks])
                
                # 创建Data对象，将格式化后的知识块存储在data字段中
                return Data(data={"text": formatted_knowledge})
        

    async def as_dataframe(self) -> DataFrame:
        """
        Make a request to the WiseAgent RAG service and return a DataFrame.
        """
        data = await self.as_data()
        return DataFrame([data])
    

    async def as_message(self) -> Message:
        """
        Make a request to the WiseAgent RAG service and return a Message.
        """
        data = await self.as_data()
        if not data:
            return Message(text="")
        # 获取文本内容
        text_content = data.get_text() if data.get_text() else ""
        # 创建消息对象
        return Message(text=text_content, data=data.data)