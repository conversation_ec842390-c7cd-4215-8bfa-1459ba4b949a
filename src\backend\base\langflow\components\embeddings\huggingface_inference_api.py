from urllib.parse import urlparse

import requests
from langchain_community.embeddings.huggingface import HuggingFaceInferenceAPIEmbeddings

# Next update: use langchain_huggingface
from pydantic import SecretStr
from tenacity import retry, stop_after_attempt, wait_fixed

from langflow.base.embeddings.model import LCEmbeddingsModel
from langflow.field_typing import Embeddings
from langflow.io import MessageTextInput, Output, SecretStrInput


class HuggingFaceInferenceAPIEmbeddingsComponent(LCEmbeddingsModel):
    display_name = "HuggingFace 嵌入推理" # "HuggingFace Embeddings Inference "
    description = "使用 HuggingFace 文本嵌入推理 (TEI) 生成嵌入。"  # "Generate embeddings using HuggingFace Text Embeddings Inference (TEI)"
    documentation = "https://huggingface.co/docs/text-embeddings-inference/index"
    icon = "HuggingFace"
    name = "HuggingFaceInferenceAPIEmbeddings"

    inputs = [
        SecretStrInput(
            name="api_key",
            display_name="API 密钥",  # "API Key"
            advanced=False,
            info="非本地推理端点所需。本地推理不需要 API 密钥。",  # "Required for non-local inference endpoints. Local inference does not require an API Key."
        ),
        MessageTextInput(
            name="inference_endpoint",
            display_name="推理端点",  # "Inference Endpoint"
            required=True,
            value="https://api-inference.huggingface.co/models/",
            info="自定义推理端点 URL。",  # "Custom inference endpoint URL."
        ),
        MessageTextInput(
            name="model_name",
            display_name="模型名称",  # "Model Name"
            value="BAAI/bge-large-en-v1.5",
            info="要用于文本嵌入的模型名称。",  # "The name of the model to use for text embeddings."
            required=True,
        ),
    ]

    outputs = [
        Output(display_name="嵌入", name="embeddings", method="build_embeddings"),  # "Embeddings"
    ]

    def validate_inference_endpoint(self, inference_endpoint: str) -> bool:
        parsed_url = urlparse(inference_endpoint)
        if not all([parsed_url.scheme, parsed_url.netloc]):
            msg = (
                f"无效的推理端点格式：'{self.inference_endpoint}'。"  # "Invalid inference endpoint format: '{self.inference_endpoint}'."
                "请确保 URL 包含方案（例如：'http://' 或 'https://'）和域名。"  # "Please ensure the URL includes both a scheme (e.g., 'http://' or 'https://') and a domain name."
                "示例：'http://localhost:8080' 或 'https://api.example.com'"  # "Example: 'http://localhost:8080' or 'https://api.example.com'"
            )
            raise ValueError(msg)

        try:
            response = requests.get(f"{inference_endpoint}/health", timeout=5)
        except requests.RequestException as e:
            msg = (
                f"推理端点 '{inference_endpoint}' 无响应。"  # "Inference endpoint '{inference_endpoint}' is not responding."
                "请确保 URL 正确且服务正在运行。"  # "Please ensure the URL is correct and the service is running."
            )
            raise ValueError(msg) from e

        if response.status_code != requests.codes.ok:
            msg = f"HuggingFace 健康检查失败：{response.status_code}"  # "HuggingFace health check failed: {response.status_code}"
            raise ValueError(msg)
        # returning True to solve linting error
        return True

    def get_api_url(self) -> str:
        if "huggingface" in self.inference_endpoint.lower():
            return f"{self.inference_endpoint}"
        return self.inference_endpoint

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    def create_huggingface_embeddings(
        self, api_key: SecretStr, api_url: str, model_name: str
    ) -> HuggingFaceInferenceAPIEmbeddings:
        return HuggingFaceInferenceAPIEmbeddings(api_key=api_key, api_url=api_url, model_name=model_name)

    def build_embeddings(self) -> Embeddings:
        api_url = self.get_api_url()

        is_local_url = (
            api_url.startswith(("http://localhost", "http://127.0.0.1", "http://0.0.0.0", "http://docker"))
            or "huggingface.co" not in api_url.lower()
        )

        if not self.api_key and is_local_url:
            self.validate_inference_endpoint(api_url)
            api_key = SecretStr("APIKeyForLocalDeployment")
        elif not self.api_key:
            msg = "非本地推理端点需要 API 密钥。"  # "API Key is required for non-local inference endpoints."
            raise ValueError(msg)
        else:
            api_key = SecretStr(self.api_key).get_secret_value()

        try:
            return self.create_huggingface_embeddings(api_key, api_url, self.model_name)
        except Exception as e:
            msg = "无法连接到 HuggingFace 推理 API。"  # "Could not connect to HuggingFace Inference API."
            raise ValueError(msg) from e
