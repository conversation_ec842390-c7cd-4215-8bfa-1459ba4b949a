from langchain_openai import OpenAIEmbeddings

from langflow.base.embeddings.model import LCEmbeddingsModel
from langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES
from langflow.field_typing import Embeddings
from langflow.io import BoolInput, DictInput, DropdownInput, FloatInput, IntInput, MessageTextInput, SecretStrInput


class OpenAIEmbeddingsComponent(LCEmbeddingsModel):
    display_name = "OpenAI嵌入"  # "OpenAI Embeddings"
    description = "使用 OpenAI 模型生成嵌入。"  # "Generate embeddings using OpenAI models."
    icon = "OpenAI"
    name = "OpenAIEmbeddings"

    inputs = [
        DictInput(
            name="default_headers",
            display_name="默认请求头",  # "Default Headers"
            advanced=True,
            info="用于 API 请求的默认请求头。",  # "Default headers to use for the API request."
        ),
        DictInput(
            name="default_query",
            display_name="默认查询参数",  # "Default Query"
            advanced=True,
            info="用于 API 请求的默认查询参数。",  # "Default query parameters to use for the API request."
        ),
        IntInput(
            name="chunk_size",
            display_name="分块大小",  # "Chunk Size"
            advanced=True,
            value=1000,
        ),
        MessageTextInput(
            name="client",
            display_name="客户端",  # "Client"
            advanced=True,
        ),
        MessageTextInput(
            name="deployment",
            display_name="部署",  # "Deployment"
            advanced=True,
        ),
        IntInput(
            name="embedding_ctx_length",
            display_name="嵌入上下文长度",  # "Embedding Context Length"
            advanced=True,
            value=1536,
        ),
        IntInput(
            name="max_retries",
            display_name="最大重试次数",  # "Max Retries"
            value=3,
            advanced=True,
        ),
        DropdownInput(
            name="model",
            display_name="模型",  # "Model"
            advanced=False,
            options=OPENAI_EMBEDDING_MODEL_NAMES,
            value="text-embedding-3-small",
        ),
        DictInput(
            name="model_kwargs",
            display_name="模型参数",  # "Model Kwargs"
            advanced=True,
        ),
        SecretStrInput(
            name="openai_api_key",
            display_name="OpenAI API 密钥",  # "OpenAI API Key"
            value="OPENAI_API_KEY",
            required=True,
        ),
        MessageTextInput(
            name="openai_api_base",
            display_name="OpenAI API 基础 URL",  # "OpenAI API Base"
            advanced=True,
        ),
        MessageTextInput(
            name="openai_api_type",
            display_name="OpenAI API 类型",  # "OpenAI API Type"
            advanced=True,
        ),
        MessageTextInput(
            name="openai_api_version",
            display_name="OpenAI API 版本",  # "OpenAI API Version"
            advanced=True,
        ),
        MessageTextInput(
            name="openai_organization",
            display_name="OpenAI 组织",  # "OpenAI Organization"
            advanced=True,
        ),
        MessageTextInput(
            name="openai_proxy",
            display_name="OpenAI 代理",  # "OpenAI Proxy"
            advanced=True,
        ),
        FloatInput(
            name="request_timeout",
            display_name="请求超时时间",  # "Request Timeout"
            advanced=True,
        ),
        BoolInput(
            name="show_progress_bar",
            display_name="显示进度条",  # "Show Progress Bar"
            advanced=True,
        ),
        BoolInput(
            name="skip_empty",
            display_name="跳过空值",  # "Skip Empty"
            advanced=True,
        ),
        MessageTextInput(
            name="tiktoken_model_name",
            display_name="TikToken 模型名称",  # "TikToken Model Name"
            advanced=True,
        ),
        BoolInput(
            name="tiktoken_enable",
            display_name="启用 TikToken",  # "TikToken Enable"
            advanced=True,
            value=True,
            info="如果为 False，则必须安装 transformers。",  # "If False, you must have transformers installed."
        ),
        IntInput(
            name="dimensions",
            display_name="维度",  # "Dimensions"
            info="生成的嵌入输出的维度数量，仅某些模型支持。",  # "The number of dimensions the resulting output embeddings should have. Only supported by certain models."
            advanced=True,
        ),
    ]

    def build_embeddings(self) -> Embeddings:
        return OpenAIEmbeddings(
            client=self.client or None,
            model=self.model,
            dimensions=self.dimensions or None,
            deployment=self.deployment or None,
            api_version=self.openai_api_version or None,
            base_url=self.openai_api_base or None,
            openai_api_type=self.openai_api_type or None,
            openai_proxy=self.openai_proxy or None,
            embedding_ctx_length=self.embedding_ctx_length,
            api_key=self.openai_api_key or None,
            organization=self.openai_organization or None,
            allowed_special="all",
            disallowed_special="all",
            chunk_size=self.chunk_size,
            max_retries=self.max_retries,
            timeout=self.request_timeout or None,
            tiktoken_enabled=self.tiktoken_enable,
            tiktoken_model_name=self.tiktoken_model_name or None,
            show_progress_bar=self.show_progress_bar,
            model_kwargs=self.model_kwargs,
            skip_empty=self.skip_empty,
            default_headers=self.default_headers or None,
            default_query=self.default_query or None,
        )
