import logging
from typing import TYPE_CHECKING

from langflow.custom import Component
from langflow.io import HandleInput, MessageInput, Output
from langflow.schema import Data

if TYPE_CHECKING:
    from langflow.field_typing import Embeddings
    from langflow.schema.message import Message


class TextEmbedderComponent(Component):
    display_name: str = "文本嵌入器"  # "Text Embedder"
    description: str = "为给定消息生成嵌入，使用指定的嵌入模型。"  # "Generate embeddings for a given message using the specified embedding model."
    icon = "binary"
    inputs = [
        HandleInput(
            name="embedding_model",
            display_name="嵌入模型",  # "Embedding Model"
            info="用于生成嵌入的嵌入模型。",  # "The embedding model to use for generating embeddings."
            input_types=["Embeddings"],
            required=True,
        ),
        MessageInput(
            name="message",
            display_name="消息",  # "Message"
            info="要为其生成嵌入的消息。",  # "The message to generate embeddings for."
            required=True,
        ),
    ]
    outputs = [
        Output(display_name="嵌入数据", name="embeddings", method="generate_embeddings"),  # "Embedding Data"
    ]

    def generate_embeddings(self) -> Data:
        try:
            embedding_model: Embeddings = self.embedding_model
            message: Message = self.message

            # Combine validation checks to reduce nesting
            if not embedding_model or not hasattr(embedding_model, "embed_documents"):
                msg = "无效或不兼容的嵌入模型"  # "Invalid or incompatible embedding model"
                raise ValueError(msg)

            text_content = message.text if message and message.text else ""
            if not text_content:
                msg = "消息中未找到文本内容"  # "No text content found in message"
                raise ValueError(msg)

            embeddings = embedding_model.embed_documents([text_content])
            if not embeddings or not isinstance(embeddings, list):
                msg = "生成的嵌入无效"  # "Invalid embeddings generated"
                raise ValueError(msg)

            embedding_vector = embeddings[0]
            self.status = {"text": text_content, "embeddings": embedding_vector}
            return Data(data={"text": text_content, "embeddings": embedding_vector})
        except Exception as e:
            logging.exception("生成嵌入时出错")  # "Error generating embeddings"
            error_data = Data(data={"text": "", "embeddings": [], "error": str(e)})
            self.status = {"error": str(e)}
            return error_data
