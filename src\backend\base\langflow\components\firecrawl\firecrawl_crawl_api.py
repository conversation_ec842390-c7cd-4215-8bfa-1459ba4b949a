import uuid

from langflow.custom import Component
from langflow.io import DataInput, IntInput, MultilineInput, Output, SecretStrInput, StrInput
from langflow.schema import Data


class FirecrawlCrawlApi(Component):
    display_name: str = "Firecrawl爬取API"  # "Firecrawl Crawl API"
    description: str = "Firecrawl 爬取 API。"  # "Firecrawl Crawl API."
    name = "FirecrawlCrawlApi"

    documentation: str = "https://docs.firecrawl.dev/v1/api-reference/endpoint/crawl-post"

    inputs = [
        SecretStrInput(
            name="api_key",
            display_name="API 密钥",  # "API Key"
            required=True,
            password=True,
            info="用于 Firecrawl API 的 API 密钥。",  # "The API key to use Firecrawl API."
        ),
        MultilineInput(
            name="url",
            display_name="URL",
            required=True,
            info="要爬取的 URL。",  # "The URL to scrape."
            tool_mode=True,
        ),
        IntInput(
            name="timeout",
            display_name="超时时间",  # "Timeout"
            info="请求的超时时间（以毫秒为单位）。",  # "Timeout in milliseconds for the request."
        ),
        StrInput(
            name="idempotency_key",
            display_name="幂等键",  # "Idempotency Key"
            info="可选的幂等键，用于确保请求唯一性。",  # "Optional idempotency key to ensure unique requests."
        ),
        DataInput(
            name="crawlerOptions",
            display_name="爬取选项",  # "Crawler Options"
            info="随请求发送的爬取选项。",  # "The crawler options to send with the request."
        ),
        DataInput(
            name="scrapeOptions",
            display_name="抓取选项",  # "Scrape Options"
            info="随请求发送的页面选项。",  # "The page options to send with the request."
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="crawl"),  # "Data"
    ]
    idempotency_key: str | None = None

    def crawl(self) -> Data:
        try:
            from firecrawl import FirecrawlApp
        except ImportError as e:
            msg = "无法导入 firecrawl 集成包。请使用 `pip install firecrawl-py` 安装。"  # "Could not import firecrawl integration package. Please install it with `pip install firecrawl-py`."
            raise ImportError(msg) from e

        params = self.crawlerOptions.__dict__["data"] if self.crawlerOptions else {}
        scrape_options_dict = self.scrapeOptions.__dict__["data"] if self.scrapeOptions else {}
        if scrape_options_dict:
            params["scrapeOptions"] = scrape_options_dict

        # 为 v1 中的新参数设置默认值
        # "Set default values for new parameters in v1"
        params.setdefault("maxDepth", 2)
        params.setdefault("limit", 10000)
        params.setdefault("allowExternalLinks", False)
        params.setdefault("allowBackwardLinks", False)
        params.setdefault("ignoreSitemap", False)
        params.setdefault("ignoreQueryParameters", False)

        # 如果未提供，确保 onlyMainContent 被显式设置
        # "Ensure onlyMainContent is explicitly set if not provided"
        if "scrapeOptions" in params:
            params["scrapeOptions"].setdefault("onlyMainContent", True)
        else:
            params["scrapeOptions"] = {"onlyMainContent": True}

        if not self.idempotency_key:
            self.idempotency_key = str(uuid.uuid4())

        app = FirecrawlApp(api_key=self.api_key)
        crawl_result = app.crawl_url(self.url, params=params, idempotency_key=self.idempotency_key)
        return Data(data={"results": crawl_result})
