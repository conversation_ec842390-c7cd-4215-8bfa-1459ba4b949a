from langflow.custom import Component
from langflow.io import (
    BoolInput,
    MultilineInput,
    Output,
    SecretStrInput,
)
from langflow.schema import Data


class FirecrawlMapApi(Component):
    display_name: str = "Firecrawl映射API"  # "Firecrawl Map API"
    description: str = "Firecrawl 映射 API。"  # "Firecrawl Map API."
    name = "FirecrawlMapApi"

    documentation: str = "https://docs.firecrawl.dev/api-reference/endpoint/map"

    inputs = [
        SecretStrInput(
            name="api_key",
            display_name="API 密钥",  # "API Key"
            required=True,
            password=True,
            info="用于 Firecrawl API 的 API 密钥。",  # "The API key to use Firecrawl API."
        ),
        MultilineInput(
            name="urls",
            display_name="URLs",
            required=True,
            info="要从中创建映射的 URL 列表（用逗号或换行符分隔）。",  # "List of URLs to create maps from (separated by commas or new lines)."
            tool_mode=True,
        ),
        BoolInput(
            name="ignore_sitemap",
            display_name="忽略站点地图",  # "Ignore Sitemap"
            info="如果为 true，则在爬取过程中将忽略 sitemap.xml 文件。",  # "When true, the sitemap.xml file will be ignored during crawling."
        ),
        BoolInput(
            name="sitemap_only",
            display_name="仅限站点地图",  # "Sitemap Only"
            info="如果为 true，则仅返回站点地图中找到的链接。",  # "When true, only links found in the sitemap will be returned."
        ),
        BoolInput(
            name="include_subdomains",
            display_name="包含子域名",  # "Include Subdomains"
            info="如果为 true，则还将扫描提供的 URL 的子域名。",  # "When true, subdomains of the provided URL will also be scanned."
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="map"),  # "Data"
    ]

    def map(self) -> Data:
        try:
            from firecrawl import FirecrawlApp
        except ImportError as e:
            msg = "无法导入 firecrawl 集成包。请使用 `pip install firecrawl-py` 安装。"  # "Could not import firecrawl integration package. Please install it with `pip install firecrawl-py`."
            raise ImportError(msg) from e

        # 验证 URLs  # "Validate URLs"
        if not self.urls:
            msg = "URLs 是必需的。"  # "URLs are required."
            raise ValueError(msg)

        # 分割并验证 URLs（同时处理逗号和换行符）  # "Split and validate URLs (handle both commas and newlines)"
        urls = [url.strip() for url in self.urls.replace("\n", ",").split(",") if url.strip()]
        if not urls:
            msg = "未提供有效的 URLs。"  # "No valid URLs provided."
            raise ValueError(msg)

        params = {
            "ignoreSitemap": self.ignore_sitemap,
            "sitemapOnly": self.sitemap_only,
            "includeSubdomains": self.include_subdomains,
        }

        app = FirecrawlApp(api_key=self.api_key)

        # 映射所有提供的 URLs 并合并结果  # "Map all provided URLs and combine results"
        combined_links = []
        for url in urls:
            result = app.map_url(url, params=params)
            if isinstance(result, dict) and "links" in result:
                combined_links.extend(result["links"])

        map_result = {"success": True, "links": combined_links}

        return Data(data=map_result)
