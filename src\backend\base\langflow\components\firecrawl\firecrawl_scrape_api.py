from langflow.custom import Component
from langflow.io import (
    DataInput,
    IntInput,
    MultilineInput,
    Output,
    SecretStrInput,
)
from langflow.schema import Data


class FirecrawlScrapeApi(Component):
    display_name: str = "Firecrawl抓取API"  # "Firecrawl Scrape API"
    description: str = "Firecrawl 抓取 API。"  # "Firecrawl Scrape API."
    name = "FirecrawlScrapeApi"

    documentation: str = "https://docs.firecrawl.dev/api-reference/endpoint/scrape"

    inputs = [
        SecretStrInput(
            name="api_key",
            display_name="API 密钥",  # "API Key"
            required=True,
            password=True,
            info="用于 Firecrawl API 的 API 密钥。",  # "The API key to use Firecrawl API."
        ),
        MultilineInput(
            name="url",
            display_name="URL",
            required=True,
            info="要抓取的 URL。",  # "The URL to scrape."
            tool_mode=True,
        ),
        IntInput(
            name="timeout",
            display_name="超时时间",  # "Timeout"
            info="请求的超时时间（以毫秒为单位）。",  # "Timeout in milliseconds for the request."
        ),
        DataInput(
            name="scrapeOptions",
            display_name="抓取选项",  # "Scrape Options"
            info="随请求发送的页面选项。",  # "The page options to send with the request."
        ),
        DataInput(
            name="extractorOptions",
            display_name="提取器选项",  # "Extractor Options"
            info="随请求发送的提取器选项。",  # "The extractor options to send with the request."
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="scrape"),  # "Data"
    ]

    def scrape(self) -> Data:
        try:
            from firecrawl import FirecrawlApp
        except ImportError as e:
            msg = "无法导入 firecrawl 集成包。请使用 `pip install firecrawl-py` 安装。"  # "Could not import firecrawl integration package. Please install it with `pip install firecrawl-py`."
            raise ImportError(msg) from e

        params = self.scrapeOptions.__dict__.get("data", {}) if self.scrapeOptions else {}
        extractor_options_dict = self.extractorOptions.__dict__.get("data", {}) if self.extractorOptions else {}
        if extractor_options_dict:
            params["extract"] = extractor_options_dict

        # 设置参数的默认值
        # "Set default values for parameters"
        params.setdefault("formats", ["markdown"])  # 默认输出格式  # "Default output format"
        params.setdefault("onlyMainContent", True)  # 默认仅抓取主要内容  # "Default to only main content"

        app = FirecrawlApp(api_key=self.api_key)
        results = app.scrape_url(self.url, params=params)
        return Data(data=results)
