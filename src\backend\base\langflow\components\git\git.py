import re
import tempfile
from contextlib import asynccontextmanager
from fnmatch import fnmatch
from pathlib import Path

import anyio
from langchain_community.document_loaders.git import GitLoader

from langflow.custom import Component
from langflow.io import DropdownInput, MessageTextInput, Output
from langflow.schema import Data


class GitLoaderComponent(Component):
    display_name = "Git"
    description = (
        "从本地或远程 Git 仓库加载并过滤文档。"
        "使用本地仓库路径或从远程 URL 克隆。"  # "Load and filter documents from a local or remote Git repository. Use a local repo path or clone from a remote URL."
    )
    trace_type = "tool"
    icon = "GitLoader"

    inputs = [
        DropdownInput(
            name="repo_source",
            display_name="仓库来源",  # "Repository Source"
            options=["本地", "远程"],  # "Local", "Remote"
            required=True,
            info="选择使用本地仓库路径还是从远程 URL 克隆。",  # "Select whether to use a local repo path or clone from a remote URL."
            real_time_refresh=True,
        ),
        MessageTextInput(
            name="repo_path",
            display_name="本地仓库路径",  # "Local Repository Path"
            required=False,
            info="现有 Git 仓库的本地路径（当选择 '本地' 时使用）。",  # "The local path to the existing Git repository (used if 'Local' is selected)."
            dynamic=True,
            show=False,
        ),
        MessageTextInput(
            name="clone_url",
            display_name="克隆 URL",  # "Clone URL"
            required=False,
            info="要克隆的 Git 仓库的 URL（当选择 '远程' 时使用）。",  # "The URL of the Git repository to clone (used if 'Remote' is selected)."
            dynamic=True,
            show=False,
        ),
        MessageTextInput(
            name="branch",
            display_name="分支",  # "Branch"
            required=False,
            value="main",
            info="要加载文件的分支。默认为 'main'。",  # "The branch to load files from. Defaults to 'main'."
        ),
        MessageTextInput(
            name="file_filter",
            display_name="文件过滤器",  # "File Filter"
            required=False,
            advanced=True,
            info=(
                "用于过滤文件的模式。例如：\n"
                "仅包含 .py 文件：'*.py'\n"
                "排除 .py 文件：'!*.py'\n"
                "多个模式可以用逗号分隔。"  # "Patterns to filter files. For example:\nInclude only .py files: '*.py'\nExclude .py files: '!*.py'\nMultiple patterns can be separated by commas."
            ),
        ),
        MessageTextInput(
            name="content_filter",
            display_name="内容过滤器",  # "Content Filter"
            required=False,
            advanced=True,
            info="基于文件内容的正则表达式模式进行过滤。",  # "A regex pattern to filter files based on their content."
        ),
    ]

    outputs = [
        Output(name="data", display_name="数据", method="load_documents"),  # "Data"
    ]

    @staticmethod
    def is_binary(file_path: str | Path) -> bool:
        """通过检查是否存在空字节来判断文件是否为二进制文件。"""
        try:
            with Path(file_path).open("rb") as file:
                content = file.read(1024)
                return b"\x00" in content
        except Exception:  # noqa: BLE001
            return True

    @staticmethod
    def check_file_patterns(file_path: str | Path, patterns: str) -> bool:
        """检查文件是否匹配给定的模式。

        参数:
            file_path: 要检查的文件路径
            patterns: 逗号分隔的 glob 模式列表

        返回:
            bool: 如果文件应包含则为 True，如果排除则为 False
        """
        # 处理空或仅包含空格的模式
        if not patterns or patterns.isspace():
            return True

        path_str = str(file_path)
        file_name = Path(path_str).name
        pattern_list: list[str] = [pattern.strip() for pattern in patterns.split(",") if pattern.strip()]

        # 如果去除空格后没有有效模式，则视为包含所有
        if not pattern_list:
            return True

        # 先处理排除模式
        for pattern in pattern_list:
            if pattern.startswith("!"):
                # 对于排除模式，同时匹配完整路径和文件名
                exclude_pattern = pattern[1:]
                if fnmatch(path_str, exclude_pattern) or fnmatch(file_name, exclude_pattern):
                    return False

        # 然后检查包含模式
        include_patterns = [p for p in pattern_list if not p.startswith("!")]
        # 如果没有包含模式，则视为包含所有
        if not include_patterns:
            return True

        # 对于包含模式，同时匹配完整路径和文件名
        return any(fnmatch(path_str, pattern) or fnmatch(file_name, pattern) for pattern in include_patterns)

    @staticmethod
    def check_content_pattern(file_path: str | Path, pattern: str) -> bool:
        """检查文件内容是否匹配给定的正则表达式模式。

        参数:
            file_path: 要检查的文件路径
            pattern: 用于匹配内容的正则表达式模式

        返回:
            bool: 如果内容匹配则为 True，否则为 False
        """
        try:
            # 检查文件是否为二进制文件
            with Path(file_path).open("rb") as file:
                content = file.read(1024)
                if b"\x00" in content:
                    return False

            # 尝试先编译正则表达式模式
            try:
                # 使用 MULTILINE 标志以更好地处理文本内容
                content_regex = re.compile(pattern, re.MULTILINE)
                # 使用简单字符串测试模式以捕获语法错误
                test_str = "test\nstring"
                if not content_regex.search(test_str):
                    # 模式有效但不匹配测试字符串
                    pass
            except (re.error, TypeError, ValueError):
                return False

            # 如果不是二进制文件且正则表达式有效，则检查内容
            with Path(file_path).open(encoding="utf-8") as file:
                file_content = file.read()
            return bool(content_regex.search(file_content))
        except (OSError, UnicodeDecodeError):
            return False

    def build_combined_filter(self, file_filter_patterns: str | None = None, content_filter_pattern: str | None = None):
        """Build a combined filter function from file and content patterns.

        Args:
            file_filter_patterns: Comma-separated glob patterns
            content_filter_pattern: Regex pattern for content

        Returns:
            callable: Filter function that takes a file path and returns bool
        """

        def combined_filter(file_path: str) -> bool:
            try:
                path = Path(file_path)

                # Check if file exists and is readable
                if not path.exists():
                    return False

                # Check if file is binary
                if self.is_binary(path):
                    return False

                # Apply file pattern filters
                if file_filter_patterns and not self.check_file_patterns(path, file_filter_patterns):
                    return False

                # Apply content filter
                return not (content_filter_pattern and not self.check_content_pattern(path, content_filter_pattern))
            except Exception:  # noqa: BLE001
                return False

        return combined_filter

    @asynccontextmanager
    async def temp_clone_dir(self):
        """Context manager for handling temporary clone directory."""
        temp_dir = None
        try:
            temp_dir = tempfile.mkdtemp(prefix="langflow_clone_")
            yield temp_dir
        finally:
            if temp_dir:
                await anyio.Path(temp_dir).rmdir()

    def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:
        # Hide fields by default
        build_config["repo_path"]["show"] = False
        build_config["clone_url"]["show"] = False

        if field_name == "repo_source":
            if field_value == "Local":
                build_config["repo_path"]["show"] = True
                build_config["repo_path"]["required"] = True
                build_config["clone_url"]["required"] = False
            elif field_value == "Remote":
                build_config["clone_url"]["show"] = True
                build_config["clone_url"]["required"] = True
                build_config["repo_path"]["required"] = False

        return build_config

    async def build_gitloader(self) -> GitLoader:
        file_filter_patterns = getattr(self, "file_filter", None)
        content_filter_pattern = getattr(self, "content_filter", None)

        combined_filter = self.build_combined_filter(file_filter_patterns, content_filter_pattern)

        repo_source = getattr(self, "repo_source", None)
        if repo_source == "Local":
            repo_path = self.repo_path
            clone_url = None
        else:
            # Clone source
            clone_url = self.clone_url
            async with self.temp_clone_dir() as temp_dir:
                repo_path = temp_dir

        # Only pass branch if it's explicitly set
        branch = getattr(self, "branch", None)
        if not branch:
            branch = None

        return GitLoader(
            repo_path=repo_path,
            clone_url=clone_url if repo_source == "Remote" else None,
            branch=branch,
            file_filter=combined_filter,
        )

    async def load_documents(self) -> list[Data]:
        gitloader = await self.build_gitloader()
        data = [Data.from_document(doc) async for doc in gitloader.alazy_load()]
        self.status = data
        return data
