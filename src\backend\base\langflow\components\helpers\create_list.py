from langflow.custom import Component
from langflow.inputs import StrInput
from langflow.schema import Data
from langflow.schema.dataframe import DataFrame
from langflow.template import Output


class CreateListComponent(Component):
    display_name = "创建列表"
    description = "创建一个文本列表。"  # "Creates a list of texts."
    icon = "list"
    name = "CreateList"
    legacy = True

    inputs = [
        StrInput(
            name="texts",
            display_name="文本",  # "Texts"
            info="输入一个或多个文本。",  # "Enter one or more texts."
            is_list=True,
        ),
    ]

    outputs = [
        Output(display_name="数据列表", name="list", method="create_list"),  # "Data List"
        Output(display_name="数据框", name="dataframe", method="as_dataframe"),  # "DataFrame"
    ]

    def create_list(self) -> list[Data]:
        data = [Data(text=text) for text in self.texts]
        self.status = data
        return data

    def as_dataframe(self) -> DataFrame:
        """将 Data 对象列表转换为数据框。"""  # "Convert the list of Data objects into a DataFrame."

        return DataFrame(self.create_list())
