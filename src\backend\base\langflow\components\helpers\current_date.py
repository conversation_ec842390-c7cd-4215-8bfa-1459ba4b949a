from datetime import datetime
from zoneinfo import ZoneInfo, available_timezones

from loguru import logger

from langflow.custom import Component
from langflow.io import DropdownInput, Output
from langflow.schema.message import Message


class CurrentDateComponent(Component):
    display_name = "当前日期"
    description = "返回所选时区的当前日期和时间。"  # "Returns the current date and time in the selected timezone."
    icon = "clock"
    name = "CurrentDate"

    inputs = [
        DropdownInput(
            name="timezone", 
            display_name="时区",  # "Timezone"
            options=list(available_timezones()),
            value="UTC",
            info="选择当前日期和时间的时区。",  # "Select the timezone for the current date and time."
            tool_mode=True,
        ),
    ]
    outputs = [
        Output(display_name="当前日期", name="current_date", method="get_current_date"),  # "Current Date"
    ]

    def get_current_date(self) -> Message:
        try:
            tz = ZoneInfo(self.timezone)
            current_date = datetime.now(tz).strftime("%Y-%m-%d %H:%M:%S %Z")
            result = f"当前日期和时间（{self.timezone} 时区）：{current_date}"  # "Current date and time in {self.timezone}: {current_date}"
            self.status = result
            return Message(text=result)
        except Exception as e:  # noqa: BLE001
            logger.opt(exception=True).debug("获取当前日期时出错")  # "Error getting current date"
            error_message = f"错误：{e}"  # "Error: {e}"
            self.status = error_message
            return Message(text=error_message)
