
from langflow.custom import Component
from langflow.template import Output, Input
from langflow.schema.message import Message
from langflow.io import MessageTextInput

class CutThinkComponent(Component):
    display_name = "去除<think>标签"
    description = "去除大模型回答中的<think></think>标签内容。"
    name = "CutThink"
    icon = "cut"

    inputs = [
        MessageTextInput(
            name="input_value",
            display_name="输入",
            info="带有</think>标签的消息内容",
        ),
    ]

    outputs = [
        Output(display_name="Result", name="result", method="process_inputs"),
    ]

    def process_inputs(self) -> Message:
        """
        Process the user inputs and return a Message object.

        Returns:
            Message: A Message object containing the processed information.
        """
        try:
            processed_text = self.input_value.split("</think>", 1)[-1].strip()
            return Message(text=processed_text)
        except AttributeError as e:
            return Message(text=f"Error processing inputs: {str(e)}")