from langflow.custom import Component
from langflow.helpers.data import data_to_text
from langflow.inputs import HandleInput
from langflow.io import DropdownInput, IntInput, MessageTextInput, MultilineInput, Output
from langflow.memory import aget_messages
from langflow.schema import Data
from langflow.schema.dataframe import DataFrame
from langflow.schema.message import Message
from langflow.utils.constants import MESSAGE_SENDER_AI, MESSAGE_SENDER_USER


class MemoryComponent(Component):
    display_name = "消息历史" # "Message History"
    description = "从表或外部内存中检索存储的聊天消息。"  # "Retrieves stored chat messages from Langflow tables or an external memory."
    icon = "message-square-more"
    name = "Memory"

    inputs = [
        HandleInput(
            name="memory",
            display_name="外部内存",  # "External Memory"
            input_types=["Memory"],
            info="从外部内存中检索消息。如果为空，将使用 Langflow 表。",  # "Retrieve messages from an external memory. If empty, it will use the Langflow tables."
        ),
        DropdownInput(
            name="sender",
            display_name="发送者类型",  # "Sender Type"
            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER, "机器和用户"],  # "Machine and User"
            value="机器和用户",  # "Machine and User"
            info="按发送者类型过滤。",  # "Filter by sender type."
            advanced=True,
        ),
        MessageTextInput(
            name="sender_name",
            display_name="发送者名称",  # "Sender Name"
            info="按发送者名称过滤。",  # "Filter by sender name."
            advanced=True,
        ),
        IntInput(
            name="n_messages",
            display_name="消息数量",  # "Number of Messages"
            value=100,
            info="要检索的消息数量。",  # "Number of messages to retrieve."
            advanced=True,
        ),
        MessageTextInput(
            name="session_id",
            display_name="会话 ID",  # "Session ID"
            info="聊天的会话 ID。如果为空，将使用当前会话 ID 参数。",  # "The session ID of the chat. If empty, the current session ID parameter will be used."
            advanced=True,
        ),
        DropdownInput(
            name="order",
            display_name="排序",  # "Order"
            options=["升序", "降序"],  # "Ascending", "Descending"
            value="升序",  # "Ascending"
            info="消息的排序顺序。",  # "Order of the messages."
            advanced=True,
            tool_mode=True,
        ),
        MultilineInput(
            name="template",
            display_name="模板",  # "Template"
            info="用于格式化数据的模板。"
            "它可以包含键 {text}、{sender} 或消息数据中的任何其他键。",  # "The template to use for formatting the data. It can contain the keys {text}, {sender} or any other key in the message data."
            value="{sender_name}: {text}",
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="数据", name="messages", method="retrieve_messages"),  # "Data"
        Output(display_name="消息", name="messages_text", method="retrieve_messages_as_text"),  # "Message"
        Output(display_name="数据框", name="dataframe", method="as_dataframe"),  # "DataFrame"
    ]

    async def retrieve_messages(self) -> Data:
        sender = self.sender
        sender_name = self.sender_name
        session_id = self.session_id
        n_messages = self.n_messages
        order = "DESC" if self.order == "降序" else "ASC"  # "Descending", "Ascending"

        if sender == "机器和用户":  # "Machine and User"
            sender = None

        if self.memory and not hasattr(self.memory, "aget_messages"):
            memory_name = type(self.memory).__name__
            err_msg = f"外部内存对象 ({memory_name}) 必须具有 'aget_messages' 方法。"  # "External Memory object ({memory_name}) must have 'aget_messages' method."
            raise AttributeError(err_msg)

        if self.memory:
            # 覆盖 session_id
            # "Override session_id"
            self.memory.session_id = session_id

            stored = await self.memory.aget_messages()
            # langchain 内存应返回升序消息
            # "Langchain memories are supposed to return messages in ascending order"
            if order == "DESC":
                stored = stored[::-1]
            if n_messages:
                stored = stored[:n_messages]
            stored = [Message.from_lc_message(m) for m in stored]
            if sender:
                expected_type = MESSAGE_SENDER_AI if sender == MESSAGE_SENDER_AI else MESSAGE_SENDER_USER
                stored = [m for m in stored if m.type == expected_type]
        else:
            stored = await aget_messages(
                sender=sender,
                sender_name=sender_name,
                session_id=session_id,
                limit=n_messages,
                order=order,
            )
        self.status = stored
        return stored

    async def retrieve_messages_as_text(self) -> Message:
        stored_text = data_to_text(self.template, await self.retrieve_messages())
        self.status = stored_text
        return Message(text=stored_text)

    async def as_dataframe(self) -> DataFrame:
        """将检索到的消息转换为数据框。

        返回：
            DataFrame: 包含消息数据的数据框。
        """  # "Convert the retrieved messages into a DataFrame. Returns: A DataFrame containing the message data."
        messages = await self.retrieve_messages()
        return DataFrame(messages)
