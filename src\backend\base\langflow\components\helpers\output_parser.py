from langchain_core.output_parsers import CommaSeparatedListOutputParser

from langflow.custom.custom_component.component import Component
from langflow.field_typing.constants import OutputParser
from langflow.io import DropdownInput, Output
from langflow.schema.message import Message


class OutputParserComponent(Component):
    display_name = "输出解析器"  # "Output Parser"
    description = "将 LLM 的输出转换为指定格式。"  # "Transforms the output of an LLM into a specified format."
    icon = "type"
    name = "OutputParser"
    legacy = True

    inputs = [
        DropdownInput(
            name="parser_type", 
            display_name="解析器",  # "Parser"
            options=["CSV"],
            value="CSV",
        ),
    ]

    outputs = [
        Output(
            display_name="格式说明",  # "Format Instructions"
            name="format_instructions",
            info="传递给提示模板以包含 LLM 响应的格式说明。",  # "Pass to a prompt template to include formatting instructions for LLM responses."
            method="format_instructions",
        ),
        Output(
            display_name="输出解析器",  # "Output Parser"
            name="output_parser",
            method="build_parser",
        ),
    ]

    def build_parser(self) -> OutputParser:
        if self.parser_type == "CSV":
            return CommaSeparatedListOutputParser()
        msg = "不支持或缺少解析器"  # "Unsupported or missing parser"
        raise ValueError(msg)

    def format_instructions(self) -> Message:
        if self.parser_type == "CSV":
            return Message(text=CommaSeparatedListOutputParser().get_format_instructions())
        msg = "不支持或缺少解析器"  # "Unsupported or missing parser"
        raise ValueError(msg)
