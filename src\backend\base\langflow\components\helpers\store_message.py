from langflow.custom import Component
from langflow.inputs import HandleInput
from langflow.inputs.inputs import MessageTextInput
from langflow.memory import aget_messages, astore_message
from langflow.schema.message import Message
from langflow.template import Output
from langflow.utils.constants import MESSAGE_SENDER_AI, MESSAGE_SENDER_NAME_AI


class MessageStoreComponent(Component):
    display_name = "消息存储"  # "Message Store"
    description = "将聊天消息或文本存储到表或外部内存中。"  # "Stores a chat message or text into Langflow tables or an external memory."
    icon = "message-square-text"
    name = "StoreMessage"

    inputs = [
        MessageTextInput(
            name="message",
            display_name="消息",  # "Message"
            info="要存储的聊天消息。",  # "The chat message to be stored."
            required=True,
            tool_mode=True,
        ),
        HandleInput(
            name="memory",
            display_name="外部内存",  # "External Memory"
            input_types=["Memory"],
            info="用于存储消息的外部内存。如果为空，将使用wiseAgent表。",  # "The external memory to store the message. If empty, it will use the Langflow tables."
        ),
        MessageTextInput(
            name="sender",
            display_name="发送者",  # "Sender"
            info="消息的发送者。可能是机器或用户。如果为空，将使用当前发送者参数。",  # "The sender of the message. Might be Machine or User. If empty, the current sender parameter will be used."
            advanced=True,
        ),
        MessageTextInput(
            name="sender_name",
            display_name="发送者名称",  # "Sender Name"
            info="发送者的名称。可能是 AI 或用户。如果为空，将使用当前发送者参数。",  # "The name of the sender. Might be AI or User. If empty, the current sender parameter will be used."
            advanced=True,
        ),
        MessageTextInput(
            name="session_id",
            display_name="会话 ID",  # "Session ID"
            info="聊天的会话 ID。如果为空，将使用当前会话 ID 参数。",  # "The session ID of the chat. If empty, the current session ID parameter will be used."
            value="",
            advanced=True,
        ),
    ]

    outputs = [
        Output(
            display_name="存储的消息",  # "Stored Messages"
            name="stored_messages",
            method="store_message",
            hidden=True,
        ),
    ]

    async def store_message(self) -> Message:
        message = Message(text=self.message) if isinstance(self.message, str) else self.message

        message.session_id = self.session_id or message.session_id
        message.sender = self.sender or message.sender or MESSAGE_SENDER_AI
        message.sender_name = self.sender_name or message.sender_name or MESSAGE_SENDER_NAME_AI

        stored_messages: list[Message] = []

        if self.memory:
            self.memory.session_id = message.session_id
            lc_message = message.to_lc_message()
            await self.memory.aadd_messages([lc_message])

            stored_messages = await self.memory.aget_messages() or []

            stored_messages = [Message.from_lc_message(m) for m in stored_messages] if stored_messages else []

            if message.sender:
                stored_messages = [m for m in stored_messages if m.sender == message.sender]
        else:
            await astore_message(message, flow_id=self.graph.flow_id)
            stored_messages = (
                await aget_messages(
                    session_id=message.session_id, sender_name=message.sender_name, sender=message.sender
                )
                or []
            )

        if not stored_messages:
            msg = "未存储任何消息。请确保正确设置了会话 ID 和发送者。"  # "No messages were stored. Please ensure that the session ID and sender are properly set."
            raise ValueError(msg)

        stored_message = stored_messages[0]
        self.status = stored_message
        return stored_message
