from typing import TYPE_CHECKING, cast

from pydantic import BaseModel, Field, create_model

from langflow.base.models.chat_result import get_chat_result
from langflow.custom import Component
from langflow.helpers.base_model import build_model_from_schema
from langflow.io import (
    BoolInput,
    HandleInput,
    MessageTextInput,
    MultilineInput,
    Output,
    TableInput,
)
from langflow.schema.data import Data
from langflow.schema.dataframe import DataFrame
from langflow.schema.table import EditMode

if TYPE_CHECKING:
    from langflow.field_typing.constants import LanguageModel


class StructuredOutputComponent(Component):
    display_name = "结构化输出"  # "Structured Output"
    description = (
        "将 LLM 响应转换为 **结构化数据格式**。非常适合提取特定信息或创建一致的输出。"  # "Transforms LLM responses into **structured data formats**. Ideal for extracting specific information or creating consistent outputs."
    )
    name = "StructuredOutput"
    icon = "braces"

    inputs = [
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            info="用于生成结构化输出的语言模型。",  # "The language model to use to generate the structured output."
            input_types=["LanguageModel"],
            required=True,
        ),
        MessageTextInput(
            name="input_value",
            display_name="输入消息",  # "Input Message"
            info="传递给语言模型的输入消息。",  # "The input message to the language model."
            tool_mode=True,
            required=True,
        ),
        MultilineInput(
            name="system_prompt",
            display_name="格式说明",  # "Format Instructions"
            info="提供给语言模型的用于格式化输出的说明。",  # "The instructions to the language model for formatting the output."
            value=(
                "您是一个 AI 系统，旨在从非结构化文本中提取结构化信息。"
                "根据输入文本，返回一个具有预定义键的 JSON 对象，基于预期的结构。"
                "准确提取值并根据指定的类型（例如，字符串、整数、浮点数、日期）格式化它们。"
                "如果缺少值或无法确定值，请返回默认值（例如，null、0 或 'N/A'）。"
                "如果输入文本中存在多个预期结构的实例，请将每个实例作为单独的 JSON 对象流式返回。"  # Original English instructions
            ),
            required=True,
            advanced=True,
        ),
        MessageTextInput(
            name="schema_name",
            display_name="模式名称",  # "Schema Name"
            info="为输出数据模式提供一个名称。",  # "Provide a name for the output data schema."
            advanced=True,
        ),
        TableInput(
            name="output_schema",
            display_name="输出模式",  # "Output Schema"
            info="定义模型输出的结构和数据类型。",  # "Define the structure and data types for the model's output."
            required=True,
            table_schema=[
                {
                    "name": "name",
                    "display_name": "名称",  # "Name"
                    "type": "str",
                    "description": "指定输出字段的名称。",  # "Specify the name of the output field."
                    "default": "field",
                    "edit_mode": EditMode.INLINE,
                },
                {
                    "name": "description",
                    "display_name": "描述",  # "Description"
                    "type": "str",
                    "description": "描述输出字段的用途。",  # "Describe the purpose of the output field."
                    "default": "字段的描述",  # "description of field"
                    "edit_mode": EditMode.POPOVER,
                },
                {
                    "name": "type",
                    "display_name": "类型",  # "Type"
                    "type": "str",
                    "edit_mode": EditMode.INLINE,
                    "description": (
                        "指示输出字段的数据类型（例如，str、int、float、bool、list、dict）。"  # "Indicate the data type of the output field (e.g., str, int, float, bool, list, dict)."
                    ),
                    "options": ["str", "int", "float", "bool", "list", "dict"],
                    "default": "str",
                },
                {
                    "name": "multiple",
                    "display_name": "多个值",  # "Multiple"
                    "type": "boolean",
                    "description": "如果此输出字段应为指定类型的列表，则设置为 True。",  # "Set to True if this output field should be a list of the specified type."
                    "default": "False",
                    "edit_mode": EditMode.INLINE,
                },
            ],
            value=[
                {
                    "name": "field",
                    "description": "字段的描述",  # "description of field"
                    "type": "str",
                    "multiple": "False",
                }
            ],
        ),
        BoolInput(
            name="multiple",
            advanced=True,
            display_name="生成多个",  # "Generate Multiple"
            info="[已弃用] 始终设置为 True",  # "[Deprecated] Always set to True"
            value=True,
        ),
    ]

    outputs = [
        Output(
            name="structured_output",
            display_name="结构化输出",  # "Structured Output"
            method="build_structured_output",
        ),
        Output(
            name="structured_output_dataframe",
            display_name="数据框",  # "DataFrame"
            method="as_dataframe",
        ),
    ]

    def build_structured_output_base(self) -> Data:
        schema_name = self.schema_name or "OutputModel"

        if not hasattr(self.llm, "with_structured_output"):
            msg = "语言模型不支持结构化输出。"  # "Language model does not support structured output."
            raise TypeError(msg)
        if not self.output_schema:
            msg = "输出模式不能为空。"  # "Output schema cannot be empty."
            raise ValueError(msg)

        output_model_ = build_model_from_schema(self.output_schema)

        output_model = create_model(
            schema_name,
            objects=(list[output_model_], Field(description=f"一个 {schema_name} 的列表。")),  # "A list of {schema_name}."
        )

        try:
            llm_with_structured_output = cast("LanguageModel", self.llm).with_structured_output(schema=output_model)

        except NotImplementedError as exc:
            msg = f"{self.llm.__class__.__name__} 不支持结构化输出。"  # "{self.llm.__class__.__name__} does not support structured output."
            raise TypeError(msg) from exc
        config_dict = {
            "run_name": self.display_name,
            "project_name": self.get_project_name(),
            "callbacks": self.get_langchain_callbacks(),
        }
        result = get_chat_result(
            runnable=llm_with_structured_output,
            system_message=self.system_prompt,
            input_value=self.input_value,
            config=config_dict,
        )
        if isinstance(result, BaseModel):
            result = result.model_dump()
        if "objects" in result:
            return result["objects"]
        return result

    def build_structured_output(self) -> Data:
        output = self.build_structured_output_base()

        return Data(results=output)

    def as_dataframe(self) -> DataFrame:
        output = self.build_structured_output_base()
        if isinstance(output, list):
            return DataFrame(data=output)
        return DataFrame(data=[output])
