import os
from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES
from langflow.base.io.chat import ChatComponent
from langflow.inputs import BoolInput
from langflow.io import (
    DropdownInput,
    FileInput,
    MessageTextInput,
    MultilineInput,
    Output,
)
from langflow.services.deps import get_storage_service
from langflow.schema.message import Message
from langflow.utils.constants import (
    MESSAGE_SENDER_AI,
    MESSAGE_SENDER_NAME_USER,
    MESSAGE_SENDER_USER,
)


class ChatOCRInput(ChatComponent):
    display_name = "聊天输入ocr"  # "Chat Input"
    description = "从练习场获取聊天输入、支持上传各类文件解析。"  # "Get chat inputs from the Playground."
    icon = "MessagesSquare"  # "消息方块"
    name = "ChatOCRInput"  # ""
    minimized = True

    inputs = [
        MultilineInput(
            name="input_value",  # "input_value"
            display_name="文本",  # "Text"
            value="",
            info="作为输入传递的消息。",  # "Message to be passed as input."
            input_types=[],
        ),
        BoolInput(
            name="should_store_message",  # "should_store_message"
            display_name="存储消息",  # "Store Messages"
            info="将消息存储在历史记录中。",  # "Store the message in the history."
            value=True,
            advanced=True,
        ),
        DropdownInput(
            name="sender",  # "sender"
            display_name="发送者类型",  # "Sender Type"
            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],
            value=MESSAGE_SENDER_USER,
            info="发送者的类型。",  # "Type of sender."
            advanced=True,
        ),
        MessageTextInput(
            name="sender_name",  # "sender_name"
            display_name="发送者名称",  # "Sender Name"
            info="发送者的名称。",  # "Name of the sender."
            value=MESSAGE_SENDER_NAME_USER,
            advanced=True,
        ),
        MessageTextInput(
            name="session_id",  # "session_id"
            display_name="会话 ID",  # "Session ID"
            info="聊天的会话 ID。如果为空，将使用当前会话 ID 参数。",  # "The session ID of the chat. If empty, the current session ID parameter will be used."
            advanced=True,
        ),
        FileInput(
            name="files",  # "files"
            display_name="文件",  # "Files"
            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,
            info="随消息发送的文件。",  # "Files to be sent with the message."
            advanced=True,
            is_list=True,
            temp_file=True,
        ),
        MessageTextInput(
            name="background_color",  # "background_color"
            display_name="背景颜色",  # "Background Color"
            info="图标的背景颜色。",  # "The background color of the icon."
            advanced=True,
        ),
        MessageTextInput(
            name="chat_icon",  # "chat_icon"
            display_name="图标",  # "Icon"
            info="消息的图标。",  # "The icon of the message."
            advanced=True,
        ),
        MessageTextInput(
            name="text_color",  # "text_color"
            display_name="文本颜色",  # "Text Color"
            info="名称的文本颜色。",  # "The text color of the name."
            advanced=True,
        ),
    ]
    outputs = [
        Output(display_name="消息", name="message", method="message_response"),  # "Message"
        Output(display_name="文件", name="messages", method="file_response"),
    ]

    async def message_response(self) -> Message:
        background_color = self.background_color
        text_color = self.text_color
        icon = self.chat_icon

        message = await Message.create(
            text=self.input_value,
            sender=self.sender,
            sender_name=self.sender_name,
            session_id=self.session_id,
            files=[],
            properties={
                "background_color": background_color,
                "text_color": text_color,
                "icon": icon,
            },
        )
        if self.session_id and isinstance(message, Message) and self.should_store_message:
            stored_message = await self.send_message(
                message,
            )
            self.message.value = stored_message
            message = stored_message

        self.status = message
        return message

    async def file_response(self) -> list[Message]:
        storage_service = get_storage_service()
        file_paths = []
        for obj in self.files:
            # 检查是否为绝对路径
            if os.path.isabs(str(obj)):
                file_path = str(obj)
            else:
                # 如果不是绝对路径，添加data_dir前缀
                file_path = os.path.join(str(storage_service.data_dir), str(obj))
            message = await Message.create(text=file_path)
            file_paths.append(message)
        return file_paths
    