from langflow.base.io.text import TextComponent
from langflow.io import MultilineInput, Output
from langflow.schema.message import Message


class TextInputComponent(TextComponent):
    display_name = "文本输入"  # "Text Input"
    description = "从练习场获取文本输入。"  # "Get text inputs from the Playground."
    icon = "type"  # "文本类型"
    name = "TextInput"  # ""

    inputs = [
        MultilineInput(
            name="input_value",  # "input_value"
            display_name="文本",  # "Text"
            info="作为输入传递的文本。",  # "Text to be passed as input."
        ),
    ]
    outputs = [
        Output(display_name="消息", name="text", method="text_response"),  # "Message"
    ]

    def text_response(self) -> Message:
        return Message(
            text=self.input_value,
        )
