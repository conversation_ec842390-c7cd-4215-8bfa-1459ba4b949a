from typing import Any

from langchain_text_splitters import CharacterTextSplitter, TextSplitter

from langflow.base.textsplitters.model import LCTextSplitterComponent
from langflow.inputs import DataInput, IntInput, MessageTextInput
from langflow.utils.util import unescape_string


class CharacterTextSplitterComponent(LCTextSplitterComponent):
    display_name = "字符文本拆分器"  # "Character Text Splitter"
    description = "按字符数拆分文本。"  # "Split text by number of characters."
    documentation = "https://docs.langflow.org/components/text-splitters#charactertextsplitter"
    name = "CharacterTextSplitter"
    icon = "LangChain"

    inputs = [
        IntInput(
            name="chunk_size",
            display_name="块大小",  # "Chunk Size"
            info="每个块的最大长度。",  # "The maximum length of each chunk."
            value=1000,
        ),
        IntInput(
            name="chunk_overlap",
            display_name="块重叠",  # "Chunk Overlap"
            info="块之间的重叠量。",  # "The amount of overlap between chunks."
            value=200,
        ),
        DataInput(
            name="data_input",
            display_name="输入",  # "Input"
            info="要拆分的文本。",  # "The texts to split."
            input_types=["Document", "Data"],
            required=True,
        ),
        MessageTextInput(
            name="separator",
            display_name="分隔符",  # "Separator"
            info='要拆分的字符。\n如果留空，默认为 "\\n\\n"。',  # 'The characters to split on.\nIf left empty defaults to "\\n\\n".'
        ),
    ]

    def get_data_input(self) -> Any:
        return self.data_input

    def build_text_splitter(self) -> TextSplitter:
        separator = unescape_string(self.separator) if self.separator else "\n\n"
        return CharacterTextSplitter(
            chunk_overlap=self.chunk_overlap,
            chunk_size=self.chunk_size,
            separator=separator,
        )
