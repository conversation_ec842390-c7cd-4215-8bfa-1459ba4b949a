from langchain_experimental.agents.agent_toolkits.csv.base import create_csv_agent

from langflow.base.agents.agent import LCAgentComponent
from langflow.field_typing import AgentExecutor
from langflow.inputs import DropdownInput, FileInput, HandleInput
from langflow.inputs.inputs import DictInput, MessageTextInput
from langflow.schema.message import Message
from langflow.template.field.base import Output


class CSVAgentComponent(LCAgentComponent):
    display_name = "CSV代理"  # "CSV Agent"
    description = "从 CSV 和工具构建一个 CSV 代理。"  # "Construct a CSV agent from a CSV and tools."
    documentation = "https://python.langchain.com/docs/modules/agents/toolkits/csv"
    name = "CSVAgent"
    icon = "LangChain"

    inputs = [
        *LCAgentComponent._base_inputs,
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            input_types=["LanguageModel"],
            required=True,
            info="一个 LLM 模型对象（可以在任何 LLM 组件中找到）。",  # "An LLM Model Object (It can be found in any LLM Component)."
        ),
        FileInput(
            name="path",
            display_name="文件路径",  # "File Path"
            file_types=["csv"],
            input_types=["str", "Message"],
            required=True,
            info="一个 CSV 文件或文件路径。",  # "A CSV File or File Path."
        ),
        DropdownInput(
            name="agent_type", 
            display_name="代理类型",  # "Agent Type"
            advanced=True,
            options=["zero-shot-react-description", "openai-functions", "openai-tools"],
            value="openai-tools",
        ),
        MessageTextInput(
            name="input_value",
            display_name="文本",  # "Text"
            info="传递为输入并从 CSV 文件中提取信息的文本。",  # "Text to be passed as input and extract info from the CSV File."
            required=True,
        ),
        DictInput(
            name="pandas_kwargs",
            display_name="Pandas 参数",  # "Pandas Kwargs"
            info="传递给代理的 Pandas 参数。",  # "Pandas Kwargs to be passed to the agent."
            advanced=True,
            is_list=True,
        ),
    ]

    outputs = [
        Output(display_name="响应", name="response", method="build_agent_response"),  # "Response"
        Output(display_name="代理", name="agent", method="build_agent", hidden=True, tool_mode=False),  # "Agent"
    ]

    def _path(self) -> str:
        if isinstance(self.path, Message) and isinstance(self.path.text, str):
            return self.path.text
        return self.path

    def build_agent_response(self) -> Message:
        agent_kwargs = {
            "verbose": self.verbose,
            "allow_dangerous_code": True,
        }

        agent_csv = create_csv_agent(
            llm=self.llm,
            path=self._path(),
            agent_type=self.agent_type,
            handle_parsing_errors=self.handle_parsing_errors,
            pandas_kwargs=self.pandas_kwargs,
            **agent_kwargs,
        )

        result = agent_csv.invoke({"input": self.input_value})
        return Message(text=str(result["output"]))

    def build_agent(self) -> AgentExecutor:
        agent_kwargs = {
            "verbose": self.verbose,
            "allow_dangerous_code": True,
        }

        agent_csv = create_csv_agent(
            llm=self.llm,
            path=self._path(),
            agent_type=self.agent_type,
            handle_parsing_errors=self.handle_parsing_errors,
            pandas_kwargs=self.pandas_kwargs,
            **agent_kwargs,
        )

        self.status = Message(text=str(agent_csv))

        return agent_csv
