from langchain_community.embeddings import FakeEmbeddings

from langflow.base.embeddings.model import LCEmbeddingsModel
from langflow.field_typing import Embeddings
from langflow.io import IntInput


class FakeEmbeddingsComponent(LCEmbeddingsModel):
    display_name = "假嵌入"  # "Fake Embeddings"
    description = "生成假嵌入，适用于初始测试和连接组件。"  # "Generate fake embeddings, useful for initial testing and connecting components."
    icon = "LangChain"
    name = "LangChainFakeEmbeddings"

    inputs = [
        IntInput(
            name="dimensions",
            display_name="维度",  # "Dimensions"
            info="生成的嵌入输出应具有的维度数量。",  # "The number of dimensions the resulting output embeddings should have."
            value=5,
        ),
    ]

    def build_embeddings(self) -> Embeddings:
        return FakeEmbeddings(
            size=self.dimensions or 5,
        )
