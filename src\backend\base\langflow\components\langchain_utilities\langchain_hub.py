import re

from langchain_core.prompts import HumanMessagePromptTemplate

from langflow.custom import Component
from langflow.inputs import Default<PERSON><PERSON>pt<PERSON>ield, SecretStrInput, StrInput
from langflow.io import Output
from langflow.schema.message import Message


class LangChainHubPromptComponent(Component):
    display_name: str = "提示中心"
    description: str = "使用 LangChain Hub 提示的提示组件。"  # "Prompt Component that uses LangChain Hub prompts."
    beta = True
    icon = "LangChain"
    trace_type = "prompt"
    name = "LangChain Hub Prompt"

    inputs = [
        SecretStrInput(
            name="langchain_api_key",
            display_name="您的 LangChain API 密钥",  # "Your LangChain API Key"
            info="要使用的 LangChain API 密钥。",  # "The LangChain API Key to use."
            required=True,
        ),
        StrInput(
            name="langchain_hub_prompt",
            display_name="LangChain Hub 提示",  # "LangChain Hub Prompt"
            info="要使用的 LangChain Hub 提示，例如 'efriis/my-first-prompt'。",  # "The LangChain Hub prompt to use, i.e., 'efriis/my-first-prompt'."
            refresh_button=True,
            required=True,
        ),
    ]

    outputs = [
        Output(display_name="构建提示", name="prompt", method="build_prompt"),  # "Build Prompt"
    ]

    def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None):
        # 如果字段不是 langchain_hub_prompt 或值为空，则按原样返回构建配置
        # "If the field is not langchain_hub_prompt or the value is empty, return the build config as is"
        if field_name != "langchain_hub_prompt" or not field_value:
            return build_config

        # 获取模板
        # "Fetch the template"
        template = self._fetch_langchain_hub_template()

        # 获取模板的消息
        # "Get the template's messages"
        if hasattr(template, "messages"):
            template_messages = template.messages
        else:
            template_messages = [HumanMessagePromptTemplate(prompt=template)]

        # 从提示数据中提取消息
        # "Extract the messages from the prompt data"
        prompt_template = [message_data.prompt for message_data in template_messages]

        # 正则表达式以查找所有 {<string>} 实例
        # "Regular expression to find all instances of {<string>}"
        pattern = r"\{(.*?)\}"

        # 获取所有自定义字段
        # "Get all the custom fields"
        custom_fields: list[str] = []
        full_template = ""
        for message in prompt_template:
            # 查找所有匹配项
            # "Find all matches"
            matches = re.findall(pattern, message.template)
            custom_fields += matches

            # 创建完整模板的字符串版本
            # "Create a string version of the full template"
            full_template = full_template + "\n" + message.template

        # 如果我们已经有它们，则无需重新处理
        # "No need to reprocess if we have them already"
        if all("param_" + custom_field in build_config for custom_field in custom_fields):
            return build_config

        # 彩蛋：在信息弹出窗口中显示模板
        # "Easter egg: Show template in info popup"
        build_config["langchain_hub_prompt"]["info"] = full_template

        # 如果有旧的参数输入，则将其删除
        # "Remove old parameter inputs if any"
        for key in build_config.copy():
            if key.startswith("param_"):
                del build_config[key]

        # 现在为每个字段创建输入
        # "Now create inputs for each"
        for custom_field in custom_fields:
            new_parameter = DefaultPromptField(
                name=f"param_{custom_field}",
                display_name=custom_field,
                info="填写 {" + custom_field + "} 的值",  # "Fill in the value for {" + custom_field + "}"
            ).to_dict()

            # 将新参数添加到构建配置
            # "Add the new parameter to the build config"
            build_config[f"param_{custom_field}"] = new_parameter

        return build_config

    async def build_prompt(
        self,
    ) -> Message:
        # 获取模板
        # "Fetch the template"
        template = self._fetch_langchain_hub_template()

        # 从属性中获取参数
        # "Get the parameters from the attributes"
        params_dict = {param: getattr(self, "param_" + param, f"{{{param}}}") for param in template.input_variables}
        original_params = {k: v.text if hasattr(v, "text") else v for k, v in params_dict.items() if v is not None}
        prompt_value = template.invoke(original_params)

        # 使用新值更新模板
        # "Update the template with the new value"
        original_params["template"] = prompt_value.to_string()

        # 现在将过滤后的属性传递给函数
        # "Now pass the filtered attributes to the function"
        prompt = Message.from_template(**original_params)

        self.status = prompt.text

        return prompt

    def _fetch_langchain_hub_template(self):
        import langchain.hub

        # 检查是否提供了 API 密钥
        # "Check if the api key is provided"
        if not self.langchain_api_key:
            msg = "请提供 LangChain API 密钥"  # "Please provide a LangChain API Key"

            raise ValueError(msg)

        # 从 LangChain Hub 拉取提示
        # "Pull the prompt from LangChain Hub"
        return langchain.hub.pull(self.langchain_hub_prompt, api_key=self.langchain_api_key)
