from langchain.docstore.document import Document
from langchain_experimental.text_splitter import SemanticChunker

from langflow.base.textsplitters.model import LCTextSplitterComponent
from langflow.io import (
    DropdownInput,
    FloatInput,
    HandleInput,
    IntInput,
    MessageTextInput,
    Output,
)
from langflow.schema import Data


class SemanticTextSplitterComponent(LCTextSplitterComponent):
    """Split text into semantically meaningful chunks using semantic similarity."""

    display_name: str = "语义文本分割器"    # "Semantic Text Splitter"
    name: str = "SemanticTextSplitter"
    description: str = "使用语义相似性将文本拆分为语义上有意义的块。"  # "Split text into semantically meaningful chunks using semantic similarity."
    documentation = "https://python.langchain.com/docs/how_to/semantic-chunker/"
    beta = True  # this component is beta because it is imported from langchain_experimental
    icon = "LangChain"

    inputs = [
        HandleInput(
            name="data_inputs",
            display_name="数据输入",  # "Data Inputs"
            info="包含要拆分的文本和元数据的 Data 对象列表。",  # "List of Data objects containing text and metadata to split."
            input_types=["Data"],
            is_list=True,
            required=True,
        ),
        HandleInput(
            name="embeddings",
            display_name="嵌入模型",  # "Embeddings"
            info="用于语义相似性的嵌入模型。必填。",  # "Embeddings model to use for semantic similarity. Required."
            input_types=["Embeddings"],
            is_list=False,
            required=True,
        ),
        DropdownInput(
            name="breakpoint_threshold_type",
            display_name="断点阈值类型",  # "Breakpoint Threshold Type"
            info=(
                "确定断点的方法。选项：'percentile'（百分位数）、"
                "'standard_deviation'（标准差）、'interquartile'（四分位距）。默认为 'percentile'。"  # "Method to determine breakpoints. Options: 'percentile', 'standard_deviation', 'interquartile'. Defaults to 'percentile'."
            ),
            value="percentile",
            options=["percentile", "standard_deviation", "interquartile"],
        ),
        FloatInput(
            name="breakpoint_threshold_amount",
            display_name="断点阈值数量",  # "Breakpoint Threshold Amount"
            info="断点阈值的数值。",  # "Numerical amount for the breakpoint threshold."
            value=0.5,
        ),
        IntInput(
            name="number_of_chunks",
            display_name="块数量",  # "Number of Chunks"
            info="将文本拆分为的块数量。",  # "Number of chunks to split the text into."
            value=5,
        ),
        MessageTextInput(
            name="sentence_split_regex",
            display_name="句子分割正则表达式",  # "Sentence Split Regex"
            info="用于分割句子的正则表达式。可选。",  # "Regular expression to split sentences. Optional."
            value="",
            advanced=True,
        ),
        IntInput(
            name="buffer_size",
            display_name="缓冲区大小",  # "Buffer Size"
            info="缓冲区的大小。",  # "Size of the buffer."
            value=0,
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="块", name="chunks", method="split_text"),  # "Chunks"
    ]

    def _docs_to_data(self, docs: list[Document]) -> list[Data]:
        """Convert a list of Document objects to Data objects."""
        return [Data(text=doc.page_content, data=doc.metadata) for doc in docs]

    def split_text(self) -> list[Data]:
        """Split the input data into semantically meaningful chunks."""
        try:
            embeddings = getattr(self, "embeddings", None)
            if embeddings is None:
                error_msg = "需要一个嵌入模型用于语义文本分割器。"  # "An embeddings model is required for SemanticTextSplitter."
                raise ValueError(error_msg)

            if not self.data_inputs:
                error_msg = "数据输入不能为空。"  # "Data inputs cannot be empty."
                raise ValueError(error_msg)

            documents = []
            for _input in self.data_inputs:
                if isinstance(_input, Data):
                    documents.append(_input.to_lc_document())
                else:
                    error_msg = f"无效的数据输入类型：{_input}"  # "Invalid data input type: {_input}"
                    raise TypeError(error_msg)

            if not documents:
                error_msg = "在数据输入中未找到有效的数据对象。"  # "No valid Data objects found in data_inputs."
                raise ValueError(error_msg)

            texts = [doc.page_content for doc in documents]
            metadatas = [doc.metadata for doc in documents]

            splitter_params = {
                "embeddings": embeddings,
                "breakpoint_threshold_type": self.breakpoint_threshold_type or "percentile",
                "breakpoint_threshold_amount": self.breakpoint_threshold_amount,
                "number_of_chunks": self.number_of_chunks,
                "buffer_size": self.buffer_size,
            }

            if self.sentence_split_regex:
                splitter_params["sentence_split_regex"] = self.sentence_split_regex

            splitter = SemanticChunker(**splitter_params)
            docs = splitter.create_documents(texts, metadatas=metadatas)

            data = self._docs_to_data(docs)
            self.status = data

        except Exception as e:
            error_msg = f"语义拆分过程中发生错误：{e}"  # "An error occurred during semantic splitting: {e}"
            raise RuntimeError(error_msg) from e

        else:
            return data
