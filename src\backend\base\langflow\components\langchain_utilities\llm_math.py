from langchain.chains import <PERSON><PERSON><PERSON><PERSON>hain

from langflow.base.chains.model import <PERSON><PERSON>hainComponent
from langflow.field_typing import Message
from langflow.inputs import HandleInput, MultilineInput
from langflow.template import Output


class LLMMathChainComponent(LCChainComponent):
    display_name = "LLM数学计算链"  # "LLM Math Chain"
    description = "解释提示并执行 Python 代码进行数学计算的链。"  # "Chain that interprets a prompt and executes python code to do math."
    documentation = "https://python.langchain.com/docs/modules/chains/additional/llm_math"
    name = "LLMMathChain"
    legacy: bool = True
    icon = "LangChain"
    inputs = [
        MultilineInput(
            name="input_value",
            display_name="输入",  # "Input"
            info="传递给链的输入值。",  # "The input value to pass to the chain."
            required=True,
        ),
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            input_types=["LanguageModel"],
            required=True,
        ),
    ]

    outputs = [Output(display_name="消息", name="text", method="invoke_chain")]  # "Message"

    def invoke_chain(self) -> Message:
        chain = LLMMathChain.from_llm(llm=self.llm)
        response = chain.invoke(
            {chain.input_key: self.input_value},
            config={"callbacks": self.get_langchain_callbacks()},
        )
        result = response.get(chain.output_key, "")
        result = str(result)
        self.status = result
        return Message(text=result)
