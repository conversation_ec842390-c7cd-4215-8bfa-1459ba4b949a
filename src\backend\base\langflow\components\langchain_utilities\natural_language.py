from typing import Any

from langchain_text_splitters import NLTKTextSplitter, TextSplitter

from langflow.base.textsplitters.model import LCTextSplitterComponent
from langflow.inputs import DataInput, IntInput, MessageTextInput
from langflow.utils.util import unescape_string


class NaturalLanguageTextSplitterComponent(LCTextSplitterComponent):
    display_name = "自然语言文本分割器"  # "Natural Language Text Splitter"
    description = "基于自然语言边界拆分文本，并针对指定语言进行了优化。"  # "Split text based on natural language boundaries, optimized for a specified language."
    documentation = (
        "https://python.langchain.com/v0.1/docs/modules/data_connection/document_transformers/split_by_token/#nltk"
    )
    name = "NaturalLanguageTextSplitter"
    icon = "LangChain"
    inputs = [
        IntInput(
            name="chunk_size",
            display_name="块大小",  # "Chunk Size"
            info="拆分后每个块的最大字符数。",  # "The maximum number of characters in each chunk after splitting."
            value=1000,
        ),
        IntInput(
            name="chunk_overlap",
            display_name="块重叠",  # "Chunk Overlap"
            info="连续块之间重叠的字符数。",  # "The number of characters that overlap between consecutive chunks."
            value=200,
        ),
        DataInput(
            name="data_input",
            display_name="输入",  # "Input"
            info="要拆分的文本数据。",  # "The text data to be split."
            input_types=["Document", "Data"],
            required=True,
        ),
        MessageTextInput(
            name="separator",
            display_name="分隔符",  # "Separator"
            info='拆分文本时使用的分隔符字符。\n如果留空，默认为 "\\n\\n"。',  # 'The character(s) to use as a delimiter when splitting text.\nDefaults to "\\n\\n" if left empty.'
        ),
        MessageTextInput(
            name="language",
            display_name="语言",  # "Language"
            info='文本的语言。默认为 "English"。支持多种语言以更好地识别文本边界。',  # 'The language of the text. Default is "English". Supports multiple languages for better text boundary recognition.'
        ),
    ]

    def get_data_input(self) -> Any:
        return self.data_input

    def build_text_splitter(self) -> TextSplitter:
        separator = unescape_string(self.separator) if self.separator else "\n\n"
        return NLTKTextSplitter(
            language=self.language.lower() if self.language else "english",
            separator=separator,
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
        )
