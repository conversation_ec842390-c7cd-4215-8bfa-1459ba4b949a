from langchain.agents import create_openai_tools_agent
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, PromptTemplate

from langflow.base.agents.agent import LCToolsAgentComponent
from langflow.inputs import MultilineInput
from langflow.inputs.inputs import DataInput, HandleInput
from langflow.schema import Data


class OpenAIToolsAgentComponent(LCToolsAgentComponent):
    display_name: str = "OpenAI 工具代理"
    description: str = "通过 openai-tools 使用工具的代理。"  # "Agent that uses tools via openai-tools."
    icon = "LangChain"
    name = "OpenAIToolsAgent"

    inputs = [
        *LCToolsAgentComponent._base_inputs,
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            input_types=["LanguageModel", "ToolEnabledLanguageModel"],
            required=True,
        ),
        MultilineInput(
            name="system_prompt",
            display_name="系统提示",  # "System Prompt"
            info="代理的系统提示。",  # "System prompt for the agent."
            value="你是一个乐于助人的助手",  # "You are a helpful assistant"
        ),
        MultilineInput(
            name="user_prompt",
            display_name="提示",  # "Prompt"
            info="此提示必须包含 'input' 键。",  # "This prompt must contain 'input' key."
            value="{input}",
        ),
        DataInput(
            name="chat_history",
            display_name="聊天记录",  # "Chat History"
            is_list=True,
            advanced=True,
        ),
    ]

    def get_chat_history_data(self) -> list[Data] | None:
        return self.chat_history

    def create_agent_runnable(self):
        if "input" not in self.user_prompt:
            msg = "提示必须包含 'input' 键。"  # "Prompt must contain 'input' key."
            raise ValueError(msg)
        messages = [
            ("system", self.system_prompt),  # 系统提示  # "System Prompt"
            ("placeholder", "{chat_history}"),  # 聊天记录占位符  # "Chat History Placeholder"
            HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=["input"], template=self.user_prompt)),
            ("placeholder", "{agent_scratchpad}"),  # 代理草稿占位符  # "Agent Scratchpad Placeholder"
        ]
        prompt = ChatPromptTemplate.from_messages(messages)
        return create_openai_tools_agent(self.llm, self.tools, prompt)
