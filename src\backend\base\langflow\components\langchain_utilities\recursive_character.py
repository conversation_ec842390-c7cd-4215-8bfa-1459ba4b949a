from typing import Any

from langchain_text_splitters import RecursiveCharacterTextSplitter, TextSplitter

from langflow.base.textsplitters.model import LCTextSplitterComponent
from langflow.inputs.inputs import DataInput, IntInput, MessageTextInput
from langflow.utils.util import unescape_string


class RecursiveCharacterTextSplitterComponent(LCTextSplitterComponent):
    display_name: str = "递归字符文本分割器"  # "Recursive Character Text Splitter"  
    description: str = "尝试将所有相关文本保持在一起的方式拆分文本。"  # "Split text trying to keep all related text together."
    documentation: str = "https://docs.langflow.org/components-processing"
    name = "RecursiveCharacterTextSplitter"
    icon = "LangChain"

    inputs = [
        IntInput(
            name="chunk_size",
            display_name="块大小",  # "Chunk Size"
            info="每个块的最大长度。",  # "The maximum length of each chunk."
            value=1000,
        ),
        IntInput(
            name="chunk_overlap",
            display_name="块重叠",  # "Chunk Overlap"
            info="块之间的重叠量。",  # "The amount of overlap between chunks."
            value=200,
        ),
        DataInput(
            name="data_input",
            display_name="输入",  # "Input"
            info="要拆分的文本。",  # "The texts to split."
            input_types=["Document", "Data"],
            required=True,
        ),
        MessageTextInput(
            name="separators",
            display_name="分隔符",  # "Separators"
            info='用于拆分的字符。\n如果留空，默认为 ["\\n\\n", "\\n", " ", ""]。',  # 'The characters to split on.\nIf left empty defaults to ["\\n\\n", "\\n", " ", ""].'
            is_list=True,
        ),
    ]

    def get_data_input(self) -> Any:
        return self.data_input

    def build_text_splitter(self) -> TextSplitter:
        if not self.separators:
            separators: list[str] | None = None
        else:
            # 检查分隔符列表是否包含转义字符
            # 如果有转义字符，则取消转义它们
            # "Check if the separators list has escaped characters"
            # "If there are escaped characters, unescape them"
            separators = [unescape_string(x) for x in self.separators]

        return RecursiveCharacterTextSplitter(
            separators=separators,
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
        )
