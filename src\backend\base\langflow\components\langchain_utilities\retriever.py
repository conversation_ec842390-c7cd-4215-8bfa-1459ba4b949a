from langchain_core.tools import create_retriever_tool

from langflow.custom import CustomComponent
from langflow.field_typing import BaseRetriever, Tool
from langflow.io import (
    Output,
)


class RetrieverToolComponent(CustomComponent):
    display_name = "检索器工具"  # "Retriever Tool"
    description = "用于与检索器交互的工具"  # "Tool for interacting with retriever"
    name = "RetrieverTool"
    legacy = True
    icon = "LangChain"

    def build_config(self):
        return {
            "retriever": {
                "display_name": "检索器",  # "Retriever"
                "info": "用于交互的检索器",  # "Retriever to interact with"
                "type": BaseRetriever,
                "input_types": ["Retriever"],
            },
            "name": {
                "display_name": "名称",  # "Name"
                "info": "工具的名称",  # "Name of the tool"
            },
            "description": {
                "display_name": "描述",  # "Description"
                "info": "工具的描述",  # "Description of the tool"
            },
        }
    
    outputs = [
        Output(display_name="工具", name="tool", types=["Tool"], selected="Tool"),  # "Tool"
    ]

    def build(self, retriever: BaseRetriever, name: str, description: str, **kwargs) -> Tool:
        _ = kwargs
        return create_retriever_tool(
            retriever=retriever,
            name=name,
            description=description,
        )
