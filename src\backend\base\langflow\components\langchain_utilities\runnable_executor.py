from langchain.agents import AgentExecutor

from langflow.custom import Component
from langflow.inputs import BoolInput, HandleInput, MessageTextInput
from langflow.schema.message import Message
from langflow.template import Output


class RunnableExecComponent(Component):
    description = "执行一个可运行对象。它会尝试猜测输入和输出键。"  # "Execute a runnable. It will try to guess the input and output keys."
    display_name = "可运行对象执行器"  # "Runnable Executor"
    name = "RunnableExecutor"
    beta: bool = True
    icon = "LangChain"

    inputs = [
        MessageTextInput(name="input_value", display_name="输入", required=True),  # "Input"
        HandleInput(
            name="runnable",
            display_name="代理执行器",  # "Agent Executor"
            input_types=["Chain", "AgentExecutor", "Agent", "Runnable"],
            required=True,
        ),
        MessageTextInput(
            name="input_key",
            display_name="输入键",  # "Input Key"
            value="input",
            advanced=True,
        ),
        MessageTextInput(
            name="output_key",
            display_name="输出键",  # "Output Key"
            value="output",
            advanced=True,
        ),
        BoolInput(
            name="use_stream",
            display_name="流式传输",  # "Stream"
            value=False,
        ),
    ]

    outputs = [
        Output(
            display_name="消息",  # "Message"
            name="text",
            method="build_executor",
        ),
    ]

    def get_output(self, result, input_key, output_key):
        """根据指定的输入和输出键，从结果字典中检索输出值。

        Args:
            result (dict): 包含输出值的结果字典。  # "The result dictionary containing the output value."
            input_key (str): 用于从结果字典中检索输入值的键。  # "The key used to retrieve the input value from the result dictionary."
            output_key (str): 用于从结果字典中检索输出值的键。  # "The key used to retrieve the output value from the result dictionary."

        Returns:
            tuple: 包含输出值和状态消息的元组。  # "A tuple containing the output value and the status message."
        """
        possible_output_keys = ["answer", "response", "output", "result", "text"]
        status = ""
        result_value = None

        if output_key in result:
            result_value = result.get(output_key)
        elif len(result) == 2 and input_key in result:  # noqa: PLR2004
            # 从结果字典中获取另一个键
            # "Get the other key from the result dict"
            other_key = next(k for k in result if k != input_key)
            if other_key == output_key:
                result_value = result.get(output_key)
            else:
                status += f"警告：输出键不是 '{output_key}'。输出键是 '{other_key}'。"  # "Warning: The output key is not '{output_key}'. The output key is '{other_key}'."
                result_value = result.get(other_key)
        elif len(result) == 1:
            result_value = next(iter(result.values()))
        elif any(k in result for k in possible_output_keys):
            for key in possible_output_keys:
                if key in result:
                    result_value = result.get(key)
                    status += f"输出键：'{key}'。"  # "Output key: '{key}'."
                    break
            if result_value is None:
                result_value = result
                status += f"警告：输出键不是 '{output_key}'。"  # "Warning: The output key is not '{output_key}'."
        else:
            result_value = result
            status += f"警告：输出键不是 '{output_key}'。"  # "Warning: The output key is not '{output_key}'."

        return result_value, status

    def get_input_dict(self, runnable, input_key, input_value):
        """返回包含输入键值对的字典。

        Args:
            runnable: 可运行对象。  # "The runnable object."
            input_key: 输入值的键。  # "The key for the input value."
            input_value: 输入键的值。  # "The value for the input key."

        Returns:
            input_dict: 包含输入键值对的字典。  # "A dictionary containing the input key-value pair."
            status: 指示输入键是否不在可运行对象的输入键中的状态消息。  # "A status message indicating if the input key is not in the runnable's input keys."
        """
        input_dict = {}
        status = ""
        if hasattr(runnable, "input_keys"):
            # 检查输入键是否在可运行对象的输入键中
            # "Check if input_key is in the runnable's input_keys"
            if input_key in runnable.input_keys:
                input_dict[input_key] = input_value
            else:
                input_dict = dict.fromkeys(runnable.input_keys, input_value)
                status = f"警告：输入键不是 '{input_key}'。输入键是 '{runnable.input_keys}'。"  # "Warning: The input key is not '{input_key}'. The input key is '{runnable.input_keys}'."
        return input_dict, status

    async def build_executor(self) -> Message:
        input_dict, status = self.get_input_dict(self.runnable, self.input_key, self.input_value)
        if not isinstance(self.runnable, AgentExecutor):
            msg = "可运行对象必须是 AgentExecutor。"  # "The runnable must be an AgentExecutor."
            raise TypeError(msg)

        if self.use_stream:
            return self.astream_events(input_dict)
        result = await self.runnable.ainvoke(input_dict)
        result_value, status_ = self.get_output(result, self.input_key, self.output_key)
        status += status_
        status += f"\n\n输出：{result_value}\n\n原始输出：{result}"  # "Output: {result_value}\n\nRaw Output: {result}"
        self.status = status
        return result_value

    async def astream_events(self, runnable_input):
        async for event in self.runnable.astream_events(runnable_input, version="v1"):
            if event.get("event") != "on_chat_model_stream":
                continue

            yield event.get("data").get("chunk")
