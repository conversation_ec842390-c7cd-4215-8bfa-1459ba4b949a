from spider.spider import Spider

from langflow.base.langchain_utilities.spider_constants import MODES
from langflow.custom import Component
from langflow.io import (
    BoolInput,
    DictInput,
    DropdownInput,
    IntInput,
    Output,
    SecretStrInput,
    StrInput,
)
from langflow.schema import Data


class SpiderTool(Component):
    display_name: str = "蜘蛛网页爬虫抓取"  # "Spider Web Crawler and Scraper"
    description: str = "Spider API 用于网络爬取和抓取。"  # "Spider API for web crawling and scraping."
    output_types: list[str] = ["Document"]
    documentation: str = "https://spider.cloud/docs/api"

    inputs = [
        SecretStrInput(
            name="spider_api_key",
            display_name="Spider API 密钥",  # "Spider API Key"
            required=True,
            password=True,
            info="Spider API 密钥，可从 https://spider.cloud 获取。",  # "The Spider API Key, get it from https://spider.cloud"
        ),
        StrInput(
            name="url",
            display_name="URL",
            required=True,
            info="要抓取或爬取的 URL。",  # "The URL to scrape or crawl"
        ),
        DropdownInput(
            name="mode",
            display_name="模式",  # "Mode"
            required=True,
            options=MODES,
            value=MODES[0],
            info="操作模式：抓取或爬取。",  # "The mode of operation: scrape or crawl"
        ),
        IntInput(
            name="limit",
            display_name="限制",  # "Limit"
            info="每个网站允许爬取的最大页面数。设置为 0 表示爬取所有页面。",  # "The maximum amount of pages allowed to crawl per website. Set to 0 to crawl all pages."
            advanced=True,
        ),
        IntInput(
            name="depth",
            display_name="深度",  # "Depth"
            info="爬取的最大深度限制。如果为 0，则不应用限制。",  # "The crawl limit for maximum depth. If 0, no limit will be applied."
            advanced=True,
        ),
        StrInput(
            name="blacklist",
            display_name="黑名单",  # "Blacklist"
            info="不想爬取的路径黑名单。使用正则表达式模式。",  # "Blacklist paths that you do not want to crawl. Use Regex patterns."
            advanced=True,
        ),
        StrInput(
            name="whitelist",
            display_name="白名单",  # "Whitelist"
            info="想要爬取的路径白名单，忽略所有其他路由。使用正则表达式模式。",  # "Whitelist paths that you want to crawl, ignoring all other routes. Use Regex patterns."
            advanced=True,
        ),
        BoolInput(
            name="readability",
            display_name="使用可读性",  # "Use Readability"
            info="使用可读性预处理内容以便阅读。",  # "Use readability to pre-process the content for reading."
            advanced=True,
        ),
        IntInput(
            name="request_timeout",
            display_name="请求超时",  # "Request Timeout"
            info="请求的超时时间（以秒为单位）。",  # "Timeout for the request in seconds."
            advanced=True,
        ),
        BoolInput(
            name="metadata",
            display_name="元数据",  # "Metadata"
            info="在响应中包含元数据。",  # "Include metadata in the response."
            advanced=True,
        ),
        DictInput(
            name="params",
            display_name="附加参数",  # "Additional Parameters"
            info="传递给 API 的附加参数。如果提供，将忽略其他输入。",  # "Additional parameters to pass to the API. If provided, other inputs will be ignored."
        ),
    ]

    outputs = [
        Output(display_name="Markdown", name="content", method="crawl"),
    ]

    def crawl(self) -> list[Data]:
        if self.params:
            parameters = self.params["data"]
        else:
            parameters = {
                "limit": self.limit or None,
                "depth": self.depth or None,
                "blacklist": self.blacklist or None,
                "whitelist": self.whitelist or None,
                "readability": self.readability,
                "request_timeout": self.request_timeout or None,
                "metadata": self.metadata,
                "return_format": "markdown",
            }

        app = Spider(api_key=self.spider_api_key)
        if self.mode == "scrape":
            parameters["limit"] = 1
            result = app.scrape_url(self.url, parameters)
        elif self.mode == "crawl":
            result = app.crawl_url(self.url, parameters)
        else:
            msg = f"无效的模式：{self.mode}。必须是 'scrape' 或 'crawl'。"  # "Invalid mode: {self.mode}. Must be 'scrape' or 'crawl'."
            raise ValueError(msg)

        records = []

        for record in result:
            if self.metadata:
                records.append(
                    Data(
                        data={
                            "content": record["content"],
                            "url": record["url"],
                            "metadata": record["metadata"],
                        }
                    )
                )
            else:
                records.append(Data(data={"content": record["content"], "url": record["url"]}))
        return records


class SpiderToolError(Exception):
    """SpiderTool 错误。"""  # "SpiderTool error."
