from langchain_community.utilities.sql_database import SQLDatabase
from sqlalchemy import create_engine
from sqlalchemy.pool import StaticPool

from langflow.custom import Component
from langflow.io import (
    Output,
    StrInput,
)


class SQLDatabaseComponent(Component):
    display_name = "SQL数据库"  # "SQL Database"
    description = "SQL 数据库"  # "SQL Database"
    name = "SQLDatabase"
    icon = "LangChain"

    inputs = [
        StrInput(
            name="uri",
            display_name="URI",  # "URI"
            info="数据库的 URI。",  # "URI to the database."
            required=True,
        ),
    ]

    outputs = [
        Output(
            display_name="SQL 数据库",  # "SQLDatabase"
            name="SQLDatabase",
            method="build_sqldatabase",
        ),
    ]

    def clean_up_uri(self, uri: str) -> str:
        if uri.startswith("postgres://"):
            uri = uri.replace("postgres://", "postgresql://")
        return uri.strip()

    def build_sqldatabase(self) -> SQLDatabase:
        uri = self.clean_up_uri(self.uri)
        # 使用 SQLAlchemy 和 StaticPool 创建引擎
        # "Create an engine using SQLAlchemy with StaticPool"
        engine = create_engine(uri, poolclass=StaticPool)
        return SQLDatabase(engine)
