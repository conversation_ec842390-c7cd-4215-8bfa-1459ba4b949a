from typing import TYPE_CHECKING

from langchain.chains import create_sql_query_chain
from langchain_core.prompts import PromptTemplate

from langflow.base.chains.model import L<PERSON>hainComponent
from langflow.field_typing import Message
from langflow.inputs import HandleInput, IntInput, MultilineInput
from langflow.template import Output

if TYPE_CHECKING:
    from langchain_core.runnables import Runnable


class SQLGeneratorComponent(LCChainComponent):
    display_name = "自然语言到 SQL"  # "Natural Language to SQL"
    description = "从自然语言生成 SQL。"  # "Generate SQL from natural language."
    name = "SQLGenerator"
    legacy: bool = True
    icon = "LangChain"

    inputs = [
        MultilineInput(
            name="input_value",
            display_name="输入",  # "Input"
            info="传递给链的输入值。",  # "The input value to pass to the chain."
            required=True,
        ),
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            input_types=["LanguageModel"],
            required=True,
        ),
        HandleInput(
            name="db",
            display_name="SQL 数据库",  # "SQLDatabase"
            input_types=["SQLDatabase"],
            required=True,
        ),
        IntInput(
            name="top_k",
            display_name="Top K",
            info="每个 SELECT 语句返回的结果数量。",  # "The number of results per select statement to return."
            value=5,
        ),
        MultilineInput(
            name="prompt",
            display_name="提示",  # "Prompt"
            info="提示必须包含 `{question}`。",  # "The prompt must contain `{question}`."
        ),
    ]

    outputs = [Output(display_name="消息", name="text", method="invoke_chain")]  # "Message"

    def invoke_chain(self) -> Message:
        prompt_template = PromptTemplate.from_template(template=self.prompt) if self.prompt else None

        if self.top_k < 1:
            msg = "Top K 必须大于 0。"  # "Top K must be greater than 0."
            raise ValueError(msg)

        if not prompt_template:
            sql_query_chain = create_sql_query_chain(llm=self.llm, db=self.db, k=self.top_k)
        else:
            # 检查提示中是否包含 {question}
            # "Check if {question} is in the prompt"
            if "{question}" not in prompt_template.template or "question" not in prompt_template.input_variables:
                msg = "提示必须包含 `{question}` 才能用于从自然语言生成 SQL。"  # "Prompt must contain `{question}` to be used with Natural Language to SQL."
                raise ValueError(msg)
            sql_query_chain = create_sql_query_chain(llm=self.llm, db=self.db, prompt=prompt_template, k=self.top_k)
        query_writer: Runnable = sql_query_chain | {"query": lambda x: x.replace("SQLQuery:", "").strip()}
        response = query_writer.invoke(
            {"question": self.input_value},
            config={"callbacks": self.get_langchain_callbacks()},
        )
        query = response.get("query")
        self.status = query
        return query
