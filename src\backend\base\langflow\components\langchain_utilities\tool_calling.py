from langchain.agents import create_tool_calling_agent
from langchain_core.prompts import ChatPromptTemplate
from langchain.agents.output_parsers.tools import ToolsAgentOutputParser
from langchain_core.runnables import <PERSON>nableLambda
from langchain.schema.output import LLMResult
from langchain_core.outputs import Generation
from langchain_core.runnables import RunnablePassthrough

import re
import json
import logging

from langflow.base.agents.agent import LCToolsAgentComponent
from langflow.custom.custom_component.component import _get_component_toolkit
from langflow.field_typing import Tool
from langflow.inputs import MessageTextInput
from langflow.inputs.inputs import DataInput, HandleInput
from langflow.schema import Data


class ToolCallingAgentComponent(LCToolsAgentComponent):
    display_name: str = "工具调用代理"  # "Tool Calling Agent"
    description: str = "一个设计用于在工作流中无缝使用各种工具的代理。"  # "An agent designed to utilize various tools seamlessly within workflows."
    icon = "LangChain"
    name = "ToolCallingAgent"

    inputs = [
        *LCToolsAgentComponent._base_inputs,
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            input_types=["LanguageModel"],
            required=True,
            info="代理用于有效执行任务的语言模型。",  # "Language model that the agent utilizes to perform tasks effectively."
        ),
        MessageTextInput(
            name="system_prompt",
            display_name="系统提示",  # "System Prompt"
            info="指导代理行为的系统提示。",  # "System prompt to guide the agent's behavior."
            value="你是一个可以使用工具回答问题和执行任务的乐于助人的助手。",  # "You are a helpful assistant that can use tools to answer questions and perform tasks."
        ),
        DataInput(
            name="chat_history",
            display_name="聊天记录",  # "Chat Memory"
            is_list=True,
            advanced=True,
            info="此输入存储聊天记录，使代理能够记住之前的对话。",  # "This input stores the chat history, allowing the agent to remember previous conversations."
        ),
    ]

    def get_chat_history_data(self) -> list[Data] | None:
        return self.chat_history

    def create_agent_runnable(self):
        messages = [
            ("system", "{system_prompt}"),
            ("placeholder", "{chat_history}"),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}"),
        ]
        prompt = ChatPromptTemplate.from_messages(messages)
        self.validate_tool_names()
        try:
            model_name = getattr(self.llm, "model_name", "").lower()
            if not (model_name.startswith("gpt-") or "claude" in model_name or "gemini" in model_name):
                clean_output_runnable = RunnableLambda(lambda x: self.clean_generation_output(x))
                message_formatter = self.format_scratchpad
                return (RunnablePassthrough.assign(agent_scratchpad=lambda x: message_formatter(x["intermediate_steps"])) 
                        | prompt | self.llm | clean_output_runnable | ToolsAgentOutputParser(max_iterations=1))
            return create_tool_calling_agent(self.llm, self.tools or [], prompt)
        except NotImplementedError as e:
            message = f"{self.display_name} 不支持工具调用。请尝试使用兼容的模型。"  # "{self.display_name} does not support tool calling. Please try using a compatible model."
            raise NotImplementedError(message) from e

    async def to_toolkit(self) -> list[Tool]:
        component_toolkit = _get_component_toolkit()
        toolkit = component_toolkit(component=self)
        tools = toolkit.get_tools(callbacks=self.get_langchain_callbacks())
        if hasattr(self, "tools_metadata"):
            tools = toolkit.update_tools_metadata(tools=tools)
        return tools
    
    def format_scratchpad(self, intermediate_steps):
        messages = []
        for action, observation in intermediate_steps:
            # 工具调用转为助手消息（自然语言）
            messages.append({
                "role": "assistant",
                "content": f"调用工具 {action.tool}，参数：{json.dumps(action.tool_input)}"
            })
            # 工具结果转为用户消息（模拟用户反馈）
            messages.append({
                "role": "user",
                "content": f"工具返回结果：{observation}"
            })
        return messages

    def clean_generation_output(self, llm_result: LLMResult) -> LLMResult:
        # 遍历每个 generation 并处理其 message content
        if hasattr(llm_result, "content"):
            content = llm_result.content
        else:
            content = str(llm_result)
        
        # 去除</think>标签和```json标记
        # content = re.sub(r"<think>.*?</think>", "", content, flags=re.DOTALL)
        # content = re.sub(r"```json|```", "", content)
        content = content.split("</think>", 1)[-1]
        content = re.sub(r"```json(.*?)```", lambda m: m.group(1).strip(), content, flags=re.DOTALL)
        content = re.sub(r"```(?!json)[^`]*```", "", content, flags=re.DOTALL)
        
        try:
            # 尝试将content解析为JSON对象
            tool_calls = json.loads(content)
            # 将解析结果放入llm_result的tool_calls属性中
            if not hasattr(llm_result, 'tool_calls'):
                llm_result.tool_calls = []
            llm_result.tool_calls = tool_calls.get("tool_calls", [])
        except json.JSONDecodeError:
            llm_result.content = content
            # 如果解析失败，抛出异常
            logging.warning(f"无法解析JSON内容: {content}")
            
        return llm_result
