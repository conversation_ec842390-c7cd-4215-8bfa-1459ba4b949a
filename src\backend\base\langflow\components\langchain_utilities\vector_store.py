from langchain_core.vectorstores import VectorStoreRetriever

from langflow.custom import CustomComponent
from langflow.field_typing import VectorStore
from langflow.io import (
    Output,
)

class VectoStoreRetrieverComponent(CustomComponent):
    display_name = "向量存储检索器"  # "Vector Store Retriever"
    description = "向量存储检索器"  # "A vector store retriever"
    name = "VectorStoreRetriever"
    legacy: bool = True
    icon = "LangChain"

    def build_config(self):
        return {
            "vectorstore": {
                "display_name": "向量存储",  # "Vector Store"
                "type": VectorStore,
            },
        }
    
    outputs = [
        Output(display_name="向量存储检索器", name="vectorStoreRetriever", types=["VectorStoreRetriever"], selected="VectorStoreRetriever"),  # "VectorStoreRetriever"
    ]

    def build(self, vectorstore: VectorStore) -> VectorStoreRetriever:
        return vectorstore.as_retriever()
