from langchain.agents.agent_toolkits.vectorstore.toolkit import VectorStoreInfo

from langflow.custom import Component
from langflow.inputs import HandleInput, MessageTextInput, MultilineInput
from langflow.template import Output


class VectorStoreInfoComponent(Component):
    display_name = "向量存储信息"  # "Vector Store Info"
    description = "关于向量存储的信息。"  # "Information about a VectorStore"
    name = "VectorStoreInfo"
    legacy: bool = True
    icon = "LangChain"

    inputs = [
        MessageTextInput(
            name="vectorstore_name",
            display_name="名称",  # "Name"
            info="向量存储的名称。",  # "Name of the VectorStore"
            required=True,
        ),
        MultilineInput(
            name="vectorstore_description",
            display_name="描述",  # "Description"
            info="向量存储的描述。",  # "Description of the VectorStore"
            required=True,
        ),
        HandleInput(
            name="input_vectorstore",
            display_name="向量存储",  # "Vector Store"
            input_types=["VectorStore"],
            required=True,
        ),
    ]

    outputs = [
        Output(display_name="向量存储信息", name="info", method="build_info"),  # "Vector Store Info"
    ]

    def build_info(self) -> VectorStoreInfo:
        self.status = {
            "name": self.vectorstore_name,
            "description": self.vectorstore_description,
        }
        return VectorStoreInfo(
            vectorstore=self.input_vectorstore,
            description=self.vectorstore_description,
            name=self.vectorstore_name,
        )
