from langchain.agents import create_xml_agent
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, PromptTemplate

from langflow.base.agents.agent import LCToolsAgentComponent
from langflow.inputs import MultilineInput
from langflow.inputs.inputs import DataInput, HandleInput
from langflow.schema import Data


class XMLAgentComponent(LCToolsAgentComponent):
    display_name: str = "XML代理"  # "XML Agent"
    description: str = "一个使用工具格式化指令为 XML 的代理。"  # "Agent that uses tools formatting instructions as xml to the Language Model."
    icon = "LangChain"
    beta = True
    name = "XMLAgent"
    inputs = [
        *LCToolsAgentComponent._base_inputs,
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            input_types=["LanguageModel"],
            required=True,
            info="代理用于有效执行任务的语言模型。",  # "Language model that the agent utilizes to perform tasks effectively."
        ),
        DataInput(
            name="chat_history",
            display_name="聊天记录",  # "Chat History"
            is_list=True,
            advanced=True,
        ),
        MultilineInput(
            name="system_prompt",
            display_name="系统提示",  # "System Prompt"
            info="指导代理行为的系统提示。",  # "System prompt to guide the agent's behavior."
            value="""你是一个乐于助人的助手。帮助用户回答任何问题。

你可以使用以下工具：

{tools}

为了使用工具，你可以使用 <tool></tool> 和 <tool_input></tool_input> 标签。然后你会收到一个形式为 <observation></observation> 的响应。

例如，如果你有一个名为 'search' 的工具可以运行谷歌搜索，为了搜索 SF 的天气，你可以回复：

<tool>search</tool><tool_input>SF 的天气</tool_input>

<observation>64 度</observation>

当你完成时，用 <final_answer></final_answer> 标签给出最终答案。例如：

<final_answer>SF 的天气是 64 度</final_answer>

开始！

问题：{input}

{agent_scratchpad}
            """,  # noqa: E501
        ),
        MultilineInput(
            name="user_prompt",
            display_name="提示",  # "Prompt"
            info="此提示必须包含 'input' 键。",  # "This prompt must contain 'input' key."
            value="{input}",
        ),
    ]

    def get_chat_history_data(self) -> list[Data] | None:
        return self.chat_history

    def create_agent_runnable(self):
        if "input" not in self.user_prompt:
            msg = "提示必须包含 'input' 键。"  # "Prompt must contain 'input' key."
            raise ValueError(msg)
        messages = [
            ("system", self.system_prompt),  # 系统提示  # "System Prompt"
            ("placeholder", "{chat_history}"),  # 聊天记录占位符  # "Chat History Placeholder"
            HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=["input"], template=self.user_prompt)),
            ("ai", "{agent_scratchpad}"),  # AI 草稿占位符  # "AI Scratchpad Placeholder"
        ]
        prompt = ChatPromptTemplate.from_messages(messages)
        return create_xml_agent(self.llm, self.tools, prompt)
