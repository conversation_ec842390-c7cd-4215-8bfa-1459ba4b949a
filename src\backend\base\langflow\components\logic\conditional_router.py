import re

from langflow.custom import Component
from langflow.io import BoolInput, DropdownInput, IntInput, MessageInput, MessageTextInput, Output
from langflow.schema.message import Message


class ConditionalRouterComponent(Component):
    display_name = "如果-否则"  # "If-Else"
    description = "根据文本比较将输入消息路由到相应的输出。"  # "Routes an input message to a corresponding output based on text comparison."
    icon = "split"
    name = "ConditionalRouter"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__iteration_updated = False

    inputs = [
        MessageTextInput(
            name="input_text",
            display_name="文本输入",  # "Text Input"
            info="操作的主要文本输入。",  # "The primary text input for the operation."
            required=True,
        ),
        MessageTextInput(
            name="match_text",
            display_name="匹配文本",  # "Match Text"
            info="要比较的文本输入。",  # "The text input to compare against."
            required=True,
        ),
        DropdownInput(
            name="operator",
            display_name="操作符",  # "Operator"
            options=["等于", "不等于", "包含", "以...开头", "以...结尾", "正则表达式"],  # ["equals", "not equals", "contains", "starts with", "ends with", "regex"]
            info="用于比较文本的操作符。",  # "The operator to apply for comparing the texts."
            value="等于",  # "equals"
            real_time_refresh=True,
        ),
        BoolInput(
            name="case_sensitive",
            display_name="区分大小写",  # "Case Sensitive"
            info="如果为 true，则比较将区分大小写。",  # "If true, the comparison will be case sensitive."
            value=False,
        ),
        MessageInput(
            name="message",
            display_name="消息",  # "Message"
            info="要通过任一路由传递的消息。",  # "The message to pass through either route."
        ),
        IntInput(
            name="max_iterations",
            display_name="最大迭代次数",  # "Max Iterations"
            info="条件路由器的最大迭代次数。",  # "The maximum number of iterations for the conditional router."
            value=10,
            advanced=True,
        ),
        DropdownInput(
            name="default_route",
            display_name="默认路由",  # "Default Route"
            options=["true_result", "false_result"],
            info="达到最大迭代次数时要采取的默认路由。",  # "The default route to take when max iterations are reached."
            value="false_result",
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="True", name="true_result", method="true_response"),  # "True"
        Output(display_name="False", name="false_result", method="false_response"),  # "False"
    ]

    def _pre_run_setup(self):
        self.__iteration_updated = False

    def evaluate_condition(self, input_text: str, match_text: str, operator: str, *, case_sensitive: bool) -> bool:
        if not case_sensitive and operator != "正则表达式":  # "regex"
            input_text = input_text.lower()
            match_text = match_text.lower()

        if operator == "等于":  # "equals"
            return input_text == match_text
        if operator == "不等于":  # "not equals"
            return input_text != match_text
        if operator == "包含":  # "contains"
            return match_text in input_text
        if operator == "以...开头":  # "starts with"
            return input_text.startswith(match_text)
        if operator == "以...结尾":  # "ends with"
            return input_text.endswith(match_text)
        if operator == "正则表达式":  # "regex"
            try:
                return bool(re.match(match_text, input_text))
            except re.error:
                return False  # 如果正则表达式无效，则返回 False # "Return False if the regex is invalid"
        return False

    def iterate_and_stop_once(self, route_to_stop: str):
        if not self.__iteration_updated:
            self.update_ctx({f"{self._id}_iteration": self.ctx.get(f"{self._id}_iteration", 0) + 1})
            self.__iteration_updated = True
            if self.ctx.get(f"{self._id}_iteration", 0) >= self.max_iterations and route_to_stop == self.default_route:
                route_to_stop = "true_result" if route_to_stop == "false_result" else "false_result"
            self.stop(route_to_stop)

    def true_response(self) -> Message:
        result = self.evaluate_condition(
            self.input_text, self.match_text, self.operator, case_sensitive=self.case_sensitive
        )
        if result:
            self.status = self.message
            self.iterate_and_stop_once("false_result")
            return self.message
        self.iterate_and_stop_once("true_result")
        return Message(content="")

    def false_response(self) -> Message:
        result = self.evaluate_condition(
            self.input_text, self.match_text, self.operator, case_sensitive=self.case_sensitive
        )
        if not result:
            self.status = self.message
            self.iterate_and_stop_once("true_result")
            return self.message
        self.iterate_and_stop_once("false_result")
        return Message(content="")

    def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:
        if field_name == "operator":
            if field_value == "正则表达式":  # "regex"
                build_config.pop("case_sensitive", None)

            # 确保对于所有其他操作符，case_sensitive 存在
            # "Ensure case_sensitive is present for all other operators"
            elif "case_sensitive" not in build_config:
                case_sensitive_input = next(
                    (input_field for input_field in self.inputs if input_field.name == "case_sensitive"), None
                )
                if case_sensitive_input:
                    build_config["case_sensitive"] = case_sensitive_input.to_dict()
        return build_config
