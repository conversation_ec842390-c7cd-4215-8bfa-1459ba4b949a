from typing import Any

from langflow.custom import Component
from langflow.io import DataInput, DropdownInput, MessageTextInput, Output
from langflow.schema import Data, dotdict


class DataConditionalRouterComponent(Component):
    display_name = "条件"  # "Condition"
    description = "基于应用于指定键的条件（包括布尔验证）路由数据对象。"  # "Route Data object(s) based on a condition applied to a specified key, including boolean validation."
    icon = "split"
    name = "DataConditionalRouter"
    legacy = True

    inputs = [
        DataInput(
            name="data_input",
            display_name="数据输入",  # "Data Input"
            info="要处理的数据对象或数据对象列表。",  # "The Data object or list of Data objects to process."
            is_list=True,
        ),
        MessageTextInput(
            name="key_name",
            display_name="键名称",  # "Key Name"
            info="要检查的数据对象中键的名称。",  # "The name of the key in the Data object(s) to check."
        ),
        DropdownInput(
            name="operator",
            display_name="操作符",  # "Operator"
            options=["等于", "不等于", "包含", "以...开头", "以...结尾", "布尔验证器"],  # ["equals", "not equals", "contains", "starts with", "ends with", "boolean validator"]
            info="用于比较值的操作符。“布尔验证器”将值视为布尔值。",  # "The operator to apply for comparing the values. 'boolean validator' treats the value as a boolean."
            value="等于",  # "equals"
        ),
        MessageTextInput(
            name="compare_value",
            display_name="匹配文本",  # "Match Text"
            info="要比较的值（布尔验证器不使用此值）。",  # "The value to compare against (not used for boolean validator)."
        ),
    ]

    outputs = [
        Output(display_name="True 输出", name="true_output", method="process_data"),  # "True Output"
        Output(display_name="False 输出", name="false_output", method="process_data"),  # "False Output"
    ]

    def compare_values(self, item_value: str, compare_value: str, operator: str) -> bool:
        if operator == "等于":  # "equals"
            return item_value == compare_value
        if operator == "不等于":  # "not equals"
            return item_value != compare_value
        if operator == "包含":  # "contains"
            return compare_value in item_value
        if operator == "以...开头":  # "starts with"
            return item_value.startswith(compare_value)
        if operator == "以...结尾":  # "ends with"
            return item_value.endswith(compare_value)
        if operator == "布尔验证器":  # "boolean validator"
            return self.parse_boolean(item_value)
        return False

    def parse_boolean(self, value):
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in {"true", "1", "yes", "y", "on"}
        return bool(value)

    def validate_input(self, data_item: Data) -> bool:
        if not isinstance(data_item, Data):
            self.status = "输入不是数据对象。"  # "Input is not a Data object."
            return False
        if self.key_name not in data_item.data:
            self.status = f"键 '{self.key_name}' 在数据中未找到。"  # "Key '{self.key_name}' not found in Data."
            return False
        return True

    def process_data(self) -> Data | list[Data]:
        if isinstance(self.data_input, list):
            true_output = []
            false_output = []
            for item in self.data_input:
                if self.validate_input(item):
                    result = self.process_single_data(item)
                    if result:
                        true_output.append(item)
                    else:
                        false_output.append(item)
            self.stop("false_output" if true_output else "true_output")
            return true_output or false_output
        if not self.validate_input(self.data_input):
            return Data(data={"error": self.status})
        result = self.process_single_data(self.data_input)
        self.stop("false_output" if result else "true_output")
        return self.data_input

    def process_single_data(self, data_item: Data) -> bool:
        item_value = data_item.data[self.key_name]
        operator = self.operator

        if operator == "布尔验证器":  # "boolean validator"
            condition_met = self.parse_boolean(item_value)
            condition_description = f"布尔验证 '{self.key_name}'"  # "Boolean validation of '{self.key_name}'"
        else:
            compare_value = self.compare_value
            condition_met = self.compare_values(str(item_value), compare_value, operator)
            condition_description = f"{self.key_name} {operator} {compare_value}"

        if condition_met:
            self.status = f"条件满足：{condition_description}"  # "Condition met: {condition_description}"
            return True
        self.status = f"条件不满足：{condition_description}"  # "Condition not met: {condition_description}"
        return False

    def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):
        if field_name == "operator":
            if field_value == "布尔验证器":  # "boolean validator"
                build_config["compare_value"]["show"] = False
                build_config["compare_value"]["advanced"] = True
                build_config["compare_value"]["value"] = None
            else:
                build_config["compare_value"]["show"] = True
                build_config["compare_value"]["advanced"] = False

        return build_config
