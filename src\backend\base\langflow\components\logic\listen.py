from langflow.custom import CustomComponent
from langflow.schema import Data
from langflow.io import (
    Output,
)

class ListenComponent(CustomComponent):
    display_name = "监听器" # Listen
    description = "一个用于监听通知的组件。"  # "A component to listen for a notification."
    name = "Listen"
    beta: bool = True
    icon = "Radio"

    def build_config(self):
        return {
            "name": {
                "display_name": "名称",  # "Name"
                "info": "要监听的通知名称。",  # "The name of the notification to listen for."
            },
        }

    outputs = [
        Output(display_name="数据", name="data", types=["Data"], selected="Data"),  # "Data"
    ]

    def build(self, name: str) -> Data:
        state = self.get_state(name)
        self._set_successors_ids()
        self.status = state
        return state

    def _set_successors_ids(self):
        self._vertex.is_state = True
        successors = self._vertex.graph.successor_map.get(self._vertex.id, [])
        return successors + self._vertex.graph.activated_vertices
