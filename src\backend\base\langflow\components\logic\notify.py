from langflow.custom import CustomComponent
from langflow.schema import Data
from langflow.io import (
    Output,
)

class NotifyComponent(CustomComponent):
    display_name = "通知"  # "Notify"
    description = "一个用于生成通知以供 Get Notified 组件使用的组件。"  # "A component to generate a notification to Get Notified component."
    icon = "Notify"
    name = "Notify"
    beta: bool = True

    def build_config(self):
        return {
            "name": {
                "display_name": "名称",  # "Name"
                "info": "通知的名称。",  # "The name of the notification."
            },
            "data": {
                "display_name": "数据",  # "Data"
                "info": "要存储的数据。",  # "The data to store."
            },
            "append": {
                "display_name": "追加",  # "Append"
                "info": "如果为 True，记录将追加到通知中。",  # "If True, the record will be appended to the notification."
            },
        }

    outputs = [
        Output(display_name="数据", name="data", types=["Data"], selected="Data"),  # "Data"
    ] 

    def build(self, name: str, *, data: Data | None = None, append: bool = False) -> Data:
        if data and not isinstance(data, Data):
            if isinstance(data, str):
                data = Data(text=data)
            elif isinstance(data, dict):
                data = Data(data=data)
            else:
                data = Data(text=str(data))
        elif not data:
            data = Data(text="")
        if data:
            if append:
                self.append_state(name, data)
            else:
                self.update_state(name, data)
        else:
            self.status = "未提供记录。"  # "No record provided."
        self.status = data
        self._set_successors_ids()
        return data

    def _set_successors_ids(self):
        self._vertex.is_state = True
        successors = self._vertex.graph.successor_map.get(self._vertex.id, [])
        return successors + self._vertex.graph.activated_vertices
