from langflow.custom import Component
from langflow.io import MessageInput
from langflow.schema.message import Message
from langflow.template import Output


class PassMessageComponent(Component):
    display_name = "转发消息"  # "Pass"
    description = "转发输入消息，不做任何更改。"  # "Forwards the input message, unchanged."
    name = "Pass"
    icon = "arrow-right"

    inputs = [
        MessageInput(
            name="input_message",
            display_name="输入消息",  # "Input Message"
            info="要传递的消息。",  # "The message to be passed forward."
            required=True,
        ),
        MessageInput(
            name="ignored_message",
            display_name="忽略的消息",  # "Ignored Message"
            info="第二条要忽略的消息。用于保持连续性的解决方法。",  # "A second message to be ignored. Used as a workaround for continuity."
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="输出消息", name="output_message", method="pass_message"),  # "Output Message"
    ]

    def pass_message(self) -> Message:
        self.status = self.input_message
        return self.input_message
