from urllib import parse

from langchain_community.chat_message_histories.redis import RedisChatMessageHistory

from langflow.base.memory.model import LCChatMemoryComponent
from langflow.field_typing.constants import Memory
from langflow.inputs import IntInput, MessageTextInput, SecretStrInput, StrInput


class RedisIndexChatMemory(LCChatMemoryComponent):
    display_name = "Redis聊天缓存"  # "Redis Chat Memory"
    description = "从 Redis 检索和存储聊天消息。"  # "Retrieves and store chat messages from Redis."
    name = "RedisChatMemory"
    icon = "Redis"

    inputs = [
        StrInput(
            name="host",
            display_name="主机名",  # "Hostname"
            required=True,
            value="localhost",
            info="IP 地址或主机名。",  # "IP address or hostname."
        ),
        IntInput(
            name="port",
            display_name="端口",  # "Port"
            required=True,
            value=6379,
            info="Redis 端口号。",  # "Redis Port Number."
        ),
        StrInput(
            name="database",
            display_name="数据库",  # "Database"
            required=True,
            value="0",
            info="Redis 数据库。",  # "Redis database."
        ),
        MessageTextInput(
            name="username",
            display_name="用户名",  # "Username"
            value="",
            info="Redis 用户名。",  # "The Redis user name."
            advanced=True,
        ),
        SecretStrInput(
            name="password",
            display_name="密码",  # "Password"
            value="",
            info="用户名的密码。",  # "The password for username."
            advanced=True,
        ),
        StrInput(
            name="key_prefix",
            display_name="键前缀",  # "Key Prefix"
            info="键的前缀。",  # "Key prefix."
            advanced=True,
        ),
        MessageTextInput(
            name="session_id",
            display_name="会话 ID",  # "Session ID"
            info="消息的会话 ID。",  # "Session ID for the message."
            advanced=True,
        ),
    ]

    def build_message_history(self) -> Memory:
        kwargs = {}
        password: str | None = self.password
        if self.key_prefix:
            kwargs["key_prefix"] = self.key_prefix
        if password:
            password = parse.quote_plus(password)

        url = f"redis://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        return RedisChatMessageHistory(session_id=self.session_id, url=url, **kwargs)
