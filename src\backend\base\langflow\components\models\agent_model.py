import httpx
from langchain_openai import ChatOpenAI
from pydantic.v1 import SecretStr
from typing_extensions import override
from typing import Dict, Any, List, Optional

from langflow.base.models.model import LCModelComponent
from langflow.field_typing import LanguageModel
from langflow.field_typing.range_spec import RangeSpec
from langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput

class AgentModelComponent(LCModelComponent):
    display_name = "灵童大模型"
    description = "使用灵童配置好的模型生成文本。"
    icon = "Agent"
    name = "wiseAgentModel"
    
    inputs = [
        *LCModelComponent._base_inputs,
        DropdownInput(
            name="name",
            display_name="agent模型",
            advanced=False,
            options=[],
            info="选择要使用的agent模型。",
            refresh_button=True,
            real_time_refresh=True,
            required=True
        ),
        IntInput(
            name="max_tokens",
            display_name="最大 Token 数",
            advanced=True,
            info="生成的最大 token 数。设置为 0 表示无限制。",
            range_spec=RangeSpec(min=0, max=128000),
        ),
        DictInput(
            name="model_kwargs",
            display_name="模型参数",
            advanced=True,
            info="传递给模型的其他关键字参数。",
        ),
        BoolInput(
            name="json_mode",
            display_name="JSON 模式",
            advanced=True,
            info="如果为 True，则无论是否传递 schema，都会输出 JSON。",
        ),
        StrInput(
            name="model_name",
            display_name="模型名称",
            advanced=True,
            info="模型名称",
        ),
        StrInput(
            name="api_base",
            display_name="API 基础地址",
            advanced=True,
            info="API 的基础 URL。默认为 http://wiseagent:8800。",
            value="http://wiseagent:8800",
        ),
        SecretStrInput(
            name="api_key",
            display_name="API 密钥",
            info="用于模型的 API 密钥。",
            advanced=False,
            value="AGENT_API_KEY",
            required=True,
        ),
        SliderInput(
            name="temperature",
            display_name="温度",
            value=0.1,
            range_spec=RangeSpec(min=0, max=1, step=0.01),
            advanced=True,
        ),
        IntInput(
            name="seed",
            display_name="随机种子",
            info="随机种子控制任务的可重复性。",
            advanced=True,
            value=1,
        ),
        IntInput(
            name="max_retries",
            display_name="最大重试次数",
            info="生成时的最大重试次数。",
            advanced=True,
            value=5,
        ),
        IntInput(
            name="timeout",
            display_name="超时时间",
            info="请求 API 完成的超时时间。",
            advanced=True,
            value=700,
        ),
    ]

    def build_model(self) -> LanguageModel:
        api_key = self.api_key
        temperature = self.temperature
        model_name = self.model_name
        max_tokens = self.max_tokens
        model_kwargs = self.model_kwargs or {}
        api_base = self.api_base or "http://wiseagent:8800/v1"
        json_mode = self.json_mode
        seed = self.seed
        max_retries = self.max_retries
        timeout = self.timeout

        api_key_value = SecretStr(api_key).get_secret_value() if api_key else None
        output = ChatOpenAI(
            max_tokens=max_tokens or None,
            model_kwargs=model_kwargs,
            model=model_name,
            base_url=api_base,
            api_key=api_key_value,
            temperature=temperature if temperature is not None else 0.1,
            seed=seed,
            max_retries=max_retries,
            request_timeout=timeout,
        )
        if json_mode:
            output = output.bind(response_format={"type": "json_object"})

        return output

    @override
    async def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None):
        if field_name == "name":

            if not build_config.get("agent_auth_token"):
                raise ValueError("当前组件需要使用agent平台的知识库，未探测到相关服务信息")
            
            if not build_config["name"]["options_metadata"]:
                # 获取模型列表并更新下拉选项
                model_options, models = await self.get_models(build_config['agent_auth_token'])
                build_config["name"]["options"] = model_options
                build_config["name"]["options_metadata"] = models

            build_config["name"]["value"] = field_value
            
            # 根据选择的模型自动设置其他参数
            models_metadata = build_config["name"]["options_metadata"]
            if isinstance(models_metadata, list):
                for model in models_metadata:
                    if model.get("name") == field_value:
                        # 更新API基础地址
                        if "service_url" in model and "api_base" in build_config:
                            build_config["api_base"]["value"] = model["service_url"]
                        
                        # 更新API密钥
                        if "api_key" in model and "api_key" in build_config:
                            build_config["api_key"]["value"] = model["api_key"]
                        
                        # 更新温度
                        if "temperature" in model and "temperature" in build_config:
                            build_config["temperature"]["value"] = model["temperature"]
                        
                        # 更新最大Token数
                        if "max_tokens" in model and "max_tokens" in build_config:
                            build_config["max_tokens"]["value"] = model["max_tokens"]

                        # 更新模型名称
                        if "m_name" in model and "model_name" in build_config:
                            build_config["model_name"]["value"] = model["m_name"]
                        
                        break
            
        return build_config

    async def get_models(self, token: str = None) -> tuple[List[str], Dict[str, Dict[str, Any]]]:
        """从API获取可用模型列表及其配置信息"""
        api_base_url = "http://wiseagent:8800"
        
        # 存储模型配置的字典
        # models_config = {}
        
        try:
            url = f"{api_base_url}/api/llms?current=1&pageSize=50"
            headers = {}
            if token:
                headers["Authorization"] = f"Bearer {token}"
                
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, timeout=20)
                response.raise_for_status()
                data = response.json()
                
                # 假设API返回的格式是包含模型列表的JSON，每个模型包含完整配置
                models = data.get("data", [])
                model_options = []
                
                if isinstance(models, list):
                    for model in models:
                        model_name = model.get("name")
                        if model_name:
                            model_options.append(model_name)
                            
                            # 存储模型的完整配置
                            # models_config[model_id] = {
                            #     "api_base": model.get("api_base", api_base),
                            #     "api_key": model.get("api_key", api_key),
                            #     "temperature": model.get("temperature", 0.1),
                            #     "max_tokens": model.get("max_tokens", 0),
                            #     "json_mode": model.get("json_mode", False),
                            #     "seed": model.get("seed", 1),
                            #     "max_retries": model.get("max_retries", 5),
                            #     "timeout": model.get("timeout", 700),
                            #     "model_kwargs": model.get("model_kwargs", {})
                            # }
                
                return model_options, models
                
        except Exception as e:
            self.status = f"获取模型列表时出错: {str(e)}"
            # 返回默认模型和空配置
            raise ValueError("获取模型列表失败！")
