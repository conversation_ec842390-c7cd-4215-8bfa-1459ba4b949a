import requests
from pydantic.v1 import SecretStr
from typing_extensions import override

from langflow.base.models.model import LCModelComponent
from langflow.field_typing import LanguageModel
from langflow.field_typing.range_spec import RangeSpec
from langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput

DEEPSEEK_MODELS = ["deepseek-chat"]  # ["deepseek-chat"]

class DeepSeekModelComponent(LCModelComponent):
    display_name = "DeepSeek"  # "DeepSeek"
    description = "使用 DeepSeek 大语言模型生成文本。"  # "Generate text using DeepSeek LLMs."
    icon = "DeepSeek"  # "DeepSeek"

    inputs = [
        *LCModelComponent._base_inputs,
        IntInput(
            name="max_tokens",  # "max_tokens"
            display_name="最大生成长度",  # "Max Tokens"
            advanced=True,
            info="生成的最大 token 数量。设置为 0 表示无限制。",  # "Maximum number of tokens to generate. Set to 0 for unlimited."
            range_spec=RangeSpec(min=0, max=128000),
        ),
        DictInput(
            name="model_kwargs",  # "model_kwargs"
            display_name="模型参数",  # "Model Kwargs"
            advanced=True,
            info="传递给模型的额外参数。",  # "Additional keyword arguments to pass to the model."
        ),
        BoolInput(
            name="json_mode",  # "json_mode"
            display_name="JSON 模式",  # "JSON Mode"
            advanced=True,
            info="如果为 True，则无论是否传递 schema，都将输出 JSON 格式。",  # "If True, it will output JSON regardless of passing a schema."
        ),
        DropdownInput(
            name="model_name",  # "model_name"
            display_name="模型名称",  # "Model Name"
            info="选择使用的 DeepSeek 模型",  # "DeepSeek model to use"
            options=DEEPSEEK_MODELS,
            value="deepseek-chat",  # "deepseek-chat"
            refresh_button=True,
        ),
        StrInput(
            name="api_base",  # "api_base"
            display_name="DeepSeek API 基地址",  # "DeepSeek API Base"
            advanced=True,
            info="API 请求的基础 URL，默认为 https://api.deepseek.com",  # "Base URL for API requests. Defaults to https://api.deepseek.com"
            value="https://api.deepseek.com",  # "https://api.deepseek.com"
        ),
        SecretStrInput(
            name="api_key",  # "api_key"
            display_name="DeepSeek API 密钥",  # "DeepSeek API Key"
            info="DeepSeek 的 API 密钥",  # "The DeepSeek API Key"
            advanced=False,
            required=True,
        ),
        SliderInput(
            name="temperature",  # "temperature"
            display_name="随机性 (Temperature)",  # "Temperature"
            info="控制生成结果的随机性",  # "Controls randomness in responses"
            value=1.0,
            range_spec=RangeSpec(min=0, max=2, step=0.01),
            advanced=True,
        ),
        IntInput(
            name="seed",  # "seed"
            display_name="随机种子",  # "Seed"
            info="随机种子用于控制生成结果的可复现性。",  # "The seed controls the reproducibility of the job."
            advanced=True,
            value=1,
        ),
    ]

    def get_models(self) -> list[str]:
        if not self.api_key:
            return DEEPSEEK_MODELS

        url = f"{self.api_base}/models"  # "{self.api_base}/models"
        headers = {"Authorization": f"Bearer {self.api_key}", "Accept": "application/json"}  # "Authorization", "Bearer", "Accept", "application/json"

        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            model_list = response.json()
            return [model["id"] for model in model_list.get("data", [])]  # "id", "data"
        except requests.RequestException as e:
            self.status = f"获取模型列表时出错: {e}"  # "Error fetching models: {e}"
            return DEEPSEEK_MODELS

    @override
    def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None):
        if field_name in {"api_key", "api_base", "model_name"}:  # "api_key", "api_base", "model_name"
            models = self.get_models()
            build_config["model_name"]["options"] = models  # "model_name", "options"
        return build_config

    def build_model(self) -> LanguageModel:
        try:
            from langchain_openai import ChatOpenAI
        except ImportError as e:
            msg = "未安装 langchain-openai。请使用 `pip install langchain-openai` 安装。"  # "langchain-openai not installed. Please install with `pip install langchain-openai`"
            raise ImportError(msg) from e

        api_key = SecretStr(self.api_key).get_secret_value() if self.api_key else None
        output = ChatOpenAI(
            model=self.model_name,
            temperature=self.temperature if self.temperature is not None else 0.1,
            max_tokens=self.max_tokens or None,
            model_kwargs=self.model_kwargs or {},
            base_url=self.api_base,
            api_key=api_key,
            streaming=self.stream if hasattr(self, "stream") else False,
            seed=self.seed,
        )

        if self.json_mode:
            output = output.bind(response_format={"type": "json_object"})  # "type", "json_object"

        return output

    def _get_exception_message(self, e: Exception):
        """从 DeepSeek API 异常中获取错误信息。"""  # "Get message from DeepSeek API exception."
        try:
            from openai import BadRequestError

            if isinstance(e, BadRequestError):
                message = e.body.get("message")  # "message"
                if message:
                    return message
        except ImportError:
            pass
        return None
