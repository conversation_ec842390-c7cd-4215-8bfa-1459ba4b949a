from langchain_openai import ChatOpenAI
from pydantic.v1 import SecretStr

from langflow.base.models.model import LCModelComponent
from langflow.base.models.openai_constants import OPENAI_MODEL_NAMES
from langflow.field_typing import LanguageModel
from langflow.field_typing.range_spec import RangeSpec
from langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput

# 替换硬编码的字符串为中文，并保留英文注释
class OpenAIModelComponent(LCModelComponent):
    display_name = "OpenAI"  # OpenAI
    description = "使用 OpenAI LLMs 生成文本。"  # Generates text using OpenAI LLMs.
    icon = "OpenAI"  # OpenAI
    name = "OpenAIModel"  # OpenAIModel

    inputs = [
        *LCModelComponent._base_inputs,
        IntInput(
            name="max_tokens",
            display_name="最大 Token 数",  # Max Tokens
            advanced=True,
            info="生成的最大 token 数。设置为 0 表示无限制。"  # The maximum number of tokens to generate. Set to 0 for unlimited tokens.
            ,
            range_spec=RangeSpec(min=0, max=128000),
        ),
        DictInput(
            name="model_kwargs",
            display_name="模型参数",  # Model Kwargs
            advanced=True,
            info="传递给模型的其他关键字参数。"  # Additional keyword arguments to pass to the model.
            ,
        ),
        BoolInput(
            name="json_mode",
            display_name="JSON 模式",  # JSON Mode
            advanced=True,
            info="如果为 True，则无论是否传递 schema，都会输出 JSON。"  # If True, it will output JSON regardless of passing a schema.
            ,
        ),
        StrInput(
            name="model_name",
            display_name="模型名称",  # Model Name
            advanced=False,
            info="所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、",
            required=True
        ),
        # DropdownInput(
        #     name="model_name",
        #     display_name="模型名称",  # Model Name
        #     advanced=False,
        #     options=OPENAI_MODEL_NAMES,
        #     value=OPENAI_MODEL_NAMES[1],
        #     combobox=True,
        # ),
        StrInput(
            name="openai_api_base",
            display_name="OpenAI API 基础地址",  # OpenAI API Base
            advanced=True,
            info="OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。"
            "您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。"  # The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.
            ,
        ),
        SecretStrInput(
            name="api_key",
            display_name="OpenAI API 密钥",  # OpenAI API Key
            info="用于 OpenAI 模型的 OpenAI API 密钥。"  # The OpenAI API Key to use for the OpenAI model.
            ,
            advanced=False,
            value="OPENAI_API_KEY",
            required=True,
        ),
        SliderInput(
            name="temperature",
            display_name="温度",  # Temperature
            value=0.1,
            range_spec=RangeSpec(min=0, max=1, step=0.01),
            advanced=True,
        ),
        IntInput(
            name="seed",
            display_name="随机种子",  # Seed
            info="随机种子控制任务的可重复性。"  # The seed controls the reproducibility of the job.
            ,
            advanced=True,
            value=1,
        ),
        IntInput(
            name="max_retries",
            display_name="最大重试次数",  # Max Retries
            info="生成时的最大重试次数。"  # The maximum number of retries to make when generating.
            ,
            advanced=True,
            value=5,
        ),
        IntInput(
            name="timeout",
            display_name="超时时间",  # Timeout
            info="请求 OpenAI 完成 API 的超时时间。"  # The timeout for requests to OpenAI completion API.
            ,
            advanced=True,
            value=700,
        ),
    ]

    def build_model(self) -> LanguageModel:  # type: ignore[type-var]
        openai_api_key = self.api_key
        temperature = self.temperature
        model_name: str = self.model_name
        max_tokens = self.max_tokens
        model_kwargs = self.model_kwargs or {}
        openai_api_base = self.openai_api_base or "https://api.openai.com/v1"
        json_mode = self.json_mode
        seed = self.seed
        max_retries = self.max_retries
        timeout = self.timeout

        api_key = SecretStr(openai_api_key).get_secret_value() if openai_api_key else None
        output = ChatOpenAI(
            max_tokens=max_tokens or None,
            model_kwargs=model_kwargs,
            model=model_name,
            base_url=openai_api_base,
            api_key=api_key,
            temperature=temperature if temperature is not None else 0.1,
            seed=seed,
            max_retries=max_retries,
            request_timeout=timeout,
        )
        if json_mode:
            output = output.bind(response_format={"type": "json_object"})

        return output

    def _get_exception_message(self, e: Exception):
        """Get a message from an OpenAI exception.

        Args:
            e (Exception): The exception to get the message from.

        Returns:
            str: The message from the exception.
        """
        try:
            from openai import BadRequestError
        except ImportError:
            return None
        if isinstance(e, BadRequestError):
            message = e.body.get("message")
            if message:
                return message
        return None
