from collections.abc import Generator
from typing import Any

from langflow.base.io.chat import ChatComponent
from langflow.inputs import BoolInput
from langflow.inputs.inputs import HandleInput
from langflow.io import DropdownInput, MessageTextInput, Output
from langflow.schema.data import Data
from langflow.schema.dataframe import Data<PERSON>rame
from langflow.schema.message import Message
from langflow.schema.properties import Source
from langflow.utils.constants import (
    MESSAGE_SENDER_AI,
    MESSAGE_SENDER_NAME_AI,
    MESSAGE_SENDER_USER,
)


class ChatOutput(ChatComponent):
    display_name = "聊天输出"  # "Chat Output"
    description = "在练习场中显示聊天消息。"  # "Display a chat message in the Playground."
    icon = "MessagesSquare"  # "消息方块"
    name = "ChatOutput"  # ""
    minimized = True

    inputs = [
        HandleInput(
            name="input_value",  # "input_value"
            display_name="文本",  # "Text"
            info="作为输出传递的消息。",  # "Message to be passed as output."
            input_types=["Data", "DataFrame", "Message"],
            required=True,
        ),
        BoolInput(
            name="should_store_message",  # "should_store_message"
            display_name="存储消息",  # "Store Messages"
            info="将消息存储在历史记录中。",  # "Store the message in the history."
            value=True,
            advanced=True,
        ),
        DropdownInput(
            name="sender",  # "sender"
            display_name="发送者类型",  # "Sender Type"
            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],
            value=MESSAGE_SENDER_AI,
            advanced=True,
            info="发送者的类型。",  # "Type of sender."
        ),
        MessageTextInput(
            name="sender_name",  # "sender_name"
            display_name="发送者名称",  # "Sender Name"
            info="发送者的名称。",  # "Name of the sender."
            value=MESSAGE_SENDER_NAME_AI,
            advanced=True,
        ),
        MessageTextInput(
            name="session_id",  # "session_id"
            display_name="会话 ID",  # "Session ID"
            info="聊天的会话 ID。如果为空，将使用当前会话 ID 参数。",  # "The session ID of the chat. If empty, the current session ID parameter will be used."
            advanced=True,
        ),
        MessageTextInput(
            name="data_template",  # "data_template"
            display_name="数据模板",  # "Data Template"
            value="{text}",
            advanced=True,
            info="用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。",  # "Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key."
        ),
        MessageTextInput(
            name="background_color",  # "background_color"
            display_name="背景颜色",  # "Background Color"
            info="图标的背景颜色。",  # "The background color of the icon."
            advanced=True,
        ),
        MessageTextInput(
            name="chat_icon",  # "chat_icon"
            display_name="图标",  # "Icon"
            info="消息的图标。",  # "The icon of the message."
            advanced=True,
        ),
        MessageTextInput(
            name="text_color",  # "text_color"
            display_name="文本颜色",  # "Text Color"
            info="名称的文本颜色。",  # "The text color of the name."
            advanced=True,
        ),
        BoolInput(
            name="clean_data",  # "clean_data"
            display_name="基本清理数据",  # "Basic Clean Data"
            value=True,
            info="是否清理数据。",  # "Whether to clean the data."
            advanced=True,
        ),
    ]
    outputs = [
        Output(
            display_name="消息",  # "Message"
            name="message",
            method="message_response",
        ),
    ]

    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:
        source_dict = {}
        if id_:
            source_dict["id"] = id_
        if display_name:
            source_dict["display_name"] = display_name
        if source:
            # Handle case where source is a ChatOpenAI object
            if hasattr(source, "model_name"):
                source_dict["source"] = source.model_name
            elif hasattr(source, "model"):
                source_dict["source"] = str(source.model)
            else:
                source_dict["source"] = str(source)
        return Source(**source_dict)

    async def message_response(self) -> Message:
        # First convert the input to string if needed
        text = self.convert_to_string()
        # Get source properties
        source, icon, display_name, source_id = self.get_properties_from_source_component()
        background_color = self.background_color
        text_color = self.text_color
        if self.chat_icon:
            icon = self.chat_icon

        # Create or use existing Message object
        if isinstance(self.input_value, Message):
            message = self.input_value
            # Update message properties
            message.text = text
        else:
            message = Message(text=text)

        # Set message properties
        message.sender = self.sender
        message.sender_name = self.sender_name
        message.session_id = self.session_id
        message.flow_id = self.graph.flow_id if hasattr(self, "graph") else None
        message.properties.source = self._build_source(source_id, display_name, source)
        message.properties.icon = icon
        message.properties.background_color = background_color
        message.properties.text_color = text_color

        # Store message if needed
        if self.session_id and self.should_store_message:
            stored_message = await self.send_message(message)
            self.message.value = stored_message
            message = stored_message

        self.status = message
        return message

    def _validate_input(self) -> None:
        """Validate the input data and raise ValueError if invalid."""
        if self.input_value is None:
            msg = "Input data cannot be None"
            raise ValueError(msg)
        if isinstance(self.input_value, list) and not all(
            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value
        ):
            invalid_types = [
                type(item).__name__
                for item in self.input_value
                if not isinstance(item, Message | Data | DataFrame | str)
            ]
            msg = f"Expected Data or DataFrame or Message or str, got {invalid_types}"
            raise TypeError(msg)
        if not isinstance(
            self.input_value,
            Message | Data | DataFrame | str | list | Generator | type(None),
        ):
            type_name = type(self.input_value).__name__
            msg = f"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}"
            raise TypeError(msg)

    def _safe_convert(self, data: Any) -> str:
        """Safely convert input data to string."""
        try:
            if isinstance(data, str):
                return data
            if isinstance(data, Message):
                return data.get_text()
            if isinstance(data, Data):
                if data.get_text() is None:
                    msg = "Empty Data object"
                    raise ValueError(msg)
                return data.get_text()
            if isinstance(data, DataFrame):
                if self.clean_data:
                    # Remove empty rows
                    data = data.dropna(how="all")
                    # Remove empty lines in each cell
                    data = data.replace(r"^\s*$", "", regex=True)
                    # Replace multiple newlines with a single newline
                    data = data.replace(r"\n+", "\n", regex=True)

                # Replace pipe characters to avoid markdown table issues
                processed_data = data.replace(r"\|", r"\\|", regex=True)

                processed_data = processed_data.map(
                    lambda x: str(x).replace("\n", "<br/>") if isinstance(x, str) else x
                )

                return processed_data.to_markdown(index=False)
            return str(data)
        except (ValueError, TypeError, AttributeError) as e:
            msg = f"Error converting data: {e!s}"
            raise ValueError(msg) from e

    def convert_to_string(self) -> str | Generator[Any, None, None]:
        """Convert input data to string with proper error handling."""
        self._validate_input()
        if isinstance(self.input_value, list):
            return "\n".join([self._safe_convert(item) for item in self.input_value])
        if isinstance(self.input_value, Generator):
            return self.input_value
        return self._safe_convert(self.input_value)
