from langflow.base.io.text import TextComponent
from langflow.io import MultilineInput, Output
from langflow.schema.message import Message


class TextOutputComponent(TextComponent):
    display_name = "文本输出"  # "Text Output"
    description = "在练习场中显示文本输出。"  # "Display a text output in the Playground."
    icon = "type"  # ""
    name = "TextOutput"  # ""

    inputs = [
        MultilineInput(
            name="input_value",  # "input_value"
            display_name="文本",  # "Text"
            info="作为输出传递的文本。",  # "Text to be passed as output."
        ),
    ]
    outputs = [
        Output(display_name="消息", name="text", method="text_response"),  # "Message"
    ]

    def text_response(self) -> Message:
        message = Message(
            text=self.input_value,
        )
        self.status = self.input_value
        return message
