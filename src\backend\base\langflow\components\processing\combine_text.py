from langflow.custom import Component
from langflow.io import MessageTextInput, Output
from langflow.schema.message import Message


class CombineTextComponent(Component):
    display_name = "合并文本"  # "Combine Text"
    description = "使用指定的分隔符将两个文本源连接成一个单一的文本块。"  # "Concatenate two text sources into a single text chunk using a specified delimiter."
    icon = "merge"
    name = "CombineText"

    inputs = [
        MessageTextInput(
            name="text1",
            display_name="第一个文本",  # "First Text"
            info="要连接的第一个文本输入。",  # "The first text input to concatenate."
        ),
        MessageTextInput(
            name="text2",
            display_name="第二个文本",  # "Second Text"
            info="要连接的第二个文本输入。",  # "The second text input to concatenate."
        ),
        MessageTextInput(
            name="delimiter",
            display_name="分隔符",  # "Delimiter"
            info="用于分隔两个文本输入的字符串。默认为空格。",  # "A string used to separate the two text inputs. Defaults to a whitespace."
            value=" ",
        ),
    ]

    outputs = [
        Output(display_name="合并后的文本", name="combined_text", method="combine_texts"),  # "Combined Text"
    ]

    def combine_texts(self) -> Message:
        combined = self.delimiter.join([self.text1, self.text2])
        self.status = combined
        return Message(text=combined)
