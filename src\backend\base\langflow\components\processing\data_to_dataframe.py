from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.schema import Data, DataFrame


class DataToDataFrameComponent(Component):
    display_name: str = "数据转数据表"  # "Data to DataFrame"
    description: str = (
"将一个或多个 Data 对象转换为数据表。"  # "Converts one or multiple Data objects into a DataFrame."
        "每个 Data 对象对应一行，`.data` 中的字段成为列，"  # "Each Data object corresponds to one row. Fields from `.data` become columns,"
        "而 `text` 字段被放置在 'text' 列中。"  # "and the `text` field is placed in a 'text' column."
    )
    icon = "table"
    name = "DataToDataFrame"

    inputs = [
        DataInput(
            name="data_list",
            display_name="数据或数据列表",  # "Data or Data List"
            info="一个或多个要转换为数据表的 Data 对象。",  # "One or multiple Data objects to transform into a DataFrame."
            is_list=True,
        ),
    ]

    outputs = [
        Output(
            display_name="数据表",  # "DataFrame"
            name="dataframe",
            method="build_dataframe",
            info="从每个 Data 对象的字段中构建数据表，并添加一个 'text' 列。",  # "Builds a DataFrame from each Data object's fields plus a 'text' column."
        ),
    ]

    def build_dataframe(self) -> DataFrame:
        """Builds a DataFrame from Data objects by combining their fields.

        For each Data object:
          - Merge item.data (dictionary) as columns
          - If item.text is present, add 'text' column

        Returns a DataFrame with one row per Data object.
        """
        data_input = self.data_list

        # If user passed a single Data, it might come in as a single object rather than a list
        if not isinstance(data_input, list):
            data_input = [data_input]

        rows = []
        for item in data_input:
            if not isinstance(item, Data):
                msg = f"预期为 Data 对象，但收到 {type(item)}。"  # "Expected Data objects, got {type(item)} instead."
                raise TypeError(msg)

            # Start with a copy of item.data or an empty dict
            row_dict = dict(item.data) if item.data else {}

            # If the Data object has text, store it under 'text' col
            text_val = item.get_text()
            if text_val:
                row_dict["text"] = text_val

            rows.append(row_dict)

        # Build a DataFrame from these row dictionaries
        df_result = DataFrame(rows)
        self.status = df_result  # store in self.status for logs
        return df_result
