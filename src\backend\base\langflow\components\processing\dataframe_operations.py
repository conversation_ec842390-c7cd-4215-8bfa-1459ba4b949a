from langflow.custom import Component
from langflow.io import BoolInput, DataFrameInput, DropdownInput, IntInput, MessageTextInput, Output, StrInput
from langflow.schema import DataFrame


class DataFrameOperationsComponent(Component):
    display_name = "数据表操作"  # "DataFrame Operations"
    description = "对数据表执行各种操作。"  # "Perform various operations on a DataFrame."
    icon = "table"

    # Available operations
    OPERATION_CHOICES = [
"添加列",  # "Add Column"
        "删除列",  # "Drop Column"
        "过滤",  # "Filter"
        "前几行",  # "Head"
        "重命名列",  # "Rename Column"
        "替换值",  # "Replace Value"
        "选择列",  # "Select Columns"
        "排序",  # "Sort"
        "后几行",  # "Tail"
    ]

    inputs = [
        DataFrameInput(
            name="df",
            display_name="数据表",  # "DataFrame"
            info="要操作的输入数据表。",  # "The input DataFrame to operate on."
        ),
        DropdownInput(
            name="operation",
            display_name="操作",  # "Operation"
            options=OPERATION_CHOICES,
            info="选择要执行的数据表操作。",  # "Select the DataFrame operation to perform."
            real_time_refresh=True,
        ),
        StrInput(
            name="column_name",
            display_name="列名",  # "Column Name"
            info="用于操作的列名。",  # "The column name to use for the operation."
            dynamic=True,
            show=False,
        ),
        MessageTextInput(
            name="filter_value",
            display_name="过滤值",  # "Filter Value"
            info="用于过滤行的值。",  # "The value to filter rows by."
            dynamic=True,
            show=False,
        ),
        BoolInput(
            name="ascending",
            display_name="升序排序",  # "Sort Ascending"
            info="是否按升序排序。",  # "Whether to sort in ascending order."
            dynamic=True,
            show=False,
            value=True,
        ),
        StrInput(
            name="new_column_name",
            display_name="新列名",  # "New Column Name"
            info="重命名或添加列时的新列名。",  # "The new column name when renaming or adding a column."
            dynamic=True,
            show=False,
        ),
        MessageTextInput(
            name="new_column_value",
            display_name="新列值",  # "New Column Value"
            info="用于填充新列的值。",  # "The value to populate the new column with."
            dynamic=True,
            show=False,
        ),
        StrInput(
            name="columns_to_select",
            display_name="选择的列",  # "Columns to Select"
            dynamic=True,
            is_list=True,
            show=False,
        ),
        IntInput(
            name="num_rows",
            display_name="行数",  # "Number of Rows"
            info="要返回的行数（用于前几行/后几行操作）。",  # "Number of rows to return (for head/tail)."
            dynamic=True,
            show=False,
            value=5,
        ),
        MessageTextInput(
            name="replace_value",
            display_name="要替换的值",  # "Value to Replace"
            info="要在列中替换的值。",  # "The value to replace in the column."
            dynamic=True,
            show=False,
        ),
        MessageTextInput(
            name="replacement_value",
            display_name="替换值",  # "Replacement Value"
            info="用于替换的值。",  # "The value to replace with."
            dynamic=True,
            show=False,
        ),
    ]

    outputs = [
        Output(
            display_name="数据表",  # "DataFrame"
            name="output",
            method="perform_operation",
            info="操作后的数据表结果。",  # "The resulting DataFrame after the operation."
        )
    ]

    def update_build_config(self, build_config, field_value, field_name=None):
        # Hide all dynamic fields by default
        dynamic_fields = [
            "column_name",
            "filter_value",
            "ascending",
            "new_column_name",
            "new_column_value",
            "columns_to_select",
            "num_rows",
            "replace_value",
            "replacement_value",
        ]
        for field in dynamic_fields:
            build_config[field]["show"] = False

        # Show relevant fields based on the selected operation
        if field_name == "operation":
            if field_value == "过滤":  # "Filter"
                build_config["column_name"]["show"] = True
                build_config["filter_value"]["show"] = True
            elif field_value == "排序":  # "Sort"
                build_config["column_name"]["show"] = True
                build_config["ascending"]["show"] = True
            elif field_value == "删除列":  # "Drop Column"
                build_config["column_name"]["show"] = True
            elif field_value == "重命名列":  # "Rename Column"
                build_config["column_name"]["show"] = True
                build_config["new_column_name"]["show"] = True
            elif field_value == "添加列":  # "Add Column"
                build_config["new_column_name"]["show"] = True
                build_config["new_column_value"]["show"] = True
            elif field_value == "选择列":  # "Select Columns"
                build_config["columns_to_select"]["show"] = True
            elif field_value in {"前几行", "后几行"}:  # {"Head", "Tail"}
                build_config["num_rows"]["show"] = True
            elif field_value == "替换值":  # "Replace Value"
                build_config["column_name"]["show"] = True
                build_config["replace_value"]["show"] = True
                build_config["replacement_value"]["show"] = True

        return build_config

    def perform_operation(self) -> DataFrame:
        dataframe_copy = self.df.copy()
        operation = self.operation

        if operation == "过滤":  # "Filter"
            return self.filter_rows_by_value(dataframe_copy)
        if operation == "排序":  # "Sort"
            return self.sort_by_column(dataframe_copy)
        if operation == "删除列":  # "Drop Column"
            return self.drop_column(dataframe_copy)
        if operation == "重命名列":  # "Rename Column"
            return self.rename_column(dataframe_copy)
        if operation == "添加列":  # "Add Column"
            return self.add_column(dataframe_copy)
        if operation == "选择列":  # "Select Columns"
            return self.select_columns(dataframe_copy)
        if operation == "前几行":  # "Head"
            return self.head(dataframe_copy)
        if operation == "后几行":  # "Tail"
            return self.tail(dataframe_copy)
        if operation == "替换值":  # "Replace Value"
            return self.replace_values(dataframe_copy)
        msg = f"不支持的操作: {operation}"  # "Unsupported operation: {operation}"

        raise ValueError(msg)

    # Existing methods
    def filter_rows_by_value(self, df: DataFrame) -> DataFrame:
        return DataFrame(df[df[self.column_name] == self.filter_value])

    def sort_by_column(self, df: DataFrame) -> DataFrame:
        return DataFrame(df.sort_values(by=self.column_name, ascending=self.ascending))

    def drop_column(self, df: DataFrame) -> DataFrame:
        return DataFrame(df.drop(columns=[self.column_name]))

    def rename_column(self, df: DataFrame) -> DataFrame:
        return DataFrame(df.rename(columns={self.column_name: self.new_column_name}))

    def add_column(self, df: DataFrame) -> DataFrame:
        df[self.new_column_name] = [self.new_column_value] * len(df)
        return DataFrame(df)

    def select_columns(self, df: DataFrame) -> DataFrame:
        columns = [col.strip() for col in self.columns_to_select]
        return DataFrame(df[columns])

    # New methods
    def head(self, df: DataFrame) -> DataFrame:
        return DataFrame(df.head(self.num_rows))

    def tail(self, df: DataFrame) -> DataFrame:
        return DataFrame(df.tail(self.num_rows))

    def replace_values(self, df: DataFrame) -> DataFrame:
        df[self.column_name] = df[self.column_name].replace(self.replace_value, self.replacement_value)
        return DataFrame(df)
