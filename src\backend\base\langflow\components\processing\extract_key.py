from langflow.custom import Component
from langflow.io import DataInput, Output, StrInput
from langflow.schema import Data


class ExtractDataKeyComponent(Component):
    display_name = "提取键"  # "Extract Key"
    description = (
"从 Data 对象或 Data 对象列表中提取特定键，"  # "Extract a specific key from a Data object or a list of "
"并将提取的值作为 Data 对象返回。"  # "Data objects and return the extracted value(s) as Data object(s)."
    )
    icon = "key"
    name = "ExtractaKey"
    legacy = True

    inputs = [
        DataInput(
            name="data_input",
            display_name="数据输入",  # "Data Input"
            info="要从中提取键的 Data 对象或 Data 对象列表。",  # "The Data object or list of Data objects to extract the key from."
        ),
        StrInput(
            name="key",
            display_name="要提取的键",  # "Key to Extract"
            info="要从 Data 对象中提取的键。",  # "The key in the Data object(s) to extract."
        ),
    ]

    outputs = [
        Output(display_name="提取的数据", name="extracted_data", method="extract_key"),  # "Extracted Data"
    ]

    def extract_key(self) -> Data | list[Data]:
        key = self.key

        if isinstance(self.data_input, list):
            result = []
            for item in self.data_input:
                if isinstance(item, Data) and key in item.data:
                    extracted_value = item.data[key]
                    result.append(Data(data={key: extracted_value}))
            self.status = result
            return result
        if isinstance(self.data_input, Data):
            if key in self.data_input.data:
                extracted_value = self.data_input.data[key]
                result = Data(data={key: extracted_value})
                self.status = result
                return result
            self.status = f"键 '{key}' 未在 Data 对象中找到。"  # "Key '{key}' not found in Data object."
            return Data(data={"error": f"键 '{key}' 未在 Data 对象中找到。"})  # "Key '{key}' not found in Data object."
        self.status = "无效输入。预期为 Data 对象或 Data 对象列表。"  # "Invalid input. Expected Data object or list of Data objects."
        return Data(data={"error": "无效输入。预期为 Data 对象或 Data 对象列表。"})  # "Invalid input. Expected Data object or list of Data objects."
