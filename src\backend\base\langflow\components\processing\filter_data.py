from langflow.custom import Component
from langflow.io import DataInput, MessageTextInput, Output
from langflow.schema import Data


class FilterDataComponent(Component):
    display_name = "过滤数据"  # "Filter Data"
    description = "根据键列表过滤 Data 对象。"  # "Filters a Data object based on a list of keys."
    icon = "filter"
    beta = True
    name = "FilterData"

    inputs = [
        DataInput(
            name="data",
            display_name="数据",  # "Data"
            info="要过滤的 Data 对象。",  # "Data object to filter."
        ),
        MessageTextInput(
            name="filter_criteria",
            display_name="过滤条件",  # "Filter Criteria"
            info="用于过滤的键列表。",  # "List of keys to filter by."
            is_list=True,
        ),
    ]

    outputs = [
        Output(display_name="过滤后的数据", name="filtered_data", method="filter_data"),  # "Filtered Data"
    ]

    def filter_data(self) -> Data:
        filter_criteria: list[str] = self.filter_criteria
        data = self.data.data if isinstance(self.data, Data) else {}

        # Filter the data
        filtered = {key: value for key, value in data.items() if key in filter_criteria}

        # Create a new Data object with the filtered data
        filtered_data = Data(data=filtered)
        self.status = filtered_data
        return filtered_data
