from typing import Any

from langflow.custom import Component
from langflow.io import DataInput, DropdownInput, MessageTextInput, Output
from langflow.schema import Data


class DataFilterComponent(Component):
    display_name = "过滤值"  # "Filter Values"
    description = (
        "根据指定的键、过滤值和比较运算符过滤数据项列表。"  # "Filter a list of data items based on a specified key, filter value,"
        "检查高级选项以选择匹配比较。"  # "and comparison operator. Check advanced options to select match comparison."
    )
    icon = "filter"
    beta = True
    name = "FilterDataValues"

    inputs = [
        DataInput(
            name="input_data",
            display_name="输入数据",  # "Input Data"
            info="要过滤的数据项列表。",  # "The list of data items to filter."
            is_list=True,
        ),
        MessageTextInput(
            name="filter_key",
            display_name="过滤键",  # "Filter Key"
            info="用于过滤的键（例如 'route'）。",  # "The key to filter on (e.g., 'route')."
            value="route",
            input_types=["Data"],
        ),
        MessageTextInput(
            name="filter_value",
            display_name="过滤值",  # "Filter Value"
            info="用于过滤的值（例如 'CMIP'）。",  # "The value to filter by (e.g., 'CMIP')."
            value="CMIP",
            input_types=["Data"],
        ),
        DropdownInput(
            name="operator",
            display_name="比较运算符",  # "Comparison Operator"
            options=["等于", "不等于", "包含", "以...开始", "以...结束"],  # ["equals", "not equals", "contains", "starts with", "ends with"]
            info="用于比较值的运算符。",  # "The operator to apply for comparing the values."
            value="等于",  # "equals"
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="过滤后的数据", name="filtered_data", method="filter_data"),  # "Filtered Data"
    ]

    def compare_values(self, item_value: Any, filter_value: str, operator: str) -> bool:
        if operator == "等于":  # "equals"
            return str(item_value) == filter_value
        if operator == "不等于":  # "not equals"
            return str(item_value) != filter_value
        if operator == "包含":  # "contains"
            return filter_value in str(item_value)
        if operator == "以...开始":  # "starts with"
            return str(item_value).startswith(filter_value)
        if operator == "以...结束":  # "ends with"
            return str(item_value).endswith(filter_value)
        return False

    def filter_data(self) -> list[Data]:
        # 提取输入
        # "Extract inputs"
        input_data: list[Data] = self.input_data
        filter_key: str = self.filter_key.text
        filter_value: str = self.filter_value.text
        operator: str = self.operator

        # 验证输入
        # "Validate inputs"
        if not input_data:
            self.status = "输入数据为空。"  # "Input data is empty."
            return []

        if not filter_key or not filter_value:
            self.status = "过滤键或值缺失。"  # "Filter key or value is missing."
            return input_data

        # 过滤数据
        # "Filter the data"
        filtered_data = []
        for item in input_data:
            if isinstance(item.data, dict) and filter_key in item.data:
                if self.compare_values(item.data[filter_key], filter_value, operator):
                    filtered_data.append(item)
            else:
                self.status = f"警告：某些项没有键 '{filter_key}' 或不是字典。"  # "Warning: Some items don't have the key '{filter_key}' or are not dictionaries."

        self.status = filtered_data
        return filtered_data
