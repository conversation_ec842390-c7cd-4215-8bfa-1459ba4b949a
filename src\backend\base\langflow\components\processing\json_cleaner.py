import json
import unicodedata

from langflow.custom import Component
from langflow.inputs import BoolInput, MessageTextInput
from langflow.schema.message import Message
from langflow.template import Output


class JSONCleaner(Component):
    icon = "braces"
    display_name = "JSON 清理器"  # "JSON Cleaner"
    description = (
"清理由 LLM 生成的混乱且有时不正确的 JSON 字符串，"  # "Cleans the messy and sometimes incorrect JSON strings produced by LLMs "
"使其完全符合 JSON 规范。"  # "so that they are fully compliant with the JSON spec."
    )
    legacy = True
    inputs = [
        MessageTextInput(
            name="json_str",
display_name="JSON 字符串",  # "JSON String"
info="需要清理的 JSON 字符串。",  # "The JSON string to be cleaned."
required=True,
        ),
        BoolInput(
            name="remove_control_chars",
            display_name="移除控制字符",  # "Remove Control Characters"
            info="从 JSON 字符串中移除控制字符。",  # "Remove control characters from the JSON string."
            required=False,
        ),
        BoolInput(
            name="normalize_unicode",
            display_name="规范化 Unicode",  # "Normalize Unicode"
            info="规范化 JSON 字符串中的 Unicode 字符。",  # "Normalize Unicode characters in the JSON string."
            required=False,
        ),
        BoolInput(
            name="validate_json",
            display_name="验证 JSON",  # "Validate JSON"
            info="验证 JSON 字符串以确保其格式正确。",  # "Validate the JSON string to ensure it is well-formed."
            required=False,
        ),
    ]

    outputs = [
        Output(
display_name="清理后的 JSON 字符串",  # "Cleaned JSON String"
name="output",
method="clean_json",
),
    ]

    def clean_json(self) -> Message:
        try:
            from json_repair import repair_json
        except ImportError as e:
            msg = "无法导入 json_repair 包。请使用 `pip install json_repair` 安装。"  # "Could not import the json_repair package. Please install it with `pip install json_repair`."
            raise ImportError(msg) from e

        """Clean the input JSON string based on provided options and return the cleaned JSON string."""
        json_str = self.json_str
        remove_control_chars = self.remove_control_chars
        normalize_unicode = self.normalize_unicode
        validate_json = self.validate_json

        start = json_str.find("{")
        end = json_str.rfind("}")
        if start == -1 or end == -1:
            msg = "无效的 JSON 字符串：缺少 '{' 或 '}'"  # "Invalid JSON string: Missing '{' or '}'"
            raise ValueError(msg)
        try:
            json_str = json_str[start : end + 1]

            if remove_control_chars:
                json_str = self._remove_control_characters(json_str)
            if normalize_unicode:
                json_str = self._normalize_unicode(json_str)
            if validate_json:
                json_str = self._validate_json(json_str)

            cleaned_json_str = repair_json(json_str)
            result = str(cleaned_json_str)

            self.status = result
            return Message(text=result)
        except Exception as e:
            msg = f"清理 JSON 字符串时出错：{e}"  # "Error cleaning JSON string: {e}"
            raise ValueError(msg) from e

    def _remove_control_characters(self, s: str) -> str:
        """Remove control characters from the string."""
        return s.translate(self.translation_table)

    def _normalize_unicode(self, s: str) -> str:
        """Normalize Unicode characters in the string."""
        return unicodedata.normalize("NFC", s)

    def _validate_json(self, s: str) -> str:
        """Validate the JSON string."""
        try:
            json.loads(s)
        except json.JSONDecodeError as e:
            msg = f"无效的 JSON 字符串：{e}"  # "Invalid JSON string: {e}"
            raise ValueError(msg) from e
        return s

    def __init__(self, *args, **kwargs):
        # Create a translation table that maps control characters to None
        super().__init__(*args, **kwargs)
        self.translation_table = str.maketrans("", "", "".join(chr(i) for i in range(32)) + chr(127))
