from __future__ import annotations

import json
import re
from typing import TYPE_CHECKING, Any

from langflow.custom import Component
from langflow.io import DataInput, HandleInput, IntInput, MultilineInput, Output
from langflow.schema import Data
from langflow.schema.dataframe import DataFrame
from langflow.utils.data_structure import get_data_structure

if TYPE_CHECKING:
    from collections.abc import Callable


class LambdaFilterComponent(Component):
    display_name = "Lambda 过滤器"  # "Lambda Filter"
    description = "使用 LLM 生成一个用于过滤或转换结构化数据的 Lambda 函数。"  # "Uses an LLM to generate a lambda function for filtering or transforming structured data."
    icon = "filter"
    name = "LambdaFilter"
    beta = True

    inputs = [
        DataInput(
            name="data",
            display_name="数据",  # "Data"
            info="要使用 Lambda 函数过滤或转换的结构化数据。",  # "The structured data to filter or transform using a lambda function."
            is_list=True,
            required=True,
        ),
        HandleInput(
            name="llm",
            display_name="语言模型",  # "Language Model"
            info="在此处连接来自 LLM 组件的 'Language Model' 输出。",  # "Connect the 'Language Model' output from your LLM component here."
            input_types=["LanguageModel"],
            required=True,
        ),
        MultilineInput(
            name="filter_instruction",
            display_name="指令",  # "Instructions"
            info=(
"关于如何使用 Lambda 函数过滤或转换数据的自然语言指令。"  # "Natural language instructions for how to filter or transform the data using a lambda function. "
"例如：过滤数据，仅包含 'status' 为 'active' 的项。"  # "Example: Filter the data to only include items where the 'status' is 'active'."
            ),
            value="过滤数据以...",  # "Filter the data to..."
            required=True,
        ),
        IntInput(
            name="sample_size",
            display_name="样本大小",  # "Sample Size"
            info="对于大型数据集，从头/尾采样的项目数量。",  # "For large datasets, number of items to sample from head/tail."
            value=1000,
            advanced=True,
        ),
        IntInput(
            name="max_size",
            display_name="最大大小",  # "Max Size"
            info="数据被视为大型数据集的字符数阈值。",  # "Number of characters for the data to be considered large."
            value=30000,
            advanced=True,
        ),
    ]

    outputs = [
        Output(
            display_name="过滤后的数据",  # "Filtered Data"
            name="filtered_data",
            method="filter_data",
        ),
        Output(
            display_name="数据表",  # "DataFrame"
            name="dataframe",
            method="as_dataframe",
        ),
    ]

    def get_data_structure(self, data):
        """Extract the structure of a dictionary, replacing values with their types."""
        return {k: get_data_structure(v) for k, v in data.items()}

    def _validate_lambda(self, lambda_text: str) -> bool:
        """Validate the provided lambda function text."""
        # Return False if the lambda function does not start with 'lambda' or does not contain a colon
        return lambda_text.strip().startswith("lambda") and ":" in lambda_text

    async def filter_data(self) -> list[Data]:
        self.log(str(self.data))
        data = self.data[0].data if isinstance(self.data, list) else self.data.data

        dump = json.dumps(data)
        self.log(str(data))

        llm = self.llm
        instruction = self.filter_instruction
        sample_size = self.sample_size

        # Get data structure and samples
        data_structure = self.get_data_structure(data)
        dump_structure = json.dumps(data_structure)
        self.log(dump_structure)

        # For large datasets, sample from head and tail
        if len(dump) > self.max_size:
            data_sample = (
                f"数据太长，无法显示...\n\n 首行（头部）：{dump[:sample_size]} \n\n"
                f" 尾行（尾部）：{dump[-sample_size:]})"  # "Data is too long to display... \n\n First lines (head): {dump[:sample_size]} \n\n Last lines (tail): {dump[-sample_size:]})"
            )
        else:
            data_sample = dump

        self.log(data_sample)

        prompt = f"""根据以下数据结构和示例，创建一个 Python Lambda 函数来实现以下指令：

                    数据结构：
                    {dump_structure}

                    示例项目：
                    {data_sample}

                    指令：{instruction}

                    仅返回 Lambda 函数，不需要其他内容。不需要 ```python 或其他内容。
                    只需返回以 lambda 开头的字符串。
                    """  # "Given this data structure and examples, create a Python lambda function that implements the following instruction:..."

        response = await llm.ainvoke(prompt)
        response_text = response.content if hasattr(response, "content") else str(response)
        self.log(response_text)

        # Extract lambda using regex
        lambda_match = re.search(r"lambda\s+\w+\s*:.*?(?=\n|$)", response_text)
        if not lambda_match:
            msg = f"无法在响应中找到 Lambda：{response_text}"  # "Could not find lambda in response: {response_text}"
            raise ValueError(msg)

        lambda_text = lambda_match.group().strip()
        self.log(lambda_text)

        # Validation is commented out as requested
        if not self._validate_lambda(lambda_text):
            msg = f"Invalid lambda format: {lambda_text}"
            raise ValueError(msg)

        # Create and apply the function
        fn: Callable[[Any], Any] = eval(lambda_text)  # noqa: S307

        # Apply the lambda function to the data
        processed_data = fn(data)

        # If it's a dict, wrap it in a Data object
        if isinstance(processed_data, dict):
            return [Data(**processed_data)]
        # If it's a list, convert each item to a Data object
        if isinstance(processed_data, list):
            return [Data(**item) if isinstance(item, dict) else Data(text=str(item)) for item in processed_data]
        # If it's anything else, convert to string and wrap in a Data object
        return [Data(text=str(processed_data))]

    async def as_dataframe(self) -> DataFrame:
        """Return filtered data as a DataFrame."""
        filtered_data = await self.filter_data()
        return DataFrame(filtered_data)
