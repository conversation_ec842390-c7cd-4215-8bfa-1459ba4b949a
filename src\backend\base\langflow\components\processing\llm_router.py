import json

import requests

from langflow.base.models.chat_result import get_chat_result
from langflow.base.models.model_utils import get_model_name
from langflow.custom import Component
from langflow.io import DropdownInput, HandleInput, Output
from langflow.schema.message import Message


class LLMRouterComponent(Component):
    display_name = "LLM 路由器"  # "LLM Router"
    description = "根据 OpenRouter 模型规范将输入路由到最合适的 LLM。"  # "Routes the input to the most appropriate LLM based on OpenRouter model specifications."
    icon = "git-branch"

    inputs = [
        HandleInput(
            name="models",
            display_name="语言模型",  # "Language Models"
            input_types=["LanguageModel"],
            required=True,
            is_list=True,
            info="要在其间路由的 LLM 列表。",  # "List of LLMs to route between."
        ),
        HandleInput(
            name="input_value",
            display_name="输入",  # "Input"
            input_types=["Message"],
            info="要路由的输入消息。",  # "The input message to be routed."
        ),
        HandleInput(
            name="judge_llm",
            display_name="评估 LLM",  # "Judge LLM"
            input_types=["LanguageModel"],
            info="将评估并选择最合适模型的 LLM。",  # "LLM that will evaluate and select the most appropriate model."
        ),
        DropdownInput(
            name="optimization",
            display_name="优化",  # "Optimization"
            options=["质量", "速度", "成本", "平衡"],  # ["quality", "speed", "cost", "balanced"]
            value="平衡",  # "balanced"
            info="模型选择的优化偏好。",  # "Optimization preference for model selection."
        ),
    ]

    outputs = [
        Output(display_name="输出", name="output", method="route_to_model"),  # "Output"
        Output(
            display_name="选择的模型",  # "Selected Model"
            name="selected_model",
            method="get_selected_model",
            required_inputs=["output"],
        ),
    ]

    _selected_model_name: str | None = None

    def get_selected_model(self) -> str:
        return self._selected_model_name or ""

    def _get_model_specs(self, model_name: str) -> str:
        """Fetch specific model information from OpenRouter API."""
        http_success = 200
        base_info = f"模型: {model_name}\n"  # "Model: {model_name}\n"

        # Remove any special characters and spaces, keep only alphanumeric
        clean_name = "".join(c.lower() for c in model_name if c.isalnum())
        url = f"https://openrouter.ai/api/v1/models/{clean_name}/endpoints"

        try:
            response = requests.get(url, timeout=10)
        except requests.exceptions.RequestException as e:
            return base_info + f"获取规格时出错: {e!s}"  # "Error fetching specs: {e!s}"

        if response.status_code != http_success:
            return base_info + "无可用规格"  # "No specifications available"

        try:
            data = response.json().get("data", {})
        except (json.JSONDecodeError, requests.exceptions.JSONDecodeError):
            return base_info + "解析响应数据时出错"  # "Error parsing response data"

        # 提取相关信息
        # "Extract relevant information"
        context_length = data.get("context_length", "未知")  # "Unknown"
        max_completion_tokens = data.get("max_completion_tokens", "未知")  # "Unknown"
        architecture = data.get("architecture", {})
        tokenizer = architecture.get("tokenizer", "未知")  # "Unknown"
        instruct_type = architecture.get("instruct_type", "未知")  # "Unknown"

        pricing = data.get("pricing", {})
        prompt_price = pricing.get("prompt", "未知")  # "Unknown"
        completion_price = pricing.get("completion", "未知")  # "Unknown"

        description = data.get("description", "无可用描述")  # "No description available"
        created = data.get("created", "未知")  # "Unknown"

        return f"""
模型: {model_name}  # "Model: {model_name}"
描述: {description}  # "Description: {description}"
上下文长度: {context_length} tokens  # "Context Length: {context_length} tokens"
最大完成 tokens: {max_completion_tokens}  # "Max Completion Tokens: {max_completion_tokens}"
分词器: {tokenizer}  # "Tokenizer: {tokenizer}"
指令类型: {instruct_type}  # "Instruct Type: {instruct_type}"
定价: ${prompt_price}/1k tokens (提示), ${completion_price}/1k tokens (完成)  # "Pricing: ${prompt_price}/1k tokens (prompt), ${completion_price}/1k tokens (completion)"
创建时间: {created}  # "Created: {created}"
"""

    MISSING_INPUTS_MSG = "缺少必要的输入: models, input_value 或 judge_llm"  # "Missing required inputs: models, input_value, or judge_llm"

    async def route_to_model(self) -> Message:
        if not self.models or not self.input_value or not self.judge_llm:
            raise ValueError(self.MISSING_INPUTS_MSG)

        system_prompt = {
            "role": "system",
            "content": (
"你是一个模型选择专家。分析输入并根据以下条件选择最合适的模型：\n"  # "You are a model selection expert. Analyze the input and select the most appropriate model based on:\n"
"1. 任务复杂性和需求\n"  # "1. Task complexity and requirements\n"
"2. 所需的上下文长度\n"  # "2. Context length needed\n"
"3. 模型能力\n"  # "3. Model capabilities\n"
"4. 成本考虑\n"  # "4. Cost considerations\n"
"5. 速度需求\n\n"  # "5. Speed requirements\n\n"
"考虑提供的详细模型规格和用户的优化偏好。仅返回最佳模型的索引号（从 0 开始）。"  # "Consider the detailed model specifications provided and the user's optimization preference. Return only the index number (0-based) of the best model."
            ),
        }

        # Create list of available models with their detailed specs
        models_info = []
        for i, model in enumerate(self.models):
            model_name = get_model_name(model)
            model_specs = self._get_model_specs(model_name)
            models_info.append(f"=== 模型 {i} ===\n{model_specs}")  # "=== Model {i} ===\n{model_specs}"

        models_str = "\n\n".join(models_info)

        user_message = {
            "role": "user",
            "content": f"""可用模型及规格:\n{models_str}\n
            优化偏好: {self.optimization}\n
            输入查询: "{self.input_value.text}"\n
            根据模型规格和优化偏好，选择最合适的模型（仅返回索引号）:""",  # "Available Models with Specifications:\n{models_str}\nOptimization Preference: {self.optimization}\nInput Query: "{self.input_value.text}"\nBased on the model specifications and optimization preference, select the most appropriate model (return only the index number):"
        }

        try:
            # Get judge's decision
            response = await self.judge_llm.ainvoke([system_prompt, user_message])

            try:
                selected_index = int(response.content.strip())
                if 0 <= selected_index < len(self.models):
                    chosen_model = self.models[selected_index]
                    self._selected_model_name = get_model_name(chosen_model)
                else:
                    chosen_model = self.models[0]
                    self._selected_model_name = get_model_name(chosen_model)
            except ValueError:
                chosen_model = self.models[0]
                self._selected_model_name = get_model_name(chosen_model)

            # Get response from chosen model
            return get_chat_result(
                runnable=chosen_model,
                input_value=self.input_value,
            )

        except (RuntimeError, ValueError) as e:
            self.status = f"错误: {e!s}"  # "Error: {e!s}"
            # 回退到第一个模型
            # "Fallback to first model"
            chosen_model = self.models[0]
            self._selected_model_name = get_model_name(chosen_model)
            return get_chat_result(
                runnable=chosen_model,
                input_value=self.input_value,
            )
