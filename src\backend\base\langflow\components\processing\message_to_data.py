from loguru import logger

from langflow.custom import Component
from langflow.io import MessageInput, Output
from langflow.schema import Data
from langflow.schema.message import Message


class MessageToDataComponent(Component):
    display_name = "消息转数据"  # "Message to Data"
    description = "将 Message 对象转换为 Data 对象"  # "Convert a Message object to a Data object"
    icon = "message-square-share"
    beta = True
    name = "MessagetoData"

    inputs = [
        MessageInput(
            name="message",
            display_name="消息",  # "Message"
            info="要转换为 Data 对象的 Message 对象",  # "The Message object to convert to a Data object"
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="convert_message_to_data"),  # "Data"
    ]

    def convert_message_to_data(self) -> Data:
        if isinstance(self.message, Message):
            # 将 Message 转换为 Data
            # "Convert Message to Data"
            return Data(data=self.message.data)

        msg = "将 Message 转换为 Data 时出错：输入必须是 Message 对象"  # "Error converting Message to Data: Input must be a Message object"
        logger.opt(exception=True).debug(msg)
        self.status = msg
        return Data(data={"error": msg})
