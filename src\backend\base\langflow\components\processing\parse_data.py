from langflow.custom import Component
from langflow.helpers.data import data_to_text, data_to_text_list
from langflow.io import DataInput, MultilineInput, Output, StrInput
from langflow.schema import Data
from langflow.schema.message import Message


class ParseDataComponent(Component):
    display_name = "数据转消息"  # "Data to Message"
    description = "使用输入数据中的任意 {字段} 将 Data 对象转换为消息。"  # "Convert Data objects into Messages using any {field_name} from input data."
    icon = "message-square"
    name = "ParseData"
    metadata = {
        "legacy_name": "解析数据",  # "Parse Data"
    }

    inputs = [
        DataInput(
            name="data",
            display_name="数据",  # "Data"
            info="要转换为文本的数据。",  # "The data to convert to text."
            is_list=True,
            required=True,
        ),
        MultilineInput(
            name="template",
            display_name="模板",  # "Template"
            info=(
                "用于格式化数据的模板。"  # "The template to use for formatting the data. "
"它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。"  # "It can contain the keys {text}, {data} or any other key in the Data."
            ),
            value="{文本}",
            required=True,
        ),
        StrInput(
name="sep",
display_name="分隔符",  # "Separator"
advanced=True,
value="\n",
),
    ]

    outputs = [
        Output(
            display_name="消息",  # "Message"
            name="text",
            info="数据作为单个消息，每个输入数据由分隔符分隔。",  # "Data as a single Message, with each input Data separated by Separator."
            method="parse_data",
        ),
        Output(
            display_name="数据列表",  # "Data List"
            name="data_list",
            info="数据作为新数据的列表，每个数据的 `text` 由模板格式化。",  # "Data as a list of new Data, each having `text` formatted by Template."
            method="parse_data_as_list",
        ),
    ]

    def _clean_args(self) -> tuple[list[Data], str, str]:
        data = self.data if isinstance(self.data, list) else [self.data]
        template = self.template
        sep = self.sep
        return data, template, sep

    def parse_data(self) -> Message:
        data, template, sep = self._clean_args()
        result_string = data_to_text(template, data, sep)
        self.status = result_string
        return Message(text=result_string)

    def parse_data_as_list(self) -> list[Data]:
        data, template, _ = self._clean_args()
        text_list, data_list = data_to_text_list(template, data)
        for item, text in zip(data_list, text_list, strict=True):
            item.set_text(text)
        self.status = data_list
        return data_list
