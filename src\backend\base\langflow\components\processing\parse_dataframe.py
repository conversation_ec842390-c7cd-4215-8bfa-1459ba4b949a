from langflow.custom import Component
from langflow.io import DataFrameInput, MultilineInput, Output, StrInput
from langflow.schema.message import Message


class ParseDataFrameComponent(Component):
    display_name = "解析数据表"  # "Parse DataFrame"
    description = (
"将数据表转换为遵循指定模板的纯文本。"  # "Convert a DataFrame into plain text following a specified template. "
"数据表中的每一列都被视为可能的模板键，例如 {列名}。"  # "Each column in the DataFrame is treated as a possible template key, e.g. {col_name}."
    )
    icon = "braces"
    name = "ParseDataFrame"

    inputs = [
        DataFrameInput(
name="df",
display_name="数据表",  # "DataFrame"
info="要转换为文本行的数据表。",  # "The DataFrame to convert to text rows."
),
        MultilineInput(
            name="template",
            display_name="模板",  # "Template"
            info=(
"用于格式化每一行的模板。"  # "The template for formatting each row. "
"使用与数据表列名匹配的占位符，例如 '{col1}'、'{col2}'。"  # "Use placeholders matching column names in the DataFrame, for example '{col1}', '{col2}'."
            ),
            value="{文本}",
        ),
        StrInput(
            name="sep",
            display_name="分隔符",  # "Separator"
            advanced=True,
            value="\n",
            info="在构建单个文本输出时，用于连接所有行文本的字符串。",  # "String that joins all row texts when building the single Text output."
        ),
    ]

    outputs = [
        Output(
            display_name="文本",  # "Text"
            name="text",
            info="所有行合并为单个文本，每行由模板格式化并由 `sep` 分隔。",  # "All rows combined into a single text, each row formatted by the template and separated by `sep`."
            method="parse_data",
        ),
    ]

    def _clean_args(self):
        dataframe = self.df
        template = self.template or "{text}"
        sep = self.sep or "\n"
        return dataframe, template, sep

    def parse_data(self) -> Message:
        """Converts each row of the DataFrame into a formatted string using the template.

        then joins them with `sep`. Returns a single combined string as a Message.
        """
        dataframe, template, sep = self._clean_args()

        lines = []
        # For each row in the DataFrame, build a dict and format
        for _, row in dataframe.iterrows():
            row_dict = row.to_dict()
            text_line = template.format(**row_dict)  # e.g. template="{text}", row_dict={"text": "Hello"}
            lines.append(text_line)

        # Join all lines with the provided separator
        result_string = sep.join(lines)
        self.status = result_string  # store in self.status for UI logs
        return Message(text=result_string)
