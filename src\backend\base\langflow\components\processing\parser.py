from typing import Any

from langflow.custom import Component
from langflow.io import (
    BoolInput,
    HandleInput,
    MessageTextInput,
    MultilineInput,
    Output,
)
from langflow.schema import Data, DataFrame
from langflow.schema.message import Message


class ParserComponent(Component):
    display_name = "解析器"  # "Parser"
    description = (
"使用模板将 DataFrame 或 Data 对象格式化为文本。"  # "Format a DataFrame or Data object into text using a template."
        "启用 '字符串化' 以将输入转换为可读字符串。"  # "Enable 'Stringify' to convert input into a readable string instead."
    )
    icon = "braces"
    beta = True

    inputs = [
        BoolInput(
            name="stringify",
            display_name="字符串化",  # "Stringify"
            info="启用此选项以将输入转换为字符串，而不是使用模板。",  # "Enable to convert input to a string instead of using a template."
            value=False,
            real_time_refresh=True,
        ),
        MultilineInput(
            name="template",
            display_name="模板",  # "Template"
            info=(
"使用大括号中的变量提取 DataFrame 的列值或 Data 的键值。"  # "Use variables within curly brackets to extract column values for DataFrames "
                "例如：`姓名: {姓名}, 年龄: {年龄}, 国家: {国家}`"  # "For example: `Name: {Name}, Age: {Age}, Country: {Country}`"
            ),
            value="文本: {文本}",  # "Text: {text}"
            dynamic=True,
            show=True,
            required=True,
        ),
        HandleInput(
            name="input_data",
            display_name="数据或数据表",  # "Data or DataFrame"
            input_types=["DataFrame", "Data"],
            info="接受 DataFrame 或 Data 对象。",  # "Accepts either a DataFrame or a Data object."
            required=True,
        ),
        MessageTextInput(
            name="sep",
            display_name="分隔符",  # "Separator"
            advanced=True,
            value="\n",
            info="用于分隔行/项的字符串。",  # "String used to separate rows/items."
        ),
    ]

    outputs = [
        Output(
            display_name="解析后的文本",  # "Parsed Text"
            name="parsed_text",
            info="格式化的文本输出。",  # "Formatted text output."
            method="parse_combined_text",
        ),
    ]

    def update_build_config(self, build_config, field_value, field_name=None):
        """根据 `stringify` 动态隐藏/显示 `template` 并强制其为必填项。"""  # "Dynamically hide/show `template` and enforce requirement based on `stringify`."
        if field_name == "stringify":
            build_config["template"]["show"] = not field_value
            build_config["template"]["required"] = not field_value
            if field_value:
                clean_data = BoolInput(
                    name="clean_data",
                    display_name="清理数据",  # "Clean Data"
                    info=(
"启用此选项以通过删除空行和每个单元格中的空行来清理数据。"  # "Enable to clean the data by removing empty rows and lines "
"适用于 DataFrame 或 Data 对象。"  # "in each cell of the DataFrame/ Data object."
                    ),
                    value=True,
                    advanced=True,
                    required=False,
                )
                build_config["clean_data"] = clean_data.to_dict()
            else:
                build_config.pop("clean_data", None)

        return build_config

    def _clean_args(self):
        """Prepare arguments based on input type."""
        input_data = self.input_data

        match input_data:
            case list() if all(isinstance(item, Data) for item in input_data):
                msg = "不支持 Data 对象列表。"  # "List of Data objects is not supported."
                raise ValueError(msg)
            case DataFrame():
                return input_data, None
            case Data():
                return None, input_data
            case dict() if "data" in input_data:
                try:
                    if "columns" in input_data:  # Likely a DataFrame
                        return DataFrame.from_dict(input_data), None
                    # Likely a Data object
                    return None, Data(**input_data)
                except (TypeError, ValueError, KeyError) as e:
                    msg = f"提供的结构化输入无效: {e!s}"  # "Invalid structured input provided: {e!s}"
                    raise ValueError(msg) from e
            case _:
                msg = f"不支持的输入类型: {type(input_data)}。期望 DataFrame 或 Data。"  # "Unsupported input type: {type(input_data)}. Expected DataFrame or Data."
                raise ValueError(msg)

    def parse_combined_text(self) -> Message:
        """Parse all rows/items into a single text or convert input to string if `stringify` is enabled."""
        # Early return for stringify option
        if self.stringify:
            return self.convert_to_string()

        df, data = self._clean_args()

        lines = []
        if df is not None:
            for _, row in df.iterrows():
                formatted_text = self.template.format(**row.to_dict())
                lines.append(formatted_text)
        elif data is not None:
            formatted_text = self.template.format(text=data.get_text())
            lines.append(formatted_text)

        combined_text = self.sep.join(lines)
        self.status = combined_text
        return Message(text=combined_text)

    def _safe_convert(self, data: Any) -> str:
        """Safely convert input data to string."""
        try:
            if isinstance(data, str):
                return data
            if isinstance(data, Message):
                return data.get_text()
            if isinstance(data, Data):
                if data.get_text() is None:
                    msg = "空的 Data 对象"  # "Empty Data object"
                    raise ValueError(msg)
                return data.get_text()
            if isinstance(data, DataFrame):
                if hasattr(self, "clean_data") and self.clean_data:
                    # Remove empty rows
                    data = data.dropna(how="all")
                    # Remove empty lines in each cell
                    data = data.replace(r"^\s*$", "", regex=True)
                    # Replace multiple newlines with a single newline
                    data = data.replace(r"\n+", "\n", regex=True)
                return data.to_markdown(index=False)
            return str(data)
        except (ValueError, TypeError, AttributeError) as e:
            msg = f"转换数据时出错: {e!s}"  # "Error converting data: {e!s}"
            raise ValueError(msg) from e

    def convert_to_string(self) -> Message:
        """Convert input data to string with proper error handling."""
        result = ""
        if isinstance(self.input_data, list):
            result = "\n".join([self._safe_convert(item) for item in self.input_data])
        else:
            result = self._safe_convert(self.input_data)
        self.log(f"转换为字符串，长度为: {len(result)}")  # "Converted to string with length: {len(result)}"
        return Message(text=result)
