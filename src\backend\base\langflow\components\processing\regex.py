import re

from langflow.custom import Component
from langflow.io import MessageTextInput, Output
from langflow.schema import Data
from langflow.schema.message import Message


class RegexExtractorComponent(Component):
    display_name = "正则表达式提取器"  # "Regex Extractor"
    description = "使用正则表达式从文本中提取模式。"  # "Extract patterns from text using regular expressions."
    icon = "regex"

    inputs = [
        MessageTextInput(
            name="input_text",
            display_name="输入文本",  # "Input Text"
            info="要分析的文本",  # "The text to analyze"
            required=True,
        ),
        MessageTextInput(
            name="pattern",
            display_name="正则表达式模式",  # "Regex Pattern"
            info="要匹配的正则表达式模式",  # "The regular expression pattern to match"
            value=r"",
            required=True,
            tool_mode=True,
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="extract_matches"),  # "Data"
        Output(display_name="消息", name="text", method="get_matches_text"),  # "Message"
    ]

    def extract_matches(self) -> list[Data]:
        if not self.pattern or not self.input_text:
            self.status = []
            return []

        try:
            # Compile regex pattern
            pattern = re.compile(self.pattern)

            # Find all matches in the input text
            matches = pattern.findall(self.input_text)

            # Filter out empty matches
            filtered_matches = [match for match in matches if match]  # Remove empty matches

            # Return empty list for no matches, or list of matches if found
            result: list = [] if not filtered_matches else [Data(data={"match": match}) for match in filtered_matches]

        except re.error as e:
            error_message = f"无效的正则表达式模式: {e!s}"  # "Invalid regex pattern: {e!s}"
            result = [Data(data={"error": error_message})]
        except ValueError as e:
            error_message = f"提取匹配项时出错: {e!s}"  # "Error extracting matches: {e!s}"
            result = [Data(data={"error": error_message})]

        self.status = result
        return result

    def get_matches_text(self) -> Message:
        """Get matches as a formatted text message."""
        matches = self.extract_matches()

        if not matches:
            message = Message(text="未找到匹配项")  # "No matches found"
            self.status = message
            return message

        if "error" in matches[0].data:
            message = Message(text=matches[0].data["error"])
            self.status = message
            return message

        result = "\n".join(match.data["match"] for match in matches)
        message = Message(text=result)
        self.status = message
        return message
