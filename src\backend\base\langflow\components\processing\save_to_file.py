import json
from collections.abc import AsyncIterator, Iterator
from pathlib import Path

import pandas as pd

from langflow.custom import Component
from langflow.io import (
    DataFrameInput,
    DataInput,
    DropdownInput,
    MessageInput,
    Output,
    StrInput,
)
from langflow.schema import Data, DataFrame, Message


class SaveToFileComponent(Component):
    display_name = "保存到文件"  # "Save to File"
    description = "将 DataFrame、Data 或 Message 保存为各种文件格式。"  # "Save DataFrames, Data, or Messages to various file formats."
    icon = "save"
    name = "SaveToFile"

    # File format options for different types
    DATA_FORMAT_CHOICES = ["csv", "excel", "json", "markdown"]
    MESSAGE_FORMAT_CHOICES = ["txt", "json", "markdown"]

    inputs = [
        DropdownInput(
            name="input_type",
            display_name="输入类型",  # "Input Type"
            options=["数据表", "数据", "消息"],  # ["DataFrame", "Data", "Message"]
            info="选择要保存的输入类型。",  # "Select the type of input to save."
            value="数据表",  # "DataFrame"
            real_time_refresh=True,
        ),
        DataFrameInput(
            name="df",
            display_name="数据表",  # "DataFrame"
            info="要保存的数据表。",  # "The DataFrame to save."
            dynamic=True,
            show=True,
        ),
        DataInput(
            name="data",
            display_name="数据",  # "Data"
            info="要保存的数据对象。",  # "The Data object to save."
            dynamic=True,
            show=False,
        ),
        MessageInput(
            name="message",
            display_name="消息",  # "Message"
            info="要保存的消息。",  # "The Message to save."
            dynamic=True,
            show=False,
        ),
        DropdownInput(
            name="file_format",
            display_name="文件格式",  # "File Format"
            options=DATA_FORMAT_CHOICES,
            info="选择保存输入的文件格式。",  # "Select the file format to save the input."
            real_time_refresh=True,
        ),
        StrInput(
            name="file_path",
            display_name="文件路径（包括文件名）",  # "File Path (including filename)"
            info="完整的文件路径（包括文件名和扩展名）。",  # "The full file path (including filename and extension)."
            value="./output",
        ),
    ]

    outputs = [
        Output(
            name="confirmation",
            display_name="确认",  # "Confirmation"
            method="save_to_file",
            info="保存文件后的确认消息。",  # "Confirmation message after saving the file."
        ),
    ]

    def update_build_config(self, build_config, field_value, field_name=None):
        # Hide/show dynamic fields based on the selected input type
        if field_name == "input_type":
            build_config["df"]["show"] = field_value == "数据表"  # "DataFrame"
            build_config["data"]["show"] = field_value == "数据"  # "Data"
            build_config["message"]["show"] = field_value == "消息"  # "Message"

            if field_value in {"数据表", "数据"}:  # {"DataFrame", "Data"}
                build_config["file_format"]["options"] = self.DATA_FORMAT_CHOICES
            elif field_value == "消息":  # "Message"
                build_config["file_format"]["options"] = self.MESSAGE_FORMAT_CHOICES

        return build_config

    def save_to_file(self) -> str:
        input_type = self.input_type
        file_format = self.file_format
        file_path = Path(self.file_path).expanduser()

        # Ensure the directory exists
        if not file_path.parent.exists():
            file_path.parent.mkdir(parents=True, exist_ok=True)

        if input_type == "数据表":  # "DataFrame"
            dataframe = self.df
            return self._save_dataframe(dataframe, file_path, file_format)
        if input_type == "数据":  # "Data"
            data = self.data
            return self._save_data(data, file_path, file_format)
        if input_type == "消息":  # "Message"
            message = self.message
            return self._save_message(message, file_path, file_format)

        error_msg = f"不支持的输入类型: {input_type}"  # "Unsupported input type: {input_type}"
        raise ValueError(error_msg)

    def _save_dataframe(self, dataframe: DataFrame, path: Path, fmt: str) -> str:
        if fmt == "csv":
            dataframe.to_csv(path, index=False)
        elif fmt == "excel":
            dataframe.to_excel(path, index=False, engine="openpyxl")
        elif fmt == "json":
            dataframe.to_json(path, orient="records", indent=2)
        elif fmt == "markdown":
            path.write_text(dataframe.to_markdown(index=False), encoding="utf-8")
        else:
            error_msg = f"不支持的数据表格式: {fmt}"  # "Unsupported DataFrame format: {fmt}"
            raise ValueError(error_msg)

        return f"数据表已成功保存为 '{path}'"  # "DataFrame saved successfully as '{path}'"

    def _save_data(self, data: Data, path: Path, fmt: str) -> str:
        if fmt == "csv":
            pd.DataFrame(data.data).to_csv(path, index=False)
        elif fmt == "excel":
            pd.DataFrame(data.data).to_excel(path, index=False, engine="openpyxl")
        elif fmt == "json":
            path.write_text(json.dumps(data.data, indent=2), encoding="utf-8")
        elif fmt == "markdown":
            path.write_text(pd.DataFrame(data.data).to_markdown(index=False), encoding="utf-8")
        else:
            error_msg = f"不支持的数据格式: {fmt}"  # "Unsupported Data format: {fmt}"
            raise ValueError(error_msg)

        return f"数据已成功保存为 '{path}'"  # "Data saved successfully as '{path}'"

    def _save_message(self, message: Message, path: Path, fmt: str) -> str:
        if message.text is None:
            content = ""
        elif isinstance(message.text, AsyncIterator):
            # AsyncIterator 需要特殊处理
            # "AsyncIterator needs to be handled differently"
            error_msg = "不支持 AsyncIterator"  # "AsyncIterator not supported"
            raise ValueError(error_msg)
        elif isinstance(message.text, Iterator):
            # Convert iterator to string
            content = " ".join(str(item) for item in message.text)
        else:
            content = str(message.text)

        if fmt == "txt":
            path.write_text(content, encoding="utf-8")
        elif fmt == "json":
            path.write_text(json.dumps({"message": content}, indent=2), encoding="utf-8")
        elif fmt == "markdown":
            path.write_text(f"**消息:**\n\n{content}", encoding="utf-8")  # "**Message:**\n\n{content}"
        else:
            error_msg = f"不支持的消息格式: {fmt}"  # "Unsupported Message format: {fmt}"
            raise ValueError(error_msg)

        return f"消息已成功保存为 '{path}'"  # "Message saved successfully as '{path}'"
