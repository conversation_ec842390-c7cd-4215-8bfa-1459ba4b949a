from langflow.custom import Component
from langflow.field_typing.range_spec import RangeSpec
from langflow.inputs.inputs import DataInput, IntInput
from langflow.io import Output
from langflow.schema import Data


class SelectDataComponent(Component):
    display_name: str = "选择数据"  # "Select Data"
    description: str = "从数据列表中选择一个数据。"  # "Select a single data from a list of data."
    name: str = "SelectData"
    icon = "prototypes"
    legacy = True

    inputs = [
        DataInput(
            name="data_list",
            display_name="数据列表",  # "Data List"
            info="要从中选择的数据列表。",  # "List of data to select from."
            is_list=True,  # 指定此输入接受 Data 对象列表 "Specify that this input takes a list of Data objects"
        ),
        IntInput(
            name="data_index",
            display_name="数据索引",  # "Data Index"
            info="要选择的数据的索引。",  # "Index of the data to select."
            value=0,  # 将根据 data_list 的长度动态填充 "Will be populated dynamically based on the length of data_list"
            range_spec=RangeSpec(min=0, max=15, step=1, step_type="int"),
        ),
    ]

    outputs = [
        Output(display_name="选定的数据", name="selected_data", method="select_data"),  # "Selected Data"
    ]

    async def select_data(self) -> Data:
        # Retrieve the selected index from the dropdown
        selected_index = int(self.data_index)
        # Get the data list

        # Validate that the selected index is within bounds
        if selected_index < 0 or selected_index >= len(self.data_list):
            msg = f"选定的索引 {selected_index} 超出范围。"  # "Selected index {selected_index} is out of range."
            raise ValueError(msg)

        # Return the selected Data object
        selected_data = self.data_list[selected_index]
        self.status = selected_data  # Update the component status to reflect the selected data
        return selected_data
