from langchain_text_splitters import CharacterTextSplitter

from langflow.custom import Component
from langflow.io import DropdownInput, HandleInput, IntInput, MessageTextInput, Output
from langflow.schema import Data, DataFrame
from langflow.utils.util import unescape_string


class SplitTextComponent(Component):
    display_name: str = "拆分文本"  # "Split Text"
    description: str = "根据指定的条件将文本拆分为块。"  # "Split text into chunks based on specified criteria."
    icon = "scissors-line-dashed"
    name = "SplitText"

    inputs = [
        HandleInput(
            name="data_inputs",
            display_name="数据或数据表",  # "Data or DataFrame"
            info="包含要拆分为块的文本的数据。",  # "The data with texts to split in chunks."
            input_types=["Data", "DataFrame"],
            required=True,
        ),
        IntInput(
            name="chunk_overlap",
            display_name="块重叠",  # "Chunk Overlap"
            info="块之间重叠的字符数。",  # "Number of characters to overlap between chunks."
            value=200,
        ),
        IntInput(
            name="chunk_size",
            display_name="块大小",  # "Chunk Size"
            info=(
"每个块的最大长度。文本首先按分隔符拆分，"  # "The maximum length of each chunk. Text is first split by separator, "
"然后将块合并到此大小。"  # "then chunks are merged up to this size. "
"单个拆分大于此大小的不会进一步拆分。"  # "Individual splits larger than this won't be further divided."
            ),
            value=1000,
        ),
        MessageTextInput(
            name="separator",
            display_name="分隔符",  # "Separator"
            info=(
"用于拆分的字符。使用 \\n 表示换行符。"  # "The character to split on. Use \\n for newline. "
"示例：\\n\\n 表示段落，\\n 表示行，. 表示句子。"  # "Examples: \\n\\n for paragraphs, \\n for lines, . for sentences."
            ),
            value="\n",
        ),
        MessageTextInput(
            name="text_key",
            display_name="文本键",  # "Text Key"
            info="用于文本列的键。",  # "The key to use for the text column."
            value="text",
            advanced=True,
        ),
        DropdownInput(
            name="keep_separator",
            display_name="保留分隔符",  # "Keep Separator"
            info="是否在输出块中保留分隔符以及将其放置的位置。",  # "Whether to keep the separator in the output chunks and where to place it."
            options=["False", "True", "Start", "End"],
            value="False",
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="块", name="chunks", method="split_text"),  # "Chunks"
        Output(display_name="数据表", name="dataframe", method="as_dataframe"),  # "DataFrame"
    ]

    def _docs_to_data(self, docs) -> list[Data]:
        return [Data(text=doc.page_content, data=doc.metadata) for doc in docs]

    def _fix_separator(self, separator: str) -> str:
        """Fix common separator issues and convert to proper format."""
        if separator == "/n":
            return "\n"
        if separator == "/t":
            return "\t"
        return separator

    def split_text_base(self):
        separator = self._fix_separator(self.separator)
        separator = unescape_string(separator)

        if isinstance(self.data_inputs, DataFrame):
            if not len(self.data_inputs):
                msg = "数据表为空"  # "DataFrame is empty"
                raise TypeError(msg)

            self.data_inputs.text_key = self.text_key
            try:
                documents = self.data_inputs.to_lc_documents()
            except Exception as e:
                msg = f"将数据表转换为文档时出错：{e}"  # "Error converting DataFrame to documents: {e}"
                raise TypeError(msg) from e
        else:
            if not self.data_inputs:
                msg = "未提供数据输入"  # "No data inputs provided"
                raise TypeError(msg)

            documents = []
            if isinstance(self.data_inputs, Data):
                self.data_inputs.text_key = self.text_key
                documents = [self.data_inputs.to_lc_document()]
            else:
                try:
                    documents = [input_.to_lc_document() for input_ in self.data_inputs if isinstance(input_, Data)]
                    if not documents:
                        msg = f"在 {type(self.data_inputs)} 中未找到有效的数据输入"  # "No valid Data inputs found in {type(self.data_inputs)}"
                        raise TypeError(msg)
                except AttributeError as e:
                    msg = f"集合中的输入类型无效：{e}"  # "Invalid input type in collection: {e}"
                    raise TypeError(msg) from e
        try:
            # Convert string 'False'/'True' to boolean
            keep_sep = self.keep_separator
            if isinstance(keep_sep, str):
                if keep_sep.lower() == "false":
                    keep_sep = False
                elif keep_sep.lower() == "true":
                    keep_sep = True
                # 'start' and 'end' are kept as strings

            splitter = CharacterTextSplitter(
                chunk_overlap=self.chunk_overlap,
                chunk_size=self.chunk_size,
                separator=separator,
                keep_separator=keep_sep,
            )
            return splitter.split_documents(documents)
        except Exception as e:
            msg = f"拆分文本时出错：{e}"  # "Error splitting text: {e}"
            raise TypeError(msg) from e

    def split_text(self) -> list[Data]:
        return self._docs_to_data(self.split_text_base())

    def as_dataframe(self) -> DataFrame:
        return DataFrame(self.split_text())
