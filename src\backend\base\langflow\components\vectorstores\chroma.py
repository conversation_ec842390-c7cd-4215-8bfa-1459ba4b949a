from copy import deepcopy

from chromadb.config import Settings
from langchain_chroma import Chroma
from typing_extensions import override

from langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store
from langflow.base.vectorstores.utils import chroma_collection_to_data
from langflow.io import BoolInput, DropdownInput, HandleInput, IntInput, StrInput
from langflow.schema import Data, DataFrame


class ChromaVectorStoreComponent(LCVectorStoreComponent):
    """具有搜索功能的 Chroma 向量存储。"""  # "Chroma Vector Store with search capabilities."

    display_name: str = "Chroma 数据库"  # "Chroma Database"
    description: str = "具有搜索功能的 Chroma 向量存储。"  # "Chroma Vector Store with search capabilities"
    name = "Chroma"
    icon = "Chroma"

    inputs = [
        StrInput(
            name="collection_name",
            display_name="集合名称",  # "Collection Name"
            value="langflow",
        ),
        StrInput(
            name="persist_directory",
            display_name="持久化目录",  # "Persist Directory"
        ),
        *LCVectorStoreComponent.inputs,
        HandleInput(
name="embedding",
display_name="嵌入",  # "Embedding"
input_types=["Embeddings"],
),
        StrInput(
            name="chroma_server_cors_allow_origins",
            display_name="服务器 CORS 允许的来源",  # "Server CORS Allow Origins"
            advanced=True,
        ),
        StrInput(
            name="chroma_server_host",
            display_name="服务器主机",  # "Server Host"
            advanced=True,
        ),
        IntInput(
            name="chroma_server_http_port",
            display_name="服务器 HTTP 端口",  # "Server HTTP Port"
            advanced=True,
        ),
        IntInput(
            name="chroma_server_grpc_port",
            display_name="服务器 gRPC 端口",  # "Server gRPC Port"
            advanced=True,
        ),
        BoolInput(
            name="chroma_server_ssl_enabled",
            display_name="服务器 SSL 启用",  # "Server SSL Enabled"
            advanced=True,
        ),
        BoolInput(
            name="allow_duplicates",
            display_name="允许重复",  # "Allow Duplicates"
            advanced=True,
            info="如果为 False，则不会添加已存在于向量存储中的文档。",  # "If false, will not add documents that are already in the Vector Store."
        ),
        DropdownInput(
            name="search_type",
            display_name="搜索类型",  # "Search Type"
            options=["相似性", "MMR"],  # ["Similarity", "MMR"]
            value="相似性",  # "Similarity"
            advanced=True,
        ),
        IntInput(
            name="number_of_results",
            display_name="结果数量",  # "Number of Results"
            info="要返回的结果数量。",  # "Number of results to return."
            advanced=True,
            value=10,
        ),
        IntInput(
            name="limit",
            display_name="限制",  # "Limit"
            advanced=True,
            info="当允许重复为 False 时，限制比较的记录数量。",  # "Limit the number of records to compare when Allow Duplicates is False."
        ),
    ]

    @override
    @check_cached_vector_store
    def build_vector_store(self) -> Chroma:
        """Builds the Chroma object."""
        try:
            from chromadb import Client
            from langchain_chroma import Chroma
        except ImportError as e:
            msg = "无法导入 Chroma 集成包。请使用 `pip install langchain-chroma` 安装。"  # "Could not import Chroma integration package. Please install it with `pip install langchain-chroma`."
            raise ImportError(msg) from e
        # Chroma settings
        chroma_settings = None
        client = None
        if self.chroma_server_host:
            chroma_settings = Settings(
                chroma_server_cors_allow_origins=self.chroma_server_cors_allow_origins or [],
                chroma_server_host=self.chroma_server_host,
                chroma_server_http_port=self.chroma_server_http_port or None,
                chroma_server_grpc_port=self.chroma_server_grpc_port or None,
                chroma_server_ssl_enabled=self.chroma_server_ssl_enabled,
            )
            client = Client(settings=chroma_settings)

        # Check persist_directory and expand it if it is a relative path
        persist_directory = self.resolve_path(self.persist_directory) if self.persist_directory is not None else None

        chroma = Chroma(
            persist_directory=persist_directory,
            client=client,
            embedding_function=self.embedding,
            collection_name=self.collection_name,
        )

        self._add_documents_to_vector_store(chroma)
        self.status = chroma_collection_to_data(chroma.get(limit=self.limit))
        return chroma

    def _add_documents_to_vector_store(self, vector_store: "Chroma") -> None:
        """Adds documents to the Vector Store."""
        ingest_data: list | Data | DataFrame = self.ingest_data
        if not ingest_data:
            self.status = ""
            return

        # Convert DataFrame to Data if needed using parent's method
        ingest_data = self._prepare_ingest_data()

        stored_documents_without_id = []
        if self.allow_duplicates:
            stored_data = []
        else:
            stored_data = chroma_collection_to_data(vector_store.get(limit=self.limit))
            for value in deepcopy(stored_data):
                del value.id
                stored_documents_without_id.append(value)

        documents = []
        for _input in ingest_data or []:
            if isinstance(_input, Data):
                if _input not in stored_documents_without_id:
                    documents.append(_input.to_lc_document())
            else:
                msg = "向量存储输入必须是 Data 对象。"  # "Vector Store Inputs must be Data objects."
                raise TypeError(msg)

        if documents and self.embedding is not None:
            self.log(f"正在向向量存储中添加 {len(documents)} 个文档。")  # "Adding {len(documents)} documents to the Vector Store."
            vector_store.add_documents(documents)
        else:
            self.log("没有文档可添加到向量存储中。")  # "No documents to add to the Vector Store."
