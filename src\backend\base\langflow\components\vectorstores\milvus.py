from langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store
from langflow.helpers.data import docs_to_data
from langflow.io import (
    BoolInput,
    DictInput,
    DropdownInput,
    FloatInput,
    HandleInput,
    IntInput,
    SecretStrInput,
    StrInput,
)
from langflow.schema import Data


class MilvusVectorStoreComponent(LCVectorStoreComponent):
    """Milvus vector store with search capabilities."""

    display_name: str = "Milvus"
    description: str = "Milvus 向量存储，具有搜索功能。"  # "Milvus vector store with search capabilities"
    name = "Milvus"
    icon = "Milvus"

    inputs = [
        StrInput(
            name="collection_name",
            display_name="集合名称",  # "Collection Name"
            value="langflow",
        ),
        StrInput(
            name="collection_description",
            display_name="集合描述",  # "Collection Description"
            value="",
        ),
        StrInput(
            name="uri",
            display_name="连接 URI",  # "Connection URI"
            value="http://localhost:19530",
        ),
        SecretStrInput(
            name="password",
            display_name="令牌",  # "Token"
            value="",
            info="如果不需要令牌进行连接，请忽略此字段。",  # "Ignore this field if no token is required to make connection."
        ),
        DictInput(
            name="connection_args",
            display_name="其他连接参数",  # "Other Connection Arguments"
            advanced=True,
        ),
        StrInput(
            name="primary_field",
            display_name="主字段名称",  # "Primary Field Name"
            value="pk",
        ),
        StrInput(
            name="text_field",
            display_name="文本字段名称",  # "Text Field Name"
            value="text",
        ),
        StrInput(
            name="vector_field",
            display_name="向量字段名称",  # "Vector Field Name"
            value="vector",
        ),
        DropdownInput(
            name="consistency_level",
            display_name="一致性级别",  # "Consistency Level"
            options=["Bounded", "Session", "Strong", "Eventual"],
            value="Session",
            advanced=True,
        ),
        DictInput(
            name="index_params",
            display_name="索引参数",  # "Index Parameters"
            advanced=True,
        ),
        DictInput(
            name="search_params",
            display_name="搜索参数",  # "Search Parameters"
            advanced=True,
        ),
        BoolInput(
            name="drop_old",
            display_name="删除旧集合",  # "Drop Old Collection"
            value=False,
            advanced=True,
        ),
        FloatInput(
            name="timeout",
            display_name="超时时间",  # "Timeout"
            advanced=True,
        ),
        *LCVectorStoreComponent.inputs,
        HandleInput(
            name="embedding",
            display_name="嵌入",  # "Embedding"
            input_types=["Embeddings"],
        ),
        IntInput(
            name="number_of_results",
            display_name="结果数量",  # "Number of Results"
            info="要返回的结果数量。",  # "Number of results to return."
            value=4,
            advanced=True,
        ),
    ]

    @check_cached_vector_store
    def build_vector_store(self):
        try:
            from langchain_milvus.vectorstores import Milvus as LangchainMilvus
        except ImportError as e:
            msg = (
                "无法导入 Milvus 集成包。"  # "Could not import Milvus integration package."
                "请使用 `pip install langchain-milvus` 安装。"  # "Please install it with `pip install langchain-milvus`."
            )
            raise ImportError(msg) from e
        self.connection_args.update(uri=self.uri, token=self.password)
        milvus_store = LangchainMilvus(
            embedding_function=self.embedding,
            collection_name=self.collection_name,
            collection_description=self.collection_description,
            connection_args=self.connection_args,
            consistency_level=self.consistency_level,
            index_params=self.index_params,
            search_params=self.search_params,
            drop_old=self.drop_old,
            auto_id=True,
            primary_field=self.primary_field,
            text_field=self.text_field,
            vector_field=self.vector_field,
            timeout=self.timeout,
        )

        # Convert DataFrame to Data if needed using parent's method
        self.ingest_data = self._prepare_ingest_data()

        documents = []
        for _input in self.ingest_data or []:
            if isinstance(_input, Data):
                documents.append(_input.to_lc_document())
            else:
                documents.append(_input)

        if documents:
            milvus_store.add_documents(documents)

        return milvus_store

    def search_documents(self) -> list[Data]:
        vector_store = self.build_vector_store()

        if self.search_query and isinstance(self.search_query, str) and self.search_query.strip():
            docs = vector_store.similarity_search(
                query=self.search_query,
                k=self.number_of_results,
            )

            data = docs_to_data(docs)
            self.status = data
            return data
        return []
