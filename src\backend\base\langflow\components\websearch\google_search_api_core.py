from langchain_google_community import GoogleSearchAPIWrapper

from langflow.custom import Component
from langflow.io import IntInput, MultilineInput, Output, SecretStrInput
from langflow.schema import DataFrame


class GoogleSearchAPICore(Component):
    display_name = "Google 搜索 API"
    description = "调用 Google 搜索 API 并以数据框形式返回结果。"  # "Call Google Search API and return results as a DataFrame."
    icon = "Google"

    inputs = [
        SecretStrInput(
            name="google_api_key",
            display_name="Google API 密钥",  # "Google API Key"
            required=True,
        ),
        SecretStrInput(
            name="google_cse_id",
            display_name="Google CSE ID",  # "Google CSE ID"
            required=True,
        ),
        MultilineInput(
            name="input_value",
            display_name="输入",  # "Input"
            tool_mode=True,
        ),
        IntInput(
            name="k",
            display_name="结果数量",  # "Number of results"
            value=4,
            required=True,
        ),
    ]

    outputs = [
        Output(
            display_name="结果",  # "Results"
            name="results",
            type_=DataFrame,
            method="search_google",
        ),
    ]

    def search_google(self) -> DataFrame:
        """使用提供的查询搜索 Google。"""  # "Search Google using the provided query."
        if not self.google_api_key:
            return DataFrame([{"error": "无效的 Google API 密钥"}])  # "Invalid Google API Key"

        if not self.google_cse_id:
            return DataFrame([{"error": "无效的 Google CSE ID"}])  # "Invalid Google CSE ID"

        try:
            wrapper = GoogleSearchAPIWrapper(
                google_api_key=self.google_api_key, google_cse_id=self.google_cse_id, k=self.k
            )
            results = wrapper.results(query=self.input_value, num_results=self.k)
            return DataFrame(results)
        except (ValueError, KeyError) as e:
            return DataFrame([{"error": f"无效的配置：{e!s}"}])  # "Invalid configuration: {e!s}"
        except ConnectionError as e:
            return DataFrame([{"error": f"连接错误：{e!s}"}])  # "Connection error: {e!s}"
        except RuntimeError as e:
            return DataFrame([{"error": f"搜索时发生错误：{e!s}"}])  # "Error occurred while searching: {e!s}"

    def build(self):
        return self.search_google
