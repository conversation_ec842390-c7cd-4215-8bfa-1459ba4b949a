import httpx
from loguru import logger

from langflow.custom import Component
from langflow.helpers.data import data_to_text
from langflow.io import BoolInput, DropdownInput, IntInput, MessageTextInput, Output, SecretStrInput
from langflow.schema import Data
from langflow.schema.message import Message


class TavilySearchComponent(Component):
    display_name = "Tavily AI 搜索"  # "Tavily AI Search"
    description = """**Tavily AI** 是一个针对 LLM 和 RAG 优化的搜索引擎，\
        旨在提供高效、快速和持久的搜索结果。"""  # "**Tavily AI** is a search engine optimized for LLMs and RAG, aimed at efficient, quick, and persistent search results."
    icon = "TavilyIcon"

    inputs = [
        SecretStrInput(
            name="api_key",
            display_name="Tavily API 密钥",  # "Tavily API Key"
            required=True,
            info="您的 Tavily API 密钥。",  # "Your Tavily API Key."
        ),
        MessageTextInput(
            name="query",
            display_name="搜索查询",  # "Search Query"
            info="您希望使用 Tavily 执行的搜索查询。",  # "The search query you want to execute with Tavily."
            tool_mode=True,
        ),
        DropdownInput(
            name="search_depth",
            display_name="搜索深度",  # "Search Depth"
            info="搜索的深度。",  # "The depth of the search."
            options=["basic", "advanced"],  # ["basic", "advanced"]
            value="高级",  # "advanced"
            advanced=True,
        ),
        DropdownInput(
            name="topic",
            display_name="搜索主题",  # "Search Topic"
            info="搜索的类别。",  # "The category of the search."
            options=["general", "news"],  # ["general", "news"]
            value="通用",  # "general"
            advanced=True,
        ),
        DropdownInput(
            name="time_range",
            display_name="时间范围",  # "Time Range"
            info="从当前日期开始，搜索结果包含的时间范围。",  # "The time range back from the current date to include in the search results."
            options=["day", "week", "month", "year"],  # ["day", "week", "month", "year"]
            value=None,
            advanced=True,
            combobox=True,
        ),
        IntInput(
            name="max_results",
            display_name="最大结果数",  # "Max Results"
            info="返回的最大搜索结果数。",  # "The maximum number of search results to return."
            value=5,
            advanced=True,
        ),
        BoolInput(
            name="include_images",
            display_name="包含图片",  # "Include Images"
            info="在响应中包含与查询相关的图片列表。",  # "Include a list of query-related images in the response."
            value=True,
            advanced=True,
        ),
        BoolInput(
            name="include_answer",
            display_name="包含答案",  # "Include Answer"
            info="包含对原始查询的简短答案。",  # "Include a short answer to the original query."
            value=True,
            advanced=True,
        ),
    ]

    outputs = [
        Output(display_name="数据", name="data", method="fetch_content"),  # "Data"
        Output(display_name="文本", name="text", method="fetch_content_text"),  # "Text"
    ]

    def fetch_content(self) -> list[Data]:
        try:
            url = "https://api.tavily.com/search"
            headers = {
                "content-type": "application/json",
                "accept": "application/json",
            }
            payload = {
                "api_key": self.api_key,
                "query": self.query,
                "search_depth": self.search_depth,
                "topic": self.topic,
                "max_results": self.max_results,
                "include_images": self.include_images,
                "include_answer": self.include_answer,
                "time_range": self.time_range,
            }

            with httpx.Client() as client:
                response = client.post(url, json=payload, headers=headers)

            response.raise_for_status()
            search_results = response.json()

            data_results = []

            if self.include_answer and search_results.get("answer"):
                data_results.append(Data(text=search_results["answer"]))

            for result in search_results.get("results", []):
                content = result.get("content", "")
                data_results.append(
                    Data(
                        text=content,
                        data={
                            "title": result.get("title"),
                            "url": result.get("url"),
                            "content": content,
                            "score": result.get("score"),
                        },
                    )
                )

            if self.include_images and search_results.get("images"):
                data_results.append(Data(text="找到图片", data={"images": search_results["images"]}))  # "Images found"
        except httpx.HTTPStatusError as exc:
            error_message = f"HTTP 错误发生：{exc.response.status_code} - {exc.response.text}"  # "HTTP error occurred: {exc.response.status_code} - {exc.response.text}"
            logger.error(error_message)
            return [Data(text=error_message, data={"error": error_message})]
        except httpx.RequestError as exc:
            error_message = f"请求错误发生：{exc}"  # "Request error occurred: {exc}"
            logger.error(error_message)
            return [Data(text=error_message, data={"error": error_message})]
        except ValueError as exc:
            error_message = f"无效的响应格式：{exc}"  # "Invalid response format: {exc}"
            logger.error(error_message)
            return [Data(text=error_message, data={"error": error_message})]
        else:
            self.status = data_results
            return data_results

    def fetch_content_text(self) -> Message:
        data = self.fetch_content()
        result_string = data_to_text("{text}", data)
        self.status = result_string
        return Message(text=result_string)
