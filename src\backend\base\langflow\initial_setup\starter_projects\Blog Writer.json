{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ParseData", "id": "ParseData-4Sckw", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "references", "id": "Prompt-65R68", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "reactflow__edge-ParseData-4Sckw{œdataTypeœ:œParseDataœ,œidœ:œParseData-4Sckwœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-65R68{œfieldNameœ:œreferencesœ,œidœ:œPrompt-65R68œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "source": "ParseData-4Sckw", "sourceHandle": "{œdataTypeœ: œParseDataœ, œidœ: œParseData-4Sckwœ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-65R68", "targetHandle": "{œfieldNameœ: œreferencesœ, œidœ: œPrompt-65R68œ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "TextInput", "id": "TextInput-t88FI", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "instructions", "id": "Prompt-65R68", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "reactflow__edge-TextInput-t88FI{œdataTypeœ:œTextInputœ,œidœ:œTextInput-t88FIœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-65R68{œfieldNameœ:œinstructionsœ,œidœ:œPrompt-65R68œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "source": "TextInput-t88FI", "sourceHandle": "{œdataTypeœ: œTextInputœ, œidœ: œTextInput-t88FIœ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-65R68", "targetHandle": "{œfieldNameœ: œinstructionsœ, œidœ: œPrompt-65R68œ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}, {"className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-65R68", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "OpenAIModel-MyAsQ", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-65R68{œdataTypeœ:œPromptœ,œidœ:œPrompt-65R68œ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-OpenAIModel-MyAsQ{œfieldNameœ:œinput_valueœ,œidœ:œOpenAIModel-MyAsQœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "source": "Prompt-65R68", "sourceHandle": "{œdataTypeœ: œPromptœ, œidœ: œPrompt-65R68œ, œnameœ: œpromptœ, œoutput_typesœ: [œMessageœ]}", "target": "OpenAIModel-MyAsQ", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œOpenAIModel-MyAsQœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"className": "", "data": {"sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-MyAsQ", "name": "text_output", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-BE4YI", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-OpenAIModel-MyAsQ{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-MyAsQœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-BE4YI{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-BE4YIœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "source": "OpenAIModel-MyAsQ", "sourceHandle": "{œdataTypeœ: œOpenAIModelœ, œidœ: œOpenAIModel-MyAsQœ, œnameœ: œtext_outputœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-BE4YI", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-BE4YIœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"className": "", "data": {"sourceHandle": {"dataType": "URL", "id": "URL-EPEnt", "name": "data", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data", "id": "ParseData-4Sckw", "inputTypes": ["Data"], "type": "other"}}, "id": "reactflow__edge-URL-EPEnt{œdataTypeœ:œURLœ,œidœ:œURL-EPEntœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-ParseData-4Sckw{œfieldNameœ:œdataœ,œidœ:œParseData-4Sckwœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "source": "URL-EPEnt", "sourceHandle": "{œdataTypeœ: œURLœ, œidœ: œURL-EPEntœ, œnameœ: œdataœ, œoutput_typesœ: [œDataœ]}", "target": "ParseData-4Sckw", "targetHandle": "{œfieldNameœ: œdataœ, œidœ: œParseData-4Sckwœ, œinputTypesœ: [œDataœ], œtypeœ: œotherœ}"}], "nodes": [{"data": {"description": "Convert Data into plain text following a specified template.", "display_name": "Parse Data", "id": "ParseData-4Sckw", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Convert Data into plain text following a specified template.", "display_name": "Parse Data", "documentation": "", "edited": false, "field_order": ["data", "template", "sep"], "frozen": false, "icon": "message-square", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {"legacy_name": "解析数据"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "parse_data", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据列表", "method": "parse_data_as_list", "name": "data_list", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"数据转消息\"  # \"Data to Message\"\n    description = \"使用输入数据中的任意 {字段} 将 Data 对象转换为消息。\"  # \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n    metadata = {\n        \"legacy_name\": \"解析数据\",  # \"Parse Data\"\n    }\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"数据\",  # \"Data\"\n            info=\"要转换为文本的数据。\",  # \"The data to convert to text.\"\n            is_list=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"模板\",  # \"Template\"\n            info=(\n                \"用于格式化数据的模板。\"  # \"The template to use for formatting the data. \"\n\"它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。\"  # \"It can contain the keys {text}, {data} or any other key in the Data.\"\n            ),\n            value=\"{文本}\",\n            required=True,\n        ),\n        StrInput(\nname=\"sep\",\ndisplay_name=\"分隔符\",  # \"Separator\"\nadvanced=True,\nvalue=\"\\n\",\n),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"text\",\n            info=\"数据作为单个消息，每个输入数据由分隔符分隔。\",  # \"Data as a single Message, with each input Data separated by Separator.\"\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"数据列表\",  # \"Data List\"\n            name=\"data_list\",\n            info=\"数据作为新数据的列表，每个数据的 `text` 由模板格式化。\",  # \"Data as a list of new Data, each having `text` formatted by Template.\"\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n"}, "data": {"advanced": false, "display_name": "数据", "dynamic": false, "info": "要转换为文本的数据。", "input_types": ["Data"], "list": true, "name": "data", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "sep": {"advanced": true, "display_name": "分隔符", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "str", "value": "\n"}, "template": {"advanced": false, "display_name": "模板", "dynamic": false, "info": "用于格式化数据的模板。它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}}}, "type": "ParseData"}, "dragging": false, "height": 302, "id": "ParseData-4Sckw", "measured": {"height": 302, "width": 320}, "position": {"x": 955.6736985046297, "y": 702.7003891105396}, "positionAbsolute": {"x": 955.6736985046297, "y": 702.7003891105396}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "id": "Prompt-65R68", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": ["references", "instructions"]}, "description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "documentation": "", "edited": false, "field_order": ["template"], "frozen": false, "icon": "prompts", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "提示消息", "method": "build_prompt", "name": "prompt", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"提示模板\"  # \"Prompt\"\n    description: str = \"创建带有动态变量的提示模板。\"  # \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"  # \"\"\n    trace_type = \"prompt\"  # \"prompt\"\n    name = \"Prompt\"  # \"\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"模板\"),  # \"Template\"\n        MessageTextInput(\n            name=\"tool_placeholder\",  # \"tool_placeholder\"\n            display_name=\"工具占位符\",  # \"Tool Placeholder\"\n            tool_mode=True,\n            advanced=True,\n            info=\"工具模式的占位符输入。\",  # \"A placeholder input for tool mode.\"\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"提示消息\", name=\"prompt\", method=\"build_prompt\"),  # \"Prompt Message\"\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "instructions": {"advanced": false, "display_name": "instructions", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "instructions", "password": false, "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "references": {"advanced": false, "display_name": "references", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "references", "password": false, "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "template": {"advanced": false, "display_name": "模板", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "prompt", "value": "Reference 1:\n\n{references}\n\n---\n\n{instructions}\n\nBlog: \n\n"}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "工具占位符", "dynamic": false, "info": "工具模式的占位符输入。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}}, "type": "Prompt"}, "dragging": false, "height": 433, "id": "Prompt-65R68", "measured": {"height": 433, "width": 320}, "position": {"x": 1341.1018009526915, "y": 456.4098573354365}, "positionAbsolute": {"x": 1341.1018009526915, "y": 456.4098573354365}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Get text inputs from the Playground.", "display_name": "Instructions", "id": "TextInput-t88FI", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Get text inputs from the Playground.", "display_name": "Instructions", "documentation": "", "edited": false, "field_order": ["input_value"], "frozen": false, "icon": "type", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"文本输入\"  # \"Text Input\"\n    description = \"从练习场获取文本输入。\"  # \"Get text inputs from the Playground.\"\n    icon = \"type\"  # \"文本类型\"\n    name = \"TextInput\"  # \"\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输入传递的文本。\",  # \"Text to be passed as input.\"\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"消息\", name=\"text\", method=\"text_response\"),  # \"Message\"\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n"}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输入传递的文本。", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "Use the references above for style to write a new blog/tutorial about Langflow and AI. Suggest non-covered topics."}}}, "type": "TextInput"}, "dragging": false, "height": 234, "id": "TextInput-t88FI", "measured": {"height": 234, "width": 320}, "position": {"x": 955.8314364398983, "y": 402.24423846638155}, "positionAbsolute": {"x": 955.8314364398983, "y": 402.24423846638155}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Display a chat message in the Playground.", "display_name": "Chat Output", "id": "ChatOutput-BE4YI", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "store_message", "sender", "sender_name", "session_id", "data_template"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "基本清理数据", "dynamic": false, "info": "是否清理数据。", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"聊天输出\"  # \"Chat Output\"\n    description = \"在练习场中显示聊天消息。\"  # \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatOutput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输出传递的消息。\",  # \"Message to be passed as output.\"\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",  # \"data_template\"\n            display_name=\"数据模板\",  # \"Data Template\"\n            value=\"{text}\",\n            advanced=True,\n            info=\"用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。\",  # \"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\"\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",  # \"clean_data\"\n            display_name=\"基本清理数据\",  # \"Basic Clean Data\"\n            value=True,\n            info=\"是否清理数据。\",  # \"Whether to clean the data.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"advanced": true, "display_name": "数据模板", "dynamic": false, "info": "用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输出传递的消息。", "input_types": ["Data", "DataFrame", "Message"], "list": false, "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}}, "type": "ChatOutput"}, "dragging": false, "height": 234, "id": "ChatOutput-BE4YI", "measured": {"height": 234, "width": 320}, "position": {"x": 2097.489047349972, "y": 603.355618581002}, "positionAbsolute": {"x": 2113.228183852361, "y": 594.6116538574528}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-lriuW", "node": {"description": "## URL Component Setup\n\n**Purpose:**\nFetch and process content from the web to use as reference material for creating a blog post.\n\n**Instructions:**\n1. **Input URLs**: List the URLs of web pages whose content you want to fetch. Ensure the URLs start with `http://` or `https://`.\n2. **Select Output Format**:\n   - **Text**: To extract plain text from the pages.\n   - **Raw HTML**: To retrieve the raw HTML content for advanced uses.\n\n**Tips**:\n- Double-check URL formats to prevent any data fetching errors.\n- Use the '+' button to add multiple URLs as needed for comprehensive references.\n", "display_name": "", "documentation": "", "template": {"backgroundColor": "blue"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-lriuW", "measured": {"height": 324, "width": 325}, "position": {"x": 484.73635938598477, "y": 153.29803159918163}, "positionAbsolute": {"x": 484.73635938598477, "y": 153.29803159918163}, "resizing": false, "selected": false, "style": {"height": 329, "width": 324}, "type": "noteNode", "width": 324}, {"data": {"id": "note-1oPzi", "node": {"description": "# Blog Writing Flow Overview\n\n**Workflow Description:**\nThis flow assists in creating a blog post by using content fetched from URLs and user-provided instructions. It combines external references and user inputs to generate coherent and context-rich text.\n\n**Components**:\n1. **URL Component**: Fetches reference content from specified web pages.\n2. **Parse Data**: Converts the fetched content into a text format.\n3. **Text Input**: Accepts user-specific instructions for the blog post.\n4. **Prompt with Variables**: Merges references and instructions into a dynamic writing prompt.\n5. **OpenAI Model**: Generates the blog post using an AI language model.\n6. **Chat Output**: Displays the final blog text for user review and further refinement.\n\n**Steps to Execute**:\n1. Enter the relevant URLs and specify the output format in the **URL Component**.\n2. Provide detailed writing **Instructions** for AI to follow.\n3. Run the flow to generate the blog and view the result in **Chat Output**.\n\n**Benefits**:\n- Simplifies blog creation by using AI to structure and write content.\n- Incorporates comprehensive reference material to enhance post depth and accuracy.", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "height": 486, "id": "note-1oPzi", "measured": {"height": 486, "width": 325}, "position": {"x": -78.**************, "y": 405.**************}, "positionAbsolute": {"x": -78.**************, "y": 405.**************}, "resizing": false, "selected": false, "style": {"height": 509, "width": 562}, "type": "noteNode", "width": 324}, {"data": {"id": "note-qhsOB", "node": {"description": "## Get Your OpenAI API Key\n**Steps**:\n1. **Visit** [OpenAI's API Key Page](https://platform.openai.com/api-keys).\n\n2. **Log In/Sign Up**:\n   - Log in or create a new OpenAI account.\n\n3. **Generate API Key**:\n   - Click \"Create New Secret Key\" to obtain your key.\n\n4. **Store Your Key Securely**:\n   - Note it down as it will only display once.\n\n5. **Enter API Key**:\n   - Input your key in the OpenAI API Key field within the component setup.\n\nKeep your key safe and manage it responsibly!", "display_name": "", "documentation": "", "template": {"backgroundColor": "rose"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-qhsOB", "measured": {"height": 324, "width": 325}, "position": {"x": 1702.************, "y": 121.**************}, "positionAbsolute": {"x": 1703.************, "y": 125.**************}, "resizing": false, "selected": false, "style": {"height": 324, "width": 343}, "type": "noteNode", "width": 324}, {"data": {"id": "OpenAIModel-MyAsQ", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "category": "models", "conditional_paths": [], "custom_fields": {}, "description": "Generates text using OpenAI LLMs.", "display_name": "OpenAI", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed"], "frozen": false, "icon": "OpenAI", "key": "OpenAIModel", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "语言模型", "method": "build_model", "name": "model_output", "required_inputs": ["api_key", "model_name"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.001, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "用于 OpenAI 模型的 OpenAI API 密钥。", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput\n\n# 替换硬编码的字符串为中文，并保留英文注释\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"  # OpenAI\n    description = \"使用 OpenAI LLMs 生成文本。\"  # Generates text using OpenAI LLMs.\n    icon = \"OpenAI\"  # OpenAI\n    name = \"OpenAIModel\"  # OpenAIModel\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"最大 Token 数\",  # Max Tokens\n            advanced=True,\n            info=\"生成的最大 token 数。设置为 0 表示无限制。\"  # The maximum number of tokens to generate. Set to 0 for unlimited tokens.\n            ,\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # Model Kwargs\n            advanced=True,\n            info=\"传递给模型的其他关键字参数。\"  # Additional keyword arguments to pass to the model.\n            ,\n        ),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON 模式\",  # JSON Mode\n            advanced=True,\n            info=\"如果为 True，则无论是否传递 schema，都会输出 JSON。\"  # If True, it will output JSON regardless of passing a schema.\n            ,\n        ),\n        StrInput(\n            name=\"model_name\",\n            display_name=\"模型名称\",  # Model Name\n            advanced=False,\n            info=\"所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、\",\n            required=True\n        ),\n        # DropdownInput(\n        #     name=\"model_name\",\n        #     display_name=\"模型名称\",  # Model Name\n        #     advanced=False,\n        #     options=OPENAI_MODEL_NAMES,\n        #     value=OPENAI_MODEL_NAMES[1],\n        #     combobox=True,\n        # ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础地址\",  # OpenAI API Base\n            advanced=True,\n            info=\"OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。\"\n            \"您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。\"  # The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.\n            ,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API 密钥\",  # OpenAI API Key\n            info=\"用于 OpenAI 模型的 OpenAI API 密钥。\"  # The OpenAI API Key to use for the OpenAI model.\n            ,\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"温度\",  # Temperature\n            value=0.1,\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n        IntInput(\n            name=\"seed\",\n            display_name=\"随机种子\",  # Seed\n            info=\"随机种子控制任务的可重复性。\"  # The seed controls the reproducibility of the job.\n            ,\n            advanced=True,\n            value=1,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # Max Retries\n            info=\"生成时的最大重试次数。\"  # The maximum number of retries to make when generating.\n            ,\n            advanced=True,\n            value=5,\n        ),\n        IntInput(\n            name=\"timeout\",\n            display_name=\"超时时间\",  # Timeout\n            info=\"请求 OpenAI 完成 API 的超时时间。\"  # The timeout for requests to OpenAI completion API.\n            ,\n            advanced=True,\n            value=700,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        openai_api_key = self.api_key\n        temperature = self.temperature\n        model_name: str = self.model_name\n        max_tokens = self.max_tokens\n        model_kwargs = self.model_kwargs or {}\n        openai_api_base = self.openai_api_base or \"https://api.openai.com/v1\"\n        json_mode = self.json_mode\n        seed = self.seed\n        max_retries = self.max_retries\n        timeout = self.timeout\n\n        api_key = SecretStr(openai_api_key).get_secret_value() if openai_api_key else None\n        output = ChatOpenAI(\n            max_tokens=max_tokens or None,\n            model_kwargs=model_kwargs,\n            model=model_name,\n            base_url=openai_api_base,\n            api_key=api_key,\n            temperature=temperature if temperature is not None else 0.1,\n            seed=seed,\n            max_retries=max_retries,\n            request_timeout=timeout,\n        )\n        if json_mode:\n            output = output.bind(response_format={\"type\": \"json_object\"})\n\n        return output\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"Get a message from an OpenAI exception.\n\n        Args:\n            e (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")\n            if message:\n                return message\n        return None\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON 模式", "dynamic": false, "info": "如果为 True，则无论是否传递 schema，都会输出 JSON。", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "生成时的最大重试次数。", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "最大 Token 数", "dynamic": false, "info": "生成的最大 token 数。设置为 0 表示无限制。", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "传递给模型的其他关键字参数。", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "模型名称", "dynamic": false, "info": "所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API 基础地址", "dynamic": false, "info": "OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "随机种子", "dynamic": false, "info": "随机种子控制任务的可重复性。", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "流式输出", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "系统消息", "dynamic": false, "info": "传递给模型的系统消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "温度", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "超时时间", "dynamic": false, "info": "请求 OpenAI 完成 API 的超时时间。", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}}, "tool_mode": false}, "showNode": true, "type": "OpenAIModel"}, "dragging": false, "id": "OpenAIModel-MyAsQ", "measured": {"height": 653, "width": 320}, "position": {"x": 1715.4141756503298, "y": 434.13478201304184}, "selected": false, "type": "genericNode"}, {"data": {"id": "URL-EPEnt", "node": {"base_classes": ["Data", "DataFrame", "Message"], "beta": false, "category": "data", "conditional_paths": [], "custom_fields": {}, "description": "Load and retrive data from specified URLs.", "display_name": "URL", "documentation": "", "edited": false, "field_order": ["urls", "format"], "frozen": false, "icon": "layout-template", "key": "URL", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Data", "method": "fetch_content", "name": "data", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Text", "method": "fetch_content_text", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "DataFrame", "method": "as_dataframe", "name": "dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "score": 2.220446049250313e-16, "template": {"_type": "Component", "clean_extra_whitespace": {"_input_type": "BoolInput", "advanced": false, "display_name": "Clean Extra Whitespace", "dynamic": false, "info": "Whether to clean excessive blank lines in the text output. Only applies to 'Text' format.", "list": false, "list_add_label": "Add More", "name": "clean_extra_whitespace", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import asyncio\nimport json\nimport re\n\nimport aiohttp\nfrom langchain_community.document_loaders import AsyncHtmlLoader, WebBaseLoader\n\nfrom langflow.custom import Component\nfrom langflow.io import BoolInput, DropdownInput, MessageTextInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\n\n\nclass URLComponent(Component):\n    display_name = \"URL\"\n    description = (\n        \"Load and retrieve data from specified URLs. Supports output in plain text, raw HTML, \"\n        \"or JSON, with options for cleaning and separating multiple outputs.\"\n    )\n    icon = \"layout-template\"\n    name = \"URL\"\n\n    inputs = [\n        MessageTextInput(\n            name=\"urls\",\n            display_name=\"URLs\",\n            is_list=True,\n            tool_mode=True,\n            placeholder=\"Enter a URL...\",\n            list_add_label=\"Add URL\",\n        ),\n        DropdownInput(\n            name=\"format\",\n            display_name=\"Output Format\",\n            info=(\n                \"Output Format. Use 'Text' to extract text from the HTML, 'Raw HTML' for the raw HTML \"\n                \"content, or 'JSON' to extract JSON from the HTML.\"\n            ),\n            options=[\"Text\", \"Raw HTML\", \"JSON\"],\n            value=\"Text\",\n            real_time_refresh=True,\n        ),\n        StrInput(\n            name=\"separator\",\n            display_name=\"Separator\",\n            value=\"\\n\\n\",\n            show=True,\n            info=(\n                \"Specify the separator to use between multiple outputs. Default for Text is '\\\\n\\\\n'. \"\n                \"Default for Raw HTML is '\\\\n<!-- Separator -->\\\\n'.\"\n            ),\n        ),\n        BoolInput(\n            name=\"clean_extra_whitespace\",\n            display_name=\"Clean Extra Whitespace\",\n            value=True,\n            show=True,\n            info=\"Whether to clean excessive blank lines in the text output. Only applies to 'Text' format.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Data\", name=\"data\", method=\"fetch_content\"),\n        Output(display_name=\"Text\", name=\"text\", method=\"fetch_content_text\"),\n        Output(display_name=\"DataFrame\", name=\"dataframe\", method=\"as_dataframe\"),\n    ]\n\n    async def validate_json_content(self, url: str) -> bool:\n        \"\"\"Validates if the URL content is actually JSON.\"\"\"\n        try:\n            async with aiohttp.ClientSession() as session, session.get(url) as response:\n                http_ok = 200\n                if response.status != http_ok:\n                    return False\n\n                content = await response.text()\n                try:\n                    json.loads(content)\n                except json.JSONDecodeError:\n                    return False\n                else:\n                    return True\n        except (aiohttp.ClientError, asyncio.TimeoutError):\n            # Log specific error for debugging if needed\n            return False\n\n    def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:\n        \"\"\"Dynamically update fields based on selected format.\"\"\"\n        if field_name == \"format\":\n            is_text_mode = field_value == \"Text\"\n            is_json_mode = field_value == \"JSON\"\n            build_config[\"separator\"][\"value\"] = \"\\n\\n\" if is_text_mode else \"\\n<!-- Separator -->\\n\"\n            build_config[\"clean_extra_whitespace\"][\"show\"] = is_text_mode\n            build_config[\"separator\"][\"show\"] = not is_json_mode\n        return build_config\n\n    def ensure_url(self, string: str) -> str:\n        \"\"\"Ensures the given string is a valid URL.\"\"\"\n        if not string.startswith((\"http://\", \"https://\")):\n            string = \"http://\" + string\n\n        url_regex = re.compile(\n            r\"^(https?:\\/\\/)?\"\n            r\"(www\\.)?\"\n            r\"([a-zA-Z0-9.-]+)\"\n            r\"(\\.[a-zA-Z]{2,})?\"\n            r\"(:\\d+)?\"\n            r\"(\\/[^\\s]*)?$\",\n            re.IGNORECASE,\n        )\n\n        error_msg = \"Invalid URL - \" + string\n        if not url_regex.match(string):\n            raise ValueError(error_msg)\n\n        return string\n\n    def fetch_content(self) -> list[Data]:\n        \"\"\"Fetch content based on selected format.\"\"\"\n        urls = list({self.ensure_url(url.strip()) for url in self.urls if url.strip()})\n\n        no_urls_msg = \"No valid URLs provided.\"\n        if not urls:\n            raise ValueError(no_urls_msg)\n\n        # If JSON format is selected, validate JSON content first\n        if self.format == \"JSON\":\n            for url in urls:\n                is_json = asyncio.run(self.validate_json_content(url))\n                if not is_json:\n                    error_msg = \"Invalid JSON content from URL - \" + url\n                    raise ValueError(error_msg)\n\n        if self.format == \"Raw HTML\":\n            loader = AsyncHtmlLoader(web_path=urls, encoding=\"utf-8\")\n        else:\n            loader = WebBaseLoader(web_paths=urls, encoding=\"utf-8\")\n\n        docs = loader.load()\n\n        if self.format == \"JSON\":\n            data = []\n            for doc in docs:\n                try:\n                    json_content = json.loads(doc.page_content)\n                    data_dict = {\"text\": json.dumps(json_content, indent=2), **json_content, **doc.metadata}\n                    data.append(Data(**data_dict))\n                except json.JSONDecodeError as err:\n                    source = doc.metadata.get(\"source\", \"unknown URL\")\n                    error_msg = \"Invalid JSON content from \" + source\n                    raise ValueError(error_msg) from err\n            return data\n\n        return [Data(text=doc.page_content, **doc.metadata) for doc in docs]\n\n    def fetch_content_text(self) -> Message:\n        \"\"\"Fetch content and return as formatted text.\"\"\"\n        data = self.fetch_content()\n\n        if self.format == \"JSON\":\n            text_list = [item.text for item in data]\n            result = \"\\n\".join(text_list)\n        else:\n            text_list = [item.text for item in data]\n            if self.format == \"Text\" and self.clean_extra_whitespace:\n                text_list = [re.sub(r\"\\n{3,}\", \"\\n\\n\", text) for text in text_list]\n            result = self.separator.join(text_list)\n\n        self.status = result\n        return Message(text=result)\n\n    def as_dataframe(self) -> DataFrame:\n        \"\"\"Return fetched content as a DataFrame.\"\"\"\n        return DataFrame(self.fetch_content())\n"}, "format": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Output Format", "dynamic": false, "info": "Output Format. Use 'Text' to extract text from the HTML, 'Raw HTML' for the raw HTML content, or 'JSON' to extract JSON from the HTML.", "name": "format", "options": ["Text", "Raw HTML", "JSON"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Text"}, "separator": {"_input_type": "StrInput", "advanced": false, "display_name": "Separator", "dynamic": false, "info": "Specify the separator to use between multiple outputs. Default for Text is '\\n\\n'. Default for Raw HTML is '\\n<!-- Separator -->\\n'.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "separator", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n\n"}, "urls": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "URLs", "dynamic": false, "info": "", "input_types": ["Message"], "list": true, "list_add_label": "Add URL", "load_from_db": false, "name": "urls", "placeholder": "Enter a URL...", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ["https://langflow.org/", "https://docs.langflow.org/"]}}, "tool_mode": false}, "showNode": true, "type": "URL"}, "dragging": false, "id": "URL-EPEnt", "measured": {"height": 465, "width": 320}, "position": {"x": 498.72695054312635, "y": 554.4485732587549}, "selected": false, "type": "genericNode"}], "viewport": {"x": 107.17740467666556, "y": 281.84175643433355, "zoom": 0.5274085584390961}}, "description": "根据指令和参考文章自动生成定制化的博客文章。", "endpoint_name": null, "gradient": "4", "icon": "NotebookPen", "id": "8b12aa0f-8b59-4806-a01f-5e545b5b1688", "is_component": false, "last_tested_version": "1.0.19.post2", "name": "博客写作助手", "tags": ["chatbots", "content-generation"]}