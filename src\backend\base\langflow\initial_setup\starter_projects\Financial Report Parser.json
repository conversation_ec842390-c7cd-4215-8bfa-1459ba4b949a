{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-nRbVG", "name": "model_output", "output_types": ["LanguageModel"]}, "targetHandle": {"fieldName": "llm", "id": "StructuredOutput-KQHp8", "inputTypes": ["LanguageModel"], "type": "other"}}, "id": "reactflow__edge-OpenAIModel-nRbVG{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-nRbVGœ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}-StructuredOutput-KQHp8{œfieldNameœ:œllmœ,œidœ:œStructuredOutput-KQHp8œ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIModel-nRbVG", "sourceHandle": "{œdataTypeœ: œOpenAIModelœ, œidœ: œOpenAIModel-nRbVGœ, œnameœ: œmodel_outputœ, œoutput_typesœ: [œLanguageModelœ]}", "target": "StructuredOutput-KQHp8", "targetHandle": "{œfieldNameœ: œllmœ, œidœ: œStructuredOutput-KQHp8œ, œinputTypesœ: [œLanguageModelœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-dq6uf", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "StructuredOutput-KQHp8", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-dq6uf{œdataTypeœ:œChatInputœ,œidœ:œChatInput-dq6ufœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-StructuredOutput-KQHp8{œfieldNameœ:œinput_valueœ,œidœ:œStructuredOutput-KQHp8œ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-dq6uf", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-dq6ufœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "StructuredOutput-KQHp8", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œStructuredOutput-KQHp8œ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ParseDataFrame", "id": "ParseDataFrame-QOEC3", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-BNHyf", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-ParseDataFrame-QOEC3{œdataTypeœ:œParseDataFrameœ,œidœ:œParseDataFrame-QOEC3œ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-BNHyf{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-BNHyfœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ParseDataFrame-QOEC3", "sourceHandle": "{œdataTypeœ: œParseDataFrameœ, œidœ: œParseDataFrame-QOEC3œ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-BNHyf", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-BNHyfœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "StructuredOutput", "id": "StructuredOutput-KQHp8", "name": "structured_output_dataframe", "output_types": ["DataFrame"]}, "targetHandle": {"fieldName": "df", "id": "ParseDataFrame-QOEC3", "inputTypes": ["DataFrame"], "type": "other"}}, "id": "reactflow__edge-StructuredOutput-KQHp8{œdataTypeœ:œStructuredOutputœ,œidœ:œStructuredOutput-KQHp8œ,œnameœ:œstructured_output_dataframeœ,œoutput_typesœ:[œDataFrameœ]}-ParseDataFrame-QOEC3{œfieldNameœ:œdfœ,œidœ:œParseDataFrame-QOEC3œ,œinputTypesœ:[œDataFrameœ],œtypeœ:œotherœ}", "selected": false, "source": "StructuredOutput-KQHp8", "sourceHandle": "{œdataTypeœ: œStructuredOutputœ, œidœ: œStructuredOutput-KQHp8œ, œnameœ: œstructured_output_dataframeœ, œoutput_typesœ: [œDataFrameœ]}", "target": "ParseDataFrame-QOEC3", "targetHandle": "{œfieldNameœ: œdfœ, œidœ: œParseDataFrame-QOEC3œ, œinputTypesœ: [œDataFrameœ], œtypeœ: œotherœ}"}], "nodes": [{"data": {"id": "OpenAIModel-nRbVG", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "category": "models", "conditional_paths": [], "custom_fields": {}, "description": "Generates text using OpenAI LLMs.", "display_name": "OpenAI", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed", "max_retries", "timeout"], "frozen": false, "icon": "OpenAI", "key": "OpenAIModel", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "语言模型", "method": "build_model", "name": "model_output", "required_inputs": ["api_key", "model_name"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.001, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "用于 OpenAI 模型的 OpenAI API 密钥。", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput\n\n# 替换硬编码的字符串为中文，并保留英文注释\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"  # OpenAI\n    description = \"使用 OpenAI LLMs 生成文本。\"  # Generates text using OpenAI LLMs.\n    icon = \"OpenAI\"  # OpenAI\n    name = \"OpenAIModel\"  # OpenAIModel\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"最大 Token 数\",  # Max Tokens\n            advanced=True,\n            info=\"生成的最大 token 数。设置为 0 表示无限制。\"  # The maximum number of tokens to generate. Set to 0 for unlimited tokens.\n            ,\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # Model Kwargs\n            advanced=True,\n            info=\"传递给模型的其他关键字参数。\"  # Additional keyword arguments to pass to the model.\n            ,\n        ),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON 模式\",  # JSON Mode\n            advanced=True,\n            info=\"如果为 True，则无论是否传递 schema，都会输出 JSON。\"  # If True, it will output JSON regardless of passing a schema.\n            ,\n        ),\n        StrInput(\n            name=\"model_name\",\n            display_name=\"模型名称\",  # Model Name\n            advanced=False,\n            info=\"所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、\",\n            required=True\n        ),\n        # DropdownInput(\n        #     name=\"model_name\",\n        #     display_name=\"模型名称\",  # Model Name\n        #     advanced=False,\n        #     options=OPENAI_MODEL_NAMES,\n        #     value=OPENAI_MODEL_NAMES[1],\n        #     combobox=True,\n        # ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础地址\",  # OpenAI API Base\n            advanced=True,\n            info=\"OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。\"\n            \"您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。\"  # The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.\n            ,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API 密钥\",  # OpenAI API Key\n            info=\"用于 OpenAI 模型的 OpenAI API 密钥。\"  # The OpenAI API Key to use for the OpenAI model.\n            ,\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"温度\",  # Temperature\n            value=0.1,\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n        IntInput(\n            name=\"seed\",\n            display_name=\"随机种子\",  # Seed\n            info=\"随机种子控制任务的可重复性。\"  # The seed controls the reproducibility of the job.\n            ,\n            advanced=True,\n            value=1,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # Max Retries\n            info=\"生成时的最大重试次数。\"  # The maximum number of retries to make when generating.\n            ,\n            advanced=True,\n            value=5,\n        ),\n        IntInput(\n            name=\"timeout\",\n            display_name=\"超时时间\",  # Timeout\n            info=\"请求 OpenAI 完成 API 的超时时间。\"  # The timeout for requests to OpenAI completion API.\n            ,\n            advanced=True,\n            value=700,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        openai_api_key = self.api_key\n        temperature = self.temperature\n        model_name: str = self.model_name\n        max_tokens = self.max_tokens\n        model_kwargs = self.model_kwargs or {}\n        openai_api_base = self.openai_api_base or \"https://api.openai.com/v1\"\n        json_mode = self.json_mode\n        seed = self.seed\n        max_retries = self.max_retries\n        timeout = self.timeout\n\n        api_key = SecretStr(openai_api_key).get_secret_value() if openai_api_key else None\n        output = ChatOpenAI(\n            max_tokens=max_tokens or None,\n            model_kwargs=model_kwargs,\n            model=model_name,\n            base_url=openai_api_base,\n            api_key=api_key,\n            temperature=temperature if temperature is not None else 0.1,\n            seed=seed,\n            max_retries=max_retries,\n            request_timeout=timeout,\n        )\n        if json_mode:\n            output = output.bind(response_format={\"type\": \"json_object\"})\n\n        return output\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"Get a message from an OpenAI exception.\n\n        Args:\n            e (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")\n            if message:\n                return message\n        return None\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON 模式", "dynamic": false, "info": "如果为 True，则无论是否传递 schema，都会输出 JSON。", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "生成时的最大重试次数。", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "最大 Token 数", "dynamic": false, "info": "生成的最大 token 数。设置为 0 表示无限制。", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "传递给模型的其他关键字参数。", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "模型名称", "dynamic": false, "info": "所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API 基础地址", "dynamic": false, "info": "OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "随机种子", "dynamic": false, "info": "随机种子控制任务的可重复性。", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "流式输出", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "系统消息", "dynamic": false, "info": "传递给模型的系统消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "温度", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "超时时间", "dynamic": false, "info": "请求 OpenAI 完成 API 的超时时间。", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}}, "tool_mode": false}, "showNode": true, "type": "OpenAIModel"}, "dragging": false, "id": "OpenAIModel-nRbVG", "measured": {"height": 525, "width": 320}, "position": {"x": 929.32546849971, "y": -379.0571813289482}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatOutput-BNHyf", "node": {"base_classes": ["Message"], "beta": false, "category": "outputs", "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatOutput", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.003169567463043492, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "基本清理数据", "dynamic": false, "info": "是否清理数据。", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"聊天输出\"  # \"Chat Output\"\n    description = \"在练习场中显示聊天消息。\"  # \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatOutput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输出传递的消息。\",  # \"Message to be passed as output.\"\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",  # \"data_template\"\n            display_name=\"数据模板\",  # \"Data Template\"\n            value=\"{text}\",\n            advanced=True,\n            info=\"用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。\",  # \"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\"\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",  # \"clean_data\"\n            display_name=\"基本清理数据\",  # \"Basic Clean Data\"\n            value=True,\n            info=\"是否清理数据。\",  # \"Whether to clean the data.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "数据模板", "dynamic": false, "info": "用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输出传递的消息。", "input_types": ["Data", "DataFrame", "Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatOutput"}, "id": "ChatOutput-BNHyf", "measured": {"height": 66, "width": 192}, "position": {"x": 2235, "y": 435}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatInput-dq6uf", "node": {"base_classes": ["Message"], "beta": false, "category": "inputs", "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatInput", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.0020353564437605998, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"聊天输入\"  # \"Chat Input\"\n    description = \"从练习场获取聊天输入，仅支持上传图片解析。\"  # \"Get chat inputs from the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatInput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            value=\"\",\n            info=\"作为输入传递的消息。\",  # \"Message to be passed as input.\"\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",  # \"files\"\n            display_name=\"文件\",  # \"Files\"\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"随消息发送的文件。\",  # \"Files to be sent with the message.\"\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"消息\", name=\"message\", method=\"message_response\"),  # \"Message\"\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"_input_type": "FileInput", "advanced": true, "display_name": "文件", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "xlsx", "xls", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "随消息发送的文件。", "list": true, "list_add_label": "Add More", "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输入传递的消息。", "input_types": [], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "In 2022, the company demonstrated strong financial performance, reporting a gross profit of $1.2 billion, reflecting stable revenue generation and effective cost management. The EBITDA stood at $900 million, highlighting the company’s solid operational efficiency and profitability before interest, taxes, depreciation, and amortization. Despite a slight increase in operating expenses compared to 2021, the company maintained a healthy bottom line, achieving a net income of $500 million. This growth underscores the company’s ability to navigate economic challenges while sustaining profitability, reinforcing its financial stability and competitive position in the market."}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatInput"}, "dragging": false, "id": "ChatInput-dq6uf", "measured": {"height": 66, "width": 192}, "position": {"x": 866.761331501802, "y": 581.619639019103}, "selected": false, "type": "genericNode"}, {"data": {"id": "note-lPaVF", "node": {"description": "### 💡 Add your OpenAI API key here", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "id": "note-lPaVF", "measured": {"height": 324, "width": 324}, "position": {"x": 903.4968193095694, "y": -432.3984534767629}, "selected": false, "type": "noteNode"}, {"data": {"id": "note-OrB5w", "node": {"description": "\n# Financial Report Parser\n\nThis template extracts key financial metrics from a given financial report text using OpenAI's GPT-4o-mini model. The extracted data is structured and formatted for chat consumption.\n\n## Prerequisites\n\n- **[OpenAI API Key](https://platform.openai.com/)**\n\n## Quickstart\n\n1. Add your OpenAI API key to the OpenAI model.\n2. To run the flow, click **Playground**.\nThe **Chat Input** component in this template is pre-loaded with a sample financial report for demonstrating how structured data is extracted.\n\n* The **OpenAI** model component identifies and retrieves Gross Profit, EBITDA, Net Income, and Operating Expenses from the financial report.\n* The **Structured Output** component formats extracted data into a structured format for better readability and further processing.\n* The **Data to Message** component converts extracted data into formatted messages for chat consumption.\n\n\n\n\n\n", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "height": 688, "id": "note-OrB5w", "measured": {"height": 688, "width": 620}, "position": {"x": 270.9912976390468, "y": -396.43811550696176}, "resizing": false, "selected": false, "type": "noteNode", "width": 619}, {"data": {"id": "StructuredOutput-KQHp8", "node": {"base_classes": ["Data", "DataFrame"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Transforms LLM responses into **structured data formats**. Ideal for extracting specific information or creating consistent outputs.", "display_name": "Structured Output", "documentation": "", "edited": false, "field_order": ["llm", "input_value", "system_prompt", "schema_name", "output_schema"], "frozen": false, "icon": "braces", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "结构化输出", "method": "build_structured_output", "name": "structured_output", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据框", "method": "as_dataframe", "name": "structured_output_dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import TYPE_CHECKING, cast\n\nfrom pydantic import BaseModel, Field, create_model\n\nfrom langflow.base.models.chat_result import get_chat_result\nfrom langflow.custom import Component\nfrom langflow.helpers.base_model import build_model_from_schema\nfrom langflow.io import (\n    BoolInput,\n    HandleInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n    TableInput,\n)\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.table import EditMode\n\nif TYPE_CHECKING:\n    from langflow.field_typing.constants import LanguageModel\n\n\nclass StructuredOutputComponent(Component):\n    display_name = \"结构化输出\"  # \"Structured Output\"\n    description = (\n        \"将 LLM 响应转换为 **结构化数据格式**。非常适合提取特定信息或创建一致的输出。\"  # \"Transforms LLM responses into **structured data formats**. Ideal for extracting specific information or creating consistent outputs.\"\n    )\n    name = \"StructuredOutput\"\n    icon = \"braces\"\n\n    inputs = [\n        HandleInput(\n            name=\"llm\",\n            display_name=\"语言模型\",  # \"Language Model\"\n            info=\"用于生成结构化输出的语言模型。\",  # \"The language model to use to generate the structured output.\"\n            input_types=[\"LanguageModel\"],\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"input_value\",\n            display_name=\"输入消息\",  # \"Input Message\"\n            info=\"传递给语言模型的输入消息。\",  # \"The input message to the language model.\"\n            tool_mode=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"system_prompt\",\n            display_name=\"格式说明\",  # \"Format Instructions\"\n            info=\"提供给语言模型的用于格式化输出的说明。\",  # \"The instructions to the language model for formatting the output.\"\n            value=(\n                \"您是一个 AI 系统，旨在从非结构化文本中提取结构化信息。\"\n                \"根据输入文本，返回一个具有预定义键的 JSON 对象，基于预期的结构。\"\n                \"准确提取值并根据指定的类型（例如，字符串、整数、浮点数、日期）格式化它们。\"\n                \"如果缺少值或无法确定值，请返回默认值（例如，null、0 或 'N/A'）。\"\n                \"如果输入文本中存在多个预期结构的实例，请将每个实例作为单独的 JSON 对象流式返回。\"  # Original English instructions\n            ),\n            required=True,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"schema_name\",\n            display_name=\"模式名称\",  # \"Schema Name\"\n            info=\"为输出数据模式提供一个名称。\",  # \"Provide a name for the output data schema.\"\n            advanced=True,\n        ),\n        TableInput(\n            name=\"output_schema\",\n            display_name=\"输出模式\",  # \"Output Schema\"\n            info=\"定义模型输出的结构和数据类型。\",  # \"Define the structure and data types for the model's output.\"\n            required=True,\n            table_schema=[\n                {\n                    \"name\": \"name\",\n                    \"display_name\": \"名称\",  # \"Name\"\n                    \"type\": \"str\",\n                    \"description\": \"指定输出字段的名称。\",  # \"Specify the name of the output field.\"\n                    \"default\": \"field\",\n                    \"edit_mode\": EditMode.INLINE,\n                },\n                {\n                    \"name\": \"description\",\n                    \"display_name\": \"描述\",  # \"Description\"\n                    \"type\": \"str\",\n                    \"description\": \"描述输出字段的用途。\",  # \"Describe the purpose of the output field.\"\n                    \"default\": \"字段的描述\",  # \"description of field\"\n                    \"edit_mode\": EditMode.POPOVER,\n                },\n                {\n                    \"name\": \"type\",\n                    \"display_name\": \"类型\",  # \"Type\"\n                    \"type\": \"str\",\n                    \"edit_mode\": EditMode.INLINE,\n                    \"description\": (\n                        \"指示输出字段的数据类型（例如，str、int、float、bool、list、dict）。\"  # \"Indicate the data type of the output field (e.g., str, int, float, bool, list, dict).\"\n                    ),\n                    \"options\": [\"str\", \"int\", \"float\", \"bool\", \"list\", \"dict\"],\n                    \"default\": \"str\",\n                },\n                {\n                    \"name\": \"multiple\",\n                    \"display_name\": \"多个值\",  # \"Multiple\"\n                    \"type\": \"boolean\",\n                    \"description\": \"如果此输出字段应为指定类型的列表，则设置为 True。\",  # \"Set to True if this output field should be a list of the specified type.\"\n                    \"default\": \"False\",\n                    \"edit_mode\": EditMode.INLINE,\n                },\n            ],\n            value=[\n                {\n                    \"name\": \"field\",\n                    \"description\": \"字段的描述\",  # \"description of field\"\n                    \"type\": \"str\",\n                    \"multiple\": \"False\",\n                }\n            ],\n        ),\n        BoolInput(\n            name=\"multiple\",\n            advanced=True,\n            display_name=\"生成多个\",  # \"Generate Multiple\"\n            info=\"[已弃用] 始终设置为 True\",  # \"[Deprecated] Always set to True\"\n            value=True,\n        ),\n    ]\n\n    outputs = [\n        Output(\n            name=\"structured_output\",\n            display_name=\"结构化输出\",  # \"Structured Output\"\n            method=\"build_structured_output\",\n        ),\n        Output(\n            name=\"structured_output_dataframe\",\n            display_name=\"数据框\",  # \"DataFrame\"\n            method=\"as_dataframe\",\n        ),\n    ]\n\n    def build_structured_output_base(self) -> Data:\n        schema_name = self.schema_name or \"OutputModel\"\n\n        if not hasattr(self.llm, \"with_structured_output\"):\n            msg = \"语言模型不支持结构化输出。\"  # \"Language model does not support structured output.\"\n            raise TypeError(msg)\n        if not self.output_schema:\n            msg = \"输出模式不能为空。\"  # \"Output schema cannot be empty.\"\n            raise ValueError(msg)\n\n        output_model_ = build_model_from_schema(self.output_schema)\n\n        output_model = create_model(\n            schema_name,\n            objects=(list[output_model_], Field(description=f\"一个 {schema_name} 的列表。\")),  # \"A list of {schema_name}.\"\n        )\n\n        try:\n            llm_with_structured_output = cast(\"LanguageModel\", self.llm).with_structured_output(schema=output_model)\n\n        except NotImplementedError as exc:\n            msg = f\"{self.llm.__class__.__name__} 不支持结构化输出。\"  # \"{self.llm.__class__.__name__} does not support structured output.\"\n            raise TypeError(msg) from exc\n        config_dict = {\n            \"run_name\": self.display_name,\n            \"project_name\": self.get_project_name(),\n            \"callbacks\": self.get_langchain_callbacks(),\n        }\n        result = get_chat_result(\n            runnable=llm_with_structured_output,\n            system_message=self.system_prompt,\n            input_value=self.input_value,\n            config=config_dict,\n        )\n        if isinstance(result, BaseModel):\n            result = result.model_dump()\n        if \"objects\" in result:\n            return result[\"objects\"]\n        return result\n\n    def build_structured_output(self) -> Data:\n        output = self.build_structured_output_base()\n\n        return Data(results=output)\n\n    def as_dataframe(self) -> DataFrame:\n        output = self.build_structured_output_base()\n        if isinstance(output, list):\n            return DataFrame(data=output)\n        return DataFrame(data=[output])\n"}, "input_value": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "输入消息", "dynamic": false, "info": "传递给语言模型的输入消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "llm": {"_input_type": "HandleInput", "advanced": false, "display_name": "语言模型", "dynamic": false, "info": "用于生成结构化输出的语言模型。", "input_types": ["LanguageModel"], "list": false, "list_add_label": "Add More", "name": "llm", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "multiple": {"_input_type": "BoolInput", "advanced": true, "display_name": "生成多个", "dynamic": false, "info": "[已弃用] 始终设置为 True", "list": false, "list_add_label": "Add More", "name": "multiple", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "output_schema": {"_input_type": "TableInput", "advanced": false, "display_name": "输出模式", "dynamic": false, "info": "定义模型输出的结构和数据类型。", "is_list": true, "list_add_label": "Add More", "load_from_db": false, "name": "output_schema", "placeholder": "", "required": true, "show": true, "table_icon": "Table", "table_schema": {"columns": [{"default": "field", "description": "Specify the name of the output field.", "disable_edit": false, "display_name": "Name", "edit_mode": "inline", "filterable": true, "formatter": "text", "hidden": false, "name": "name", "sortable": true, "type": "text"}, {"default": "description of field", "description": "Describe the purpose of the output field.", "disable_edit": false, "display_name": "Description", "edit_mode": "popover", "filterable": true, "formatter": "text", "hidden": false, "name": "description", "sortable": true, "type": "text"}, {"default": "text", "description": "Indicate the data type of the output field (e.g., str, int, float, bool, list, dict).", "disable_edit": false, "display_name": "Type", "edit_mode": "inline", "filterable": true, "formatter": "text", "hidden": false, "name": "type", "sortable": true, "type": "text"}, {"default": "False", "description": "Set to True if this output field should be a list of the specified type.", "disable_edit": false, "display_name": "Multiple", "edit_mode": "inline", "filterable": true, "formatter": "text", "hidden": false, "name": "multiple", "sortable": true, "type": "boolean"}]}, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "trigger_icon": "Table", "trigger_text": "Open table", "type": "table", "value": [{"description": "description of field", "multiple": "False", "name": "EBITIDA", "type": "text"}, {"description": "description of field", "multiple": "False", "name": "NET_INCOME", "type": "text"}, {"description": "description of field", "multiple": "False", "name": "GROSS_PROFIT", "type": "text"}]}, "schema_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "模式名称", "dynamic": false, "info": "为输出数据模式提供一个名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "schema_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "system_prompt": {"_input_type": "MultilineInput", "advanced": true, "display_name": "格式说明", "dynamic": false, "info": "提供给语言模型的用于格式化输出的说明。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_prompt", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "You are an AI system designed to extract structured information from unstructured text.Given the input_text, return a JSON object with predefined keys based on the expected structure.Extract values accurately and format them according to the specified type (e.g., string, integer, float, date).If a value is missing or cannot be determined, return a default (e.g., null, 0, or 'N/A').If multiple instances of the expected structure exist within the input_text, stream each as a separate JSON object."}}, "tool_mode": false}, "showNode": true, "type": "StructuredOutput"}, "dragging": false, "id": "StructuredOutput-KQHp8", "measured": {"height": 447, "width": 320}, "position": {"x": 1378.7263469848428, "y": 167.37722508100572}, "selected": false, "type": "genericNode"}, {"data": {"id": "ParseDataFrame-QOEC3", "node": {"base_classes": ["Message"], "beta": false, "category": "processing", "conditional_paths": [], "custom_fields": {}, "description": "Convert a DataFrame into plain text following a specified template. Each column in the DataFrame is treated as a possible template key, e.g. {col_name}.", "display_name": "Parse DataFrame", "documentation": "", "edited": false, "field_order": ["df", "template", "sep"], "frozen": false, "icon": "braces", "key": "ParseDataFrame", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "文本", "method": "parse_data", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.007568328950209746, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.io import DataFrameInput, MultilineInput, Output, StrInput\nfrom langflow.schema.message import Message\n\n\nclass ParseDataFrameComponent(Component):\n    display_name = \"解析数据表\"  # \"Parse DataFrame\"\n    description = (\n\"将数据表转换为遵循指定模板的纯文本。\"  # \"Convert a DataFrame into plain text following a specified template. \"\n\"数据表中的每一列都被视为可能的模板键，例如 {列名}。\"  # \"Each column in the DataFrame is treated as a possible template key, e.g. {col_name}.\"\n    )\n    icon = \"braces\"\n    name = \"ParseDataFrame\"\n\n    inputs = [\n        DataFrameInput(\nname=\"df\",\ndisplay_name=\"数据表\",  # \"DataFrame\"\ninfo=\"要转换为文本行的数据表。\",  # \"The DataFrame to convert to text rows.\"\n),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"模板\",  # \"Template\"\n            info=(\n\"用于格式化每一行的模板。\"  # \"The template for formatting each row. \"\n\"使用与数据表列名匹配的占位符，例如 '{col1}'、'{col2}'。\"  # \"Use placeholders matching column names in the DataFrame, for example '{col1}', '{col2}'.\"\n            ),\n            value=\"{文本}\",\n        ),\n        StrInput(\n            name=\"sep\",\n            display_name=\"分隔符\",  # \"Separator\"\n            advanced=True,\n            value=\"\\n\",\n            info=\"在构建单个文本输出时，用于连接所有行文本的字符串。\",  # \"String that joins all row texts when building the single Text output.\"\n        ),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"文本\",  # \"Text\"\n            name=\"text\",\n            info=\"所有行合并为单个文本，每行由模板格式化并由 `sep` 分隔。\",  # \"All rows combined into a single text, each row formatted by the template and separated by `sep`.\"\n            method=\"parse_data\",\n        ),\n    ]\n\n    def _clean_args(self):\n        dataframe = self.df\n        template = self.template or \"{text}\"\n        sep = self.sep or \"\\n\"\n        return dataframe, template, sep\n\n    def parse_data(self) -> Message:\n        \"\"\"Converts each row of the DataFrame into a formatted string using the template.\n\n        then joins them with `sep`. Returns a single combined string as a Message.\n        \"\"\"\n        dataframe, template, sep = self._clean_args()\n\n        lines = []\n        # For each row in the DataFrame, build a dict and format\n        for _, row in dataframe.iterrows():\n            row_dict = row.to_dict()\n            text_line = template.format(**row_dict)  # e.g. template=\"{text}\", row_dict={\"text\": \"Hello\"}\n            lines.append(text_line)\n\n        # Join all lines with the provided separator\n        result_string = sep.join(lines)\n        self.status = result_string  # store in self.status for UI logs\n        return Message(text=result_string)\n"}, "df": {"_input_type": "DataFrameInput", "advanced": false, "display_name": "数据表", "dynamic": false, "info": "要转换为文本行的数据表。", "input_types": ["DataFrame"], "list": false, "list_add_label": "Add More", "name": "df", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "sep": {"_input_type": "StrInput", "advanced": true, "display_name": "分隔符", "dynamic": false, "info": "在构建单个文本输出时，用于连接所有行文本的字符串。", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n"}, "template": {"_input_type": "MultilineInput", "advanced": false, "display_name": "模板", "dynamic": false, "info": "用于格式化每一行的模板。使用与数据表列名匹配的占位符，例如 '{col1}'、'{col2}'。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "EBITIDA: {EBITIDA}  ,  Net Income: {NET_INCOME} , GROSS_PROFIT: {GROSS_PROFIT}"}}, "tool_mode": false}, "showNode": true, "type": "ParseDataFrame"}, "dragging": false, "id": "ParseDataFrame-QOEC3", "measured": {"height": 334, "width": 320}, "position": {"x": 1783.5562660355338, "y": 269.9258338477598}, "selected": false, "type": "genericNode"}], "viewport": {"x": -99.6371290033394, "y": 399.2977275294001, "zoom": 0.6105558084817295}}, "description": "使用结构化输出组件，从财务报告中提取诸如毛利润、息税折旧摊销前利润（EBITDA）和净利润等关键财务指标，并对这些指标进行整理，以便于分析。", "endpoint_name": null, "id": "a217ddf6-8620-4f38-ac09-5b35f612ce9e", "is_component": false, "last_tested_version": "1.2.0", "name": "财务报告解析器", "tags": ["chatbots", "content-generation"]}