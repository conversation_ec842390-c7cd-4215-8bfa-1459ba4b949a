{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-auIvg", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "Agent-g<PERSON>rf", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-auIvg{œdataTypeœ:œChatInputœ,œidœ:œChatInput-auIvgœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-Agent-gCLrf{œfieldNameœ:œinput_valueœ,œidœ:œAgent-gCLrfœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-auIvg", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-auIvgœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "Agent-g<PERSON>rf", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œAgent-gCLrfœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Agent", "id": "Agent-g<PERSON>rf", "name": "response", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-8WiQm", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-Agent-g<PERSON><PERSON>{œdataTypeœ:œAgentœ,œidœ:œAgent-gCLrfœ,œnameœ:œresponseœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-8WiQm{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-8WiQmœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Agent-g<PERSON>rf", "sourceHandle": "{œdataTypeœ: œAgentœ, œidœ: œAgent-gCLrfœ, œnameœ: œresponseœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-8WiQm", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-8WiQmœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ComposioAPI", "id": "ComposioAPI-Tdreq", "name": "tools", "output_types": ["Tool"]}, "targetHandle": {"fieldName": "tools", "id": "Agent-g<PERSON>rf", "inputTypes": ["Tool"], "type": "other"}}, "id": "reactflow__edge-ComposioAPI-Tdreq{œdataTypeœ:œComposioAPIœ,œidœ:œComposioAPI-Tdreqœ,œnameœ:œtoolsœ,œoutput_typesœ:[œToolœ]}-Agent-gCLrf{œfieldNameœ:œtoolsœ,œidœ:œAgent-gCLrfœ,œinputTypesœ:[œToolœ],œtypeœ:œotherœ}", "selected": false, "source": "ComposioAPI-Tdreq", "sourceHandle": "{œdataTypeœ: œComposioAPIœ, œidœ: œComposioAPI-Tdreqœ, œnameœ: œtoolsœ, œoutput_typesœ: [œToolœ]}", "target": "Agent-g<PERSON>rf", "targetHandle": "{œfieldNameœ: œtoolsœ, œidœ: œAgent-gCLrfœ, œinputTypesœ: [œToolœ], œtypeœ: œotherœ}"}], "nodes": [{"data": {"id": "Agent-g<PERSON>rf", "node": {"base_classes": ["Message"], "beta": false, "category": "agents", "conditional_paths": [], "custom_fields": {}, "description": "Define the agent's instructions, then enter a task to complete using tools.", "display_name": "Agent", "documentation": "", "edited": false, "field_order": ["agent_llm", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed", "max_retries", "timeout", "system_prompt", "tools", "input_value", "handle_parsing_errors", "verbose", "max_iterations", "agent_description", "memory", "sender", "sender_name", "n_messages", "session_id", "order", "template", "add_current_date_tool"], "frozen": false, "icon": "bot", "key": "Agent", "legacy": false, "lf_version": "1.2.0", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Response", "method": "message_response", "name": "response", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 1.1732828199964098e-19, "template": {"_type": "Component", "add_current_date_tool": {"_input_type": "BoolInput", "advanced": true, "display_name": "Current Date", "dynamic": false, "info": "If true, will add a tool to the agent that returns the current date.", "list": false, "list_add_label": "Add More", "name": "add_current_date_tool", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "agent_description": {"_input_type": "MultilineInput", "advanced": true, "display_name": "Agent Description [Deprecated]", "dynamic": false, "info": "The description of the agent. This is only used when in Tool Mode. Defaults to 'A helpful assistant with access to the following tools:' and tools are added dynamically. This feature is deprecated and will be removed in future versions.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "agent_description", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "A helpful assistant with access to the following tools:"}, "agent_llm": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Model Provider", "dynamic": false, "info": "The provider of the language model that the agent will use to generate responses.", "input_types": [], "name": "agent_llm", "options": ["Amazon Bedrock", "Anthropic", "Azure OpenAI", "Google Generative AI", "Groq", "NVIDIA", "OpenAI", "<PERSON><PERSON><PERSON><PERSON>", "Custom"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "OpenAI"}, "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API Key", "dynamic": false, "info": "The OpenAI API Key to use for the OpenAI model.", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_core.tools import StructuredTool\n\nfrom langflow.base.agents.agent import LCToolsAgentComponent\nfrom langflow.base.agents.events import ExceptionWithMessageError\nfrom langflow.base.models.model_input_constants import (\n    ALL_PROVIDER_FIELDS,\n    MODEL_DYNAMIC_UPDATE_FIELDS,\n    MODEL_PROVIDERS_DICT,\n    MODELS_METADATA,\n)\nfrom langflow.base.models.model_utils import get_model_name\nfrom langflow.components.helpers import CurrentDateComponent\nfrom langflow.components.helpers.memory import MemoryComponent\nfrom langflow.components.langchain_utilities.tool_calling import ToolCallingAgentComponent\nfrom langflow.custom.custom_component.component import _get_component_toolkit\nfrom langflow.custom.utils import update_component_build_config\nfrom langflow.field_typing import Tool\nfrom langflow.io import BoolInput, DropdownInput, MultilineInput, Output\nfrom langflow.logging import logger\nfrom langflow.schema.dotdict import dotdict\nfrom langflow.schema.message import Message\n\n\ndef set_advanced_true(component_input):\n    component_input.advanced = True\n    return component_input\n\n\nclass AgentComponent(ToolCallingAgentComponent):\n    display_name: str = \"Agent\"\n    description: str = \"Define the agent's instructions, then enter a task to complete using tools.\"\n    icon = \"bot\"\n    beta = False\n    name = \"Agent\"\n\n    memory_inputs = [set_advanced_true(component_input) for component_input in MemoryComponent().inputs]\n\n    inputs = [\n        DropdownInput(\n            name=\"agent_llm\",\n            display_name=\"Model Provider\",\n            info=\"The provider of the language model that the agent will use to generate responses.\",\n            options=[*sorted(MODEL_PROVIDERS_DICT.keys()), \"Custom\"],\n            value=\"OpenAI\",\n            real_time_refresh=True,\n            input_types=[],\n            options_metadata=[MODELS_METADATA[key] for key in sorted(MODELS_METADATA.keys())] + [{\"icon\": \"brain\"}],\n        ),\n        *MODEL_PROVIDERS_DICT[\"OpenAI\"][\"inputs\"],\n        MultilineInput(\n            name=\"system_prompt\",\n            display_name=\"Agent Instructions\",\n            info=\"System Prompt: Initial instructions and context provided to guide the agent's behavior.\",\n            value=\"You are a helpful assistant that can use tools to answer questions and perform tasks.\",\n            advanced=False,\n        ),\n        *LCToolsAgentComponent._base_inputs,\n        *memory_inputs,\n        BoolInput(\n            name=\"add_current_date_tool\",\n            display_name=\"Current Date\",\n            advanced=True,\n            info=\"If true, will add a tool to the agent that returns the current date.\",\n            value=True,\n        ),\n    ]\n    outputs = [Output(name=\"response\", display_name=\"Response\", method=\"message_response\")]\n\n    async def message_response(self) -> Message:\n        try:\n            # Get LLM model and validate\n            llm_model, display_name = self.get_llm()\n            if llm_model is None:\n                msg = \"No language model selected. Please choose a model to proceed.\"\n                raise ValueError(msg)\n            self.model_name = get_model_name(llm_model, display_name=display_name)\n\n            # Get memory data\n            self.chat_history = await self.get_memory_data()\n\n            # Add current date tool if enabled\n            if self.add_current_date_tool:\n                if not isinstance(self.tools, list):  # type: ignore[has-type]\n                    self.tools = []\n                current_date_tool = (await CurrentDateComponent(**self.get_base_args()).to_toolkit()).pop(0)\n                if not isinstance(current_date_tool, StructuredTool):\n                    msg = \"CurrentDateComponent must be converted to a StructuredTool\"\n                    raise TypeError(msg)\n                self.tools.append(current_date_tool)\n\n            # Validate tools\n            if not self.tools:\n                msg = \"Tools are required to run the agent. Please add at least one tool.\"\n                raise ValueError(msg)\n\n            # Set up and run agent\n            self.set(\n                llm=llm_model,\n                tools=self.tools,\n                chat_history=self.chat_history,\n                input_value=self.input_value,\n                system_prompt=self.system_prompt,\n            )\n            agent = self.create_agent_runnable()\n            return await self.run_agent(agent)\n\n        except (ValueError, TypeError, KeyError) as e:\n            logger.error(f\"{type(e).__name__}: {e!s}\")\n            raise\n        except ExceptionWithMessageError as e:\n            logger.error(f\"ExceptionWithMessageError occurred: {e}\")\n            raise\n        except Exception as e:\n            logger.error(f\"Unexpected error: {e!s}\")\n            raise\n\n    async def get_memory_data(self):\n        memory_kwargs = {\n            component_input.name: getattr(self, f\"{component_input.name}\") for component_input in self.memory_inputs\n        }\n        # filter out empty values\n        memory_kwargs = {k: v for k, v in memory_kwargs.items() if v}\n\n        return await MemoryComponent(**self.get_base_args()).set(**memory_kwargs).retrieve_messages()\n\n    def get_llm(self):\n        if not isinstance(self.agent_llm, str):\n            return self.agent_llm, None\n\n        try:\n            provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n            if not provider_info:\n                msg = f\"Invalid model provider: {self.agent_llm}\"\n                raise ValueError(msg)\n\n            component_class = provider_info.get(\"component_class\")\n            display_name = component_class.display_name\n            inputs = provider_info.get(\"inputs\")\n            prefix = provider_info.get(\"prefix\", \"\")\n\n            return self._build_llm_model(component_class, inputs, prefix), display_name\n\n        except Exception as e:\n            logger.error(f\"Error building {self.agent_llm} language model: {e!s}\")\n            msg = f\"Failed to initialize language model: {e!s}\"\n            raise ValueError(msg) from e\n\n    def _build_llm_model(self, component, inputs, prefix=\"\"):\n        model_kwargs = {input_.name: getattr(self, f\"{prefix}{input_.name}\") for input_ in inputs}\n        return component.set(**model_kwargs).build_model()\n\n    def set_component_params(self, component):\n        provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n        if provider_info:\n            inputs = provider_info.get(\"inputs\")\n            prefix = provider_info.get(\"prefix\")\n            model_kwargs = {input_.name: getattr(self, f\"{prefix}{input_.name}\") for input_ in inputs}\n\n            return component.set(**model_kwargs)\n        return component\n\n    def delete_fields(self, build_config: dotdict, fields: dict | list[str]) -> None:\n        \"\"\"Delete specified fields from build_config.\"\"\"\n        for field in fields:\n            build_config.pop(field, None)\n\n    def update_input_types(self, build_config: dotdict) -> dotdict:\n        \"\"\"Update input types for all fields in build_config.\"\"\"\n        for key, value in build_config.items():\n            if isinstance(value, dict):\n                if value.get(\"input_types\") is None:\n                    build_config[key][\"input_types\"] = []\n            elif hasattr(value, \"input_types\") and value.input_types is None:\n                value.input_types = []\n        return build_config\n\n    async def update_build_config(\n        self, build_config: dotdict, field_value: str, field_name: str | None = None\n    ) -> dotdict:\n        # Iterate over all providers in the MODEL_PROVIDERS_DICT\n        # Existing logic for updating build_config\n        if field_name in (\"agent_llm\",):\n            build_config[\"agent_llm\"][\"value\"] = field_value\n            provider_info = MODEL_PROVIDERS_DICT.get(field_value)\n            if provider_info:\n                component_class = provider_info.get(\"component_class\")\n                if component_class and hasattr(component_class, \"update_build_config\"):\n                    # Call the component class's update_build_config method\n                    build_config = await update_component_build_config(\n                        component_class, build_config, field_value, \"model_name\"\n                    )\n\n            provider_configs: dict[str, tuple[dict, list[dict]]] = {\n                provider: (\n                    MODEL_PROVIDERS_DICT[provider][\"fields\"],\n                    [\n                        MODEL_PROVIDERS_DICT[other_provider][\"fields\"]\n                        for other_provider in MODEL_PROVIDERS_DICT\n                        if other_provider != provider\n                    ],\n                )\n                for provider in MODEL_PROVIDERS_DICT\n            }\n            if field_value in provider_configs:\n                fields_to_add, fields_to_delete = provider_configs[field_value]\n\n                # Delete fields from other providers\n                for fields in fields_to_delete:\n                    self.delete_fields(build_config, fields)\n\n                # Add provider-specific fields\n                if field_value == \"OpenAI\" and not any(field in build_config for field in fields_to_add):\n                    build_config.update(fields_to_add)\n                else:\n                    build_config.update(fields_to_add)\n                # Reset input types for agent_llm\n                build_config[\"agent_llm\"][\"input_types\"] = []\n            elif field_value == \"Custom\":\n                # Delete all provider fields\n                self.delete_fields(build_config, ALL_PROVIDER_FIELDS)\n                # Update with custom component\n                custom_component = DropdownInput(\n                    name=\"agent_llm\",\n                    display_name=\"Language Model\",\n                    options=[*sorted(MODEL_PROVIDERS_DICT.keys()), \"Custom\"],\n                    value=\"Custom\",\n                    real_time_refresh=True,\n                    input_types=[\"LanguageModel\"],\n                    options_metadata=[MODELS_METADATA[key] for key in sorted(MODELS_METADATA.keys())]\n                    + [{\"icon\": \"brain\"}],\n                )\n                build_config.update({\"agent_llm\": custom_component.to_dict()})\n            # Update input types for all fields\n            build_config = self.update_input_types(build_config)\n\n            # Validate required keys\n            default_keys = [\n                \"code\",\n                \"_type\",\n                \"agent_llm\",\n                \"tools\",\n                \"input_value\",\n                \"add_current_date_tool\",\n                \"system_prompt\",\n                \"agent_description\",\n                \"max_iterations\",\n                \"handle_parsing_errors\",\n                \"verbose\",\n            ]\n            missing_keys = [key for key in default_keys if key not in build_config]\n            if missing_keys:\n                msg = f\"Missing required keys in build_config: {missing_keys}\"\n                raise ValueError(msg)\n        if (\n            isinstance(self.agent_llm, str)\n            and self.agent_llm in MODEL_PROVIDERS_DICT\n            and field_name in MODEL_DYNAMIC_UPDATE_FIELDS\n        ):\n            provider_info = MODEL_PROVIDERS_DICT.get(self.agent_llm)\n            if provider_info:\n                component_class = provider_info.get(\"component_class\")\n                component_class = self.set_component_params(component_class)\n                prefix = provider_info.get(\"prefix\")\n                if component_class and hasattr(component_class, \"update_build_config\"):\n                    # Call each component class's update_build_config method\n                    # remove the prefix from the field_name\n                    if isinstance(field_name, str) and isinstance(prefix, str):\n                        field_name = field_name.replace(prefix, \"\")\n                    build_config = await update_component_build_config(\n                        component_class, build_config, field_value, \"model_name\"\n                    )\n        return dotdict({k: v.to_dict() if hasattr(v, \"to_dict\") else v for k, v in build_config.items()})\n\n    async def to_toolkit(self) -> list[Tool]:\n        component_toolkit = _get_component_toolkit()\n        tools_names = self._build_tools_names()\n        agent_description = self.get_tool_description()\n        # TODO: Agent Description Depreciated Feature to be removed\n        description = f\"{agent_description}{tools_names}\"\n        tools = component_toolkit(component=self).get_tools(\n            tool_name=self.get_tool_name(), tool_description=description, callbacks=self.get_langchain_callbacks()\n        )\n        if hasattr(self, \"tools_metadata\"):\n            tools = component_toolkit(component=self, metadata=self.tools_metadata).update_tools_metadata(tools=tools)\n        return tools\n"}, "handle_parsing_errors": {"_input_type": "BoolInput", "advanced": true, "display_name": "<PERSON><PERSON> Parse Errors", "dynamic": false, "info": "Should the Agent fix errors when reading user input for better processing?", "list": false, "list_add_label": "Add More", "name": "handle_parsing_errors", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "input_value": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Input", "dynamic": false, "info": "The input provided by the user for the agent to process.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON Mode", "dynamic": false, "info": "If True, it will output JSON regardless of passing a schema.", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_iterations": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Iterations", "dynamic": false, "info": "The maximum number of attempts the agent can make to complete its task before it stops.", "list": false, "list_add_label": "Add More", "name": "max_iterations", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 15}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "Max Retries", "dynamic": false, "info": "The maximum number of retries to make when generating.", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON>", "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "memory": {"_input_type": "HandleInput", "advanced": true, "display_name": "External Memory", "dynamic": false, "info": "Retrieve messages from an external memory. If empty, it will use the Langflow tables.", "input_types": ["Memory"], "list": false, "list_add_label": "Add More", "name": "memory", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "Model Kwargs", "dynamic": false, "info": "Additional keyword arguments to pass to the model.", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "To see the model names, first choose a provider. Then, enter your API key and click the refresh button next to the model name.", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "real_time_refresh": false, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "n_messages": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Messages", "dynamic": false, "info": "Number of messages to retrieve.", "list": false, "list_add_label": "Add More", "name": "n_messages", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 100}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API Base", "dynamic": false, "info": "The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "order": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Order", "dynamic": false, "info": "Order of the messages.", "name": "order", "options": ["Ascending", "Descending"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_metadata": true, "type": "str", "value": "Ascending"}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "Seed", "dynamic": false, "info": "The seed controls the reproducibility of the job.", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Sender Type", "dynamic": false, "info": "Filter by sender type.", "name": "sender", "options": ["Machine", "User", "Machine and User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine and User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Sender Name", "dynamic": false, "info": "Filter by sender name.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Session ID", "dynamic": false, "info": "The session ID of the chat. If empty, the current session ID parameter will be used.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "system_prompt": {"_input_type": "MultilineInput", "advanced": false, "display_name": "Agent Instructions", "dynamic": false, "info": "System Prompt: Initial instructions and context provided to guide the agent's behavior.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_prompt", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "You are a helpful assistant that can use tools to answer questions and perform tasks."}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "Temperature", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "template": {"_input_type": "MultilineInput", "advanced": true, "display_name": "Template", "dynamic": false, "info": "The template to use for formatting the data. It can contain the keys {text}, {sender} or any other key in the message data.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{sender_name}: {text}"}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "Timeout", "dynamic": false, "info": "The timeout for requests to OpenAI completion API.", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}, "tools": {"_input_type": "HandleInput", "advanced": false, "display_name": "Tools", "dynamic": false, "info": "These are the tools that the agent can use to help with tasks.", "input_types": ["Tool"], "list": true, "list_add_label": "Add More", "name": "tools", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "verbose": {"_input_type": "BoolInput", "advanced": true, "display_name": "Verbose", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "name": "verbose", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}}, "tool_mode": false}, "showNode": true, "type": "Agent"}, "dragging": false, "id": "Agent-g<PERSON>rf", "measured": {"height": 624, "width": 320}, "position": {"x": 246.2965482704007, "y": 49.54798016575572}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatInput-auIvg", "node": {"base_classes": ["Message"], "beta": false, "category": "inputs", "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatInput", "legacy": false, "lf_version": "1.2.0", "metadata": {}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.0020353564437605998, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"聊天输入\"  # \"Chat Input\"\n    description = \"从练习场获取聊天输入，仅支持上传图片解析。\"  # \"Get chat inputs from the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatInput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            value=\"\",\n            info=\"作为输入传递的消息。\",  # \"Message to be passed as input.\"\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",  # \"files\"\n            display_name=\"文件\",  # \"Files\"\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"随消息发送的文件。\",  # \"Files to be sent with the message.\"\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"消息\", name=\"message\", method=\"message_response\"),  # \"Message\"\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"_input_type": "FileInput", "advanced": true, "display_name": "文件", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "xlsx", "xls", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "随消息发送的文件。", "list": true, "list_add_label": "Add More", "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输入传递的消息。", "input_types": [], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatInput"}, "dragging": false, "id": "ChatInput-auIvg", "measured": {"height": 66, "width": 192}, "position": {"x": -74.59464648081578, "y": 605.4102099043162}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatOutput-8WiQm", "node": {"base_classes": ["Message"], "beta": false, "category": "outputs", "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatOutput", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.003169567463043492, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "基本清理数据", "dynamic": false, "info": "是否清理数据。", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"聊天输出\"  # \"Chat Output\"\n    description = \"在练习场中显示聊天消息。\"  # \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatOutput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输出传递的消息。\",  # \"Message to be passed as output.\"\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",  # \"data_template\"\n            display_name=\"数据模板\",  # \"Data Template\"\n            value=\"{text}\",\n            advanced=True,\n            info=\"用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。\",  # \"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\"\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",  # \"clean_data\"\n            display_name=\"基本清理数据\",  # \"Basic Clean Data\"\n            value=True,\n            info=\"是否清理数据。\",  # \"Whether to clean the data.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "数据模板", "dynamic": false, "info": "用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输出传递的消息。", "input_types": ["Data", "DataFrame", "Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatOutput"}, "dragging": false, "id": "ChatOutput-8WiQm", "measured": {"height": 66, "width": 192}, "position": {"x": 641.*************, "y": 617.*************}, "selected": false, "type": "genericNode"}, {"data": {"id": "note-y0nez", "node": {"description": "# Gmail Agent\nUsing this flow you can send emails, create drafts, fetch emails and more\n\n## Instructions\n\n1. Get Composio API Key\n   - Visit https://app.composio.dev\n   - Enter the key in the \"Composio API Key\" field\n\n2. Authenticate Gmail Account\n   - Select Gmail App from the dropdown menu in the App Names field\n   - Click the refresh button next to the App Name\n   - Follow the Gmail authentication link\n   - After authenticating, click refresh again\n   - Verify that authentication status shows as successful\n\n3. Select Actions\n   - Default actions (pre-selected):\n     - GMAIL_SEND_EMAIL: Send emails directly\n     - GMAIL_CREATE_EMAIL_DRAFT: Create draft emails\n   - Select additional actions based on your needs\n\n4. Configure OpenAI\n   - Enter your OpenAI API key in the Agent OpenAI API key field\n\n5. Run Agent\n   Example prompts:\n   - \"Send an <NAME_EMAIL> wishing them Happy birthday!\"\n   - \"Create a draft email about project updates\"", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "height": 842, "id": "note-y0nez", "measured": {"height": 842, "width": 395}, "position": {"x": -699.*************, "y": -87.**************}, "resizing": false, "selected": false, "type": "noteNode", "width": 394}, {"data": {"description": "Use Composio toolset to run actions with your agent", "display_name": "Composio Tools", "id": "ComposioAPI-Tdreq", "node": {"base_classes": ["Tool"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Use Composio toolset to run actions with your agent", "display_name": "Composio Tools", "documentation": "https://docs.composio.dev", "edited": false, "field_order": ["entity_id", "api_key", "app_names", "app_credentials", "username", "auth_link", "auth_status", "action_names"], "frozen": false, "icon": "Composio", "legacy": false, "lf_version": "1.2.0", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Tools", "hidden": null, "method": "build_tool", "name": "tools", "options": null, "required_inputs": null, "selected": "Tool", "tool_mode": true, "types": ["Tool"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "action_names": {"_input_type": "MultiselectInput", "advanced": false, "combobox": false, "display_name": "Actions to use", "dynamic": true, "info": "The actions to pass to agent to execute", "list": true, "list_add_label": "Add More", "load_from_db": false, "name": "action_names", "options": ["GMAIL_GET_PEOPLE", "GMAIL_FETCH_EMAILS", "GMAIL_FETCH_MESSAGE_BY_THREAD_ID", "GMAIL_SEARCH_PEOPLE", "GMAIL_SEND_EMAIL", "GMAIL_CREATE_EMAIL_DRAFT", "GMAIL_FETCH_MESSAGE_BY_MESSAGE_ID", "GMAIL_CREATE_LABEL", "GMAIL_GET_ATTACHMENT", "GMAIL_FIND_EMAIL_ID", "GMAIL_REMOVE_LABEL", "GMAIL_GET_PROFILE", "GMAIL_ADD_LABEL_TO_EMAIL", "GMAIL_GET_CONTACTS", "GMAIL_REPLY_TO_THREAD", "GMAIL_LIST_LABELS", "GMAIL_FETCH_LAST_THREE_MESSAGES", "GMAIL_LIST_THREADS", "GMAIL_FETCH_EMAILS_WITH_LABEL", "GMAIL_MODIFY_THREAD_LABELS"], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ["GMAIL_GET_PEOPLE"]}, "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Composio API Key", "dynamic": false, "info": "Refer to https://docs.composio.dev/faq/api_key/api_key", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "COMPOSIO_API_KEY"}, "app_credentials": {"_input_type": "SecretStrInput", "advanced": true, "display_name": "App Credentials", "dynamic": true, "info": "Credentials for app authentication (API Key, Password, etc)", "input_types": ["Message"], "load_from_db": false, "name": "app_credentials", "password": true, "placeholder": "", "required": false, "show": false, "title_case": false, "type": "str", "value": ""}, "app_names": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "App Name", "dynamic": false, "info": "The app name to use. Please refresh after selecting app name", "load_from_db": false, "name": "app_names", "options": ["ACCELO", "AIRTABLE", "AMAZON", "APALEO", "ASANA", "ATLASSIAN", "ATTIO", "AUTH0", "BATTLENET", "BITBUCKET", "BLACKBAUD", "BLACKBOARD", "BOLDSIGN", "BORNEO", "BOX", "BRAINTREE", "BREX", "BREX_STAGING", "BRIGHTPEARL", "CALENDLY", "CANVA", "CANVAS", "CHATWORK", "CLICKUP", "CONFLUENCE", "CONTENTFUL", "D2LBRIGHTSPACE", "DEEL", "DISCORD", "DISCORDBOT", "DOCUSIGN", "DROPBOX", "DROPBOX_SIGN", "DYNAMICS365", "EPIC_GAMES", "EVENTBRITE", "EXIST", "FACEBOOK", "FIGMA", "FITBIT", "FRESHBOOKS", "FRONT", "GITHUB", "GMAIL", "GMAIL_BETA", "GO_TO_WEBINAR", "GOOGLE_ANALYTICS", "GOOGLE_DRIVE_BETA", "GOOGLE_MAPS", "GOOGLECALENDAR", "GOOGLEDOCS", "GOOGLEDRIVE", "GOOGLEMEET", "GOOGLEPHOTOS", "GOOGLESHEETS", "GOOGLETASKS", "GORGIAS", "GUMROAD", "HARVEST", "HIGHLEVEL", "HUBSPOT", "ICIMS_TALENT_CLOUD", "INTERCOM", "JIRA", "KEAP", "KLAVIYO", "LASTPASS", "LEVER", "LEVER_SANDBOX", "LINEAR", "LINKEDIN", "LINKHUT", "MAILCHIMP", "MICROSOFT_TEAMS", "MICROSOFT_TENANT", "MIRO", "MONDAY", "MURAL", "NETSUITE", "NOTION", "ONE_DRIVE", "OUTLOOK", "PAGERDUTY", "PIPEDRIVE", "PRODUCTBOARD", "REDDIT", "RING_CENTRAL", "RIPPLING", "SAGE", "SALESFORCE", "SEISMIC", "SERVICEM8", "SHARE_POINT", "SHOPIFY", "SLACK", "SLACKBOT", "SMARTRECRUITERS", "SPOTIFY", "SQUARE", "STACK_EXCHANGE", "SURVEY_MONKEY", "TIMELY", "TODOIST", "TONEDEN", "TRELLO", "TWITCH", "TWITTER", "TWITTER_MEDIA", "WAKATIME", "WAVE_ACCOUNTING", "WEBEX", "WIZ", "WRIKE", "XERO", "YANDEX", "YNAB", "YOUTUBE", "ZENDESK", "ZOHO", "ZOHO_BIGIN", "ZOHO_BOOKS", "ZOHO_DESK", "ZOHO_INVENTORY", "ZOHO_INVOICE", "ZOHO_MAIL", "ZOOM"], "options_metadata": [], "placeholder": "", "refresh_button": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "GMAIL"}, "auth_link": {"_input_type": "LinkInput", "advanced": true, "display_name": "Authentication Link", "dynamic": true, "info": "Click to authenticate with OAuth2", "load_from_db": false, "name": "auth_link", "placeholder": "Click to authenticate", "required": false, "show": false, "title_case": false, "type": "link", "value": ""}, "auth_status": {"_input_type": "StrInput", "advanced": false, "display_name": "Auth Status", "dynamic": true, "info": "Current authentication status", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "auth_status", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "✅"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "# Standard library imports\nfrom collections.abc import Sequence\nfrom typing import Any\n\nimport requests\n\n# Third-party imports\nfrom composio.client.collections import AppAuthScheme\nfrom composio.client.exceptions import NoItemsFound\nfrom composio_langchain import Action, ComposioToolSet\nfrom langchain_core.tools import Tool\nfrom loguru import logger\n\n# Local imports\nfrom langflow.base.langchain_utilities.model import LCToolComponent\nfrom langflow.inputs import DropdownInput, LinkInput, MessageTextInput, MultiselectInput, SecretStrInput, StrInput\nfrom langflow.io import Output\n\n\nclass ComposioAPIComponent(LCToolComponent):\n    display_name: str = \"Composio Tools\"\n    description: str = \"Use Composio toolset to run actions with your agent\"\n    name = \"ComposioAPI\"\n    icon = \"Composio\"\n    documentation: str = \"https://docs.composio.dev\"\n\n    inputs = [\n        # Basic configuration inputs\n        MessageTextInput(name=\"entity_id\", display_name=\"Entity ID\", value=\"default\", advanced=True),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"Composio API Key\",\n            required=True,\n            info=\"Refer to https://docs.composio.dev/faq/api_key/api_key\",\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"app_names\",\n            display_name=\"App Name\",\n            options=[],\n            value=\"\",\n            info=\"The app name to use. Please refresh after selecting app name\",\n            refresh_button=True,\n            required=True,\n        ),\n        # Authentication-related inputs (initially hidden)\n        SecretStrInput(\n            name=\"app_credentials\",\n            display_name=\"App Credentials\",\n            required=False,\n            dynamic=True,\n            show=False,\n            info=\"Credentials for app authentication (API Key, Password, etc)\",\n            load_from_db=False,\n        ),\n        MessageTextInput(\n            name=\"username\",\n            display_name=\"Username\",\n            required=False,\n            dynamic=True,\n            show=False,\n            info=\"Username for Basic authentication\",\n        ),\n        LinkInput(\n            name=\"auth_link\",\n            display_name=\"Authentication Link\",\n            value=\"\",\n            info=\"Click to authenticate with OAuth2\",\n            dynamic=True,\n            show=False,\n            placeholder=\"Click to authenticate\",\n        ),\n        StrInput(\n            name=\"auth_status\",\n            display_name=\"Auth Status\",\n            value=\"Not Connected\",\n            info=\"Current authentication status\",\n            dynamic=True,\n            show=False,\n        ),\n        MultiselectInput(\n            name=\"action_names\",\n            display_name=\"Actions to use\",\n            required=True,\n            options=[],\n            value=[],\n            info=\"The actions to pass to agent to execute\",\n            dynamic=True,\n            show=False,\n        ),\n    ]\n\n    outputs = [\n        Output(name=\"tools\", display_name=\"Tools\", method=\"build_tool\"),\n    ]\n\n    def _check_for_authorization(self, app: str) -> str:\n        \"\"\"Checks if the app is authorized.\n\n        Args:\n            app (str): The app name to check authorization for.\n\n        Returns:\n            str: The authorization status or URL.\n        \"\"\"\n        toolset = self._build_wrapper()\n        entity = toolset.client.get_entity(id=self.entity_id)\n        try:\n            # Check if user is already connected\n            entity.get_connection(app=app)\n        except NoItemsFound:\n            # Get auth scheme for the app\n            auth_scheme = self._get_auth_scheme(app)\n            return self._handle_auth_by_scheme(entity, app, auth_scheme)\n        except Exception:  # noqa: BLE001\n            logger.exception(\"Authorization error\")\n            return \"Error checking authorization\"\n        else:\n            return f\"{app} CONNECTED\"\n\n    def _get_auth_scheme(self, app_name: str) -> AppAuthScheme:\n        \"\"\"Get the primary auth scheme for an app.\n\n        Args:\n            app_name (str): The name of the app to get auth scheme for.\n\n        Returns:\n            AppAuthScheme: The auth scheme details.\n        \"\"\"\n        toolset = self._build_wrapper()\n        try:\n            return toolset.get_auth_scheme_for_app(app=app_name.lower())\n        except Exception:  # noqa: BLE001\n            logger.exception(f\"Error getting auth scheme for {app_name}\")\n            return None\n\n    def _get_oauth_apps(self, api_key: str) -> list[str]:\n        \"\"\"Fetch OAuth-enabled apps from Composio API.\n\n        Args:\n            api_key (str): The Composio API key.\n\n        Returns:\n            list[str]: A list containing OAuth-enabled app names.\n        \"\"\"\n        oauth_apps = []\n        try:\n            url = \"https://backend.composio.dev/api/v1/apps\"\n            headers = {\"x-api-key\": api_key}\n            params = {\n                \"includeLocal\": \"true\",\n                \"additionalFields\": \"auth_schemes\",\n                \"sortBy\": \"alphabet\",\n            }\n\n            response = requests.get(url, headers=headers, params=params, timeout=20)\n            data = response.json()\n\n            for item in data.get(\"items\", []):\n                for auth_scheme in item.get(\"auth_schemes\", []):\n                    if auth_scheme.get(\"mode\") in {\"OAUTH1\", \"OAUTH2\"}:\n                        oauth_apps.append(item[\"key\"].upper())\n                        break\n        except requests.RequestException as e:\n            logger.error(f\"Error fetching OAuth apps: {e}\")\n            return []\n        else:\n            return oauth_apps\n\n    def _handle_auth_by_scheme(self, entity: Any, app: str, auth_scheme: AppAuthScheme) -> str:\n        \"\"\"Handle authentication based on the auth scheme.\n\n        Args:\n            entity (Any): The entity instance.\n            app (str): The app name.\n            auth_scheme (AppAuthScheme): The auth scheme details.\n\n        Returns:\n            str: The authentication status or URL.\n        \"\"\"\n        auth_mode = auth_scheme.auth_mode\n\n        try:\n            # First check if already connected\n            entity.get_connection(app=app)\n        except NoItemsFound:\n            # If not connected, handle new connection based on auth mode\n            if auth_mode == \"API_KEY\":\n                if hasattr(self, \"app_credentials\") and self.app_credentials:\n                    try:\n                        entity.initiate_connection(\n                            app_name=app,\n                            auth_mode=\"API_KEY\",\n                            auth_config={\"api_key\": self.app_credentials},\n                            use_composio_auth=False,\n                            force_new_integration=True,\n                        )\n                    except Exception as e:  # noqa: BLE001\n                        logger.error(f\"Error connecting with API Key: {e}\")\n                        return \"Invalid API Key\"\n                    else:\n                        return f\"{app} CONNECTED\"\n                return \"Enter API Key\"\n\n            if (\n                auth_mode == \"BASIC\"\n                and hasattr(self, \"username\")\n                and hasattr(self, \"app_credentials\")\n                and self.username\n                and self.app_credentials\n            ):\n                try:\n                    entity.initiate_connection(\n                        app_name=app,\n                        auth_mode=\"BASIC\",\n                        auth_config={\"username\": self.username, \"password\": self.app_credentials},\n                        use_composio_auth=False,\n                        force_new_integration=True,\n                    )\n                except Exception as e:  # noqa: BLE001\n                    logger.error(f\"Error connecting with Basic Auth: {e}\")\n                    return \"Invalid credentials\"\n                else:\n                    return f\"{app} CONNECTED\"\n            elif auth_mode == \"BASIC\":\n                return \"Enter Username and Password\"\n\n            if auth_mode == \"OAUTH2\":\n                try:\n                    return self._initiate_default_connection(entity, app)\n                except Exception as e:  # noqa: BLE001\n                    logger.error(f\"Error initiating OAuth2: {e}\")\n                    return \"OAuth2 initialization failed\"\n\n            return \"Unsupported auth mode\"\n        except Exception as e:  # noqa: BLE001\n            logger.error(f\"Error checking connection status: {e}\")\n            return f\"Error: {e!s}\"\n        else:\n            return f\"{app} CONNECTED\"\n\n    def _initiate_default_connection(self, entity: Any, app: str) -> str:\n        connection = entity.initiate_connection(app_name=app, use_composio_auth=True, force_new_integration=True)\n        return connection.redirectUrl\n\n    def _get_connected_app_names_for_entity(self) -> list[str]:\n        toolset = self._build_wrapper()\n        connections = toolset.client.get_entity(id=self.entity_id).get_connections()\n        return list({connection.appUniqueId for connection in connections})\n\n    def _get_normalized_app_name(self) -> str:\n        \"\"\"Get app name without connection status suffix.\n\n        Returns:\n            str: Normalized app name.\n        \"\"\"\n        return self.app_names.replace(\" ✅\", \"\").replace(\"_connected\", \"\")\n\n    def update_build_config(self, build_config: dict, field_value: Any, field_name: str | None = None) -> dict:  # noqa: ARG002\n        # Update the available apps options from the API\n        if hasattr(self, \"api_key\") and self.api_key != \"\":\n            toolset = self._build_wrapper()\n            build_config[\"app_names\"][\"options\"] = self._get_oauth_apps(api_key=self.api_key)\n\n        # First, ensure all dynamic fields are hidden by default\n        dynamic_fields = [\"app_credentials\", \"username\", \"auth_link\", \"auth_status\", \"action_names\"]\n        for field in dynamic_fields:\n            if field in build_config:\n                if build_config[field][\"value\"] is None or build_config[field][\"value\"] == \"\":\n                    build_config[field][\"show\"] = False\n                    build_config[field][\"advanced\"] = True\n                    build_config[field][\"load_from_db\"] = False\n                else:\n                    build_config[field][\"show\"] = True\n                    build_config[field][\"advanced\"] = False\n\n        if field_name == \"app_names\" and (not hasattr(self, \"app_names\") or not self.app_names):\n            build_config[\"auth_status\"][\"show\"] = True\n            build_config[\"auth_status\"][\"value\"] = \"Please select an app first\"\n            return build_config\n\n        if field_name == \"app_names\" and hasattr(self, \"api_key\") and self.api_key != \"\":\n            # app_name = self._get_normalized_app_name()\n            app_name = self.app_names\n            try:\n                toolset = self._build_wrapper()\n                entity = toolset.client.get_entity(id=self.entity_id)\n\n                # Always show auth_status when app is selected\n                build_config[\"auth_status\"][\"show\"] = True\n                build_config[\"auth_status\"][\"advanced\"] = False\n\n                try:\n                    # Check if already connected\n                    entity.get_connection(app=app_name)\n                    build_config[\"auth_status\"][\"value\"] = \"✅\"\n                    build_config[\"auth_link\"][\"show\"] = False\n                    # Show action selection for connected apps\n                    build_config[\"action_names\"][\"show\"] = True\n                    build_config[\"action_names\"][\"advanced\"] = False\n\n                except NoItemsFound:\n                    # Get auth scheme and show relevant fields\n                    auth_scheme = self._get_auth_scheme(app_name)\n                    auth_mode = auth_scheme.auth_mode\n                    logger.info(f\"Auth mode for {app_name}: {auth_mode}\")\n\n                    if auth_mode == \"API_KEY\":\n                        build_config[\"app_credentials\"][\"show\"] = True\n                        build_config[\"app_credentials\"][\"advanced\"] = False\n                        build_config[\"app_credentials\"][\"display_name\"] = \"API Key\"\n                        build_config[\"auth_status\"][\"value\"] = \"Enter API Key\"\n\n                    elif auth_mode == \"BASIC\":\n                        build_config[\"username\"][\"show\"] = True\n                        build_config[\"username\"][\"advanced\"] = False\n                        build_config[\"app_credentials\"][\"show\"] = True\n                        build_config[\"app_credentials\"][\"advanced\"] = False\n                        build_config[\"app_credentials\"][\"display_name\"] = \"Password\"\n                        build_config[\"auth_status\"][\"value\"] = \"Enter Username and Password\"\n\n                    elif auth_mode == \"OAUTH2\":\n                        build_config[\"auth_link\"][\"show\"] = True\n                        build_config[\"auth_link\"][\"advanced\"] = False\n                        auth_url = self._initiate_default_connection(entity, app_name)\n                        build_config[\"auth_link\"][\"value\"] = auth_url\n                        build_config[\"auth_status\"][\"value\"] = \"Click link to authenticate\"\n\n                    else:\n                        build_config[\"auth_status\"][\"value\"] = \"Unsupported auth mode\"\n\n                # Update action names if connected\n                if build_config[\"auth_status\"][\"value\"] == \"✅\":\n                    all_action_names = [str(action).replace(\"Action.\", \"\") for action in Action.all()]\n                    app_action_names = [\n                        action_name\n                        for action_name in all_action_names\n                        if action_name.lower().startswith(app_name.lower() + \"_\")\n                    ]\n                    if build_config[\"action_names\"][\"options\"] != app_action_names:\n                        build_config[\"action_names\"][\"options\"] = app_action_names\n                        build_config[\"action_names\"][\"value\"] = [app_action_names[0]] if app_action_names else [\"\"]\n\n            except Exception as e:  # noqa: BLE001\n                logger.error(f\"Error checking auth status: {e}, app: {app_name}\")\n                build_config[\"auth_status\"][\"value\"] = f\"Error: {e!s}\"\n\n        return build_config\n\n    def build_tool(self) -> Sequence[Tool]:\n        \"\"\"Build Composio tools based on selected actions.\n\n        Returns:\n            Sequence[Tool]: List of configured Composio tools.\n        \"\"\"\n        composio_toolset = self._build_wrapper()\n        return composio_toolset.get_tools(actions=self.action_names)\n\n    def _build_wrapper(self) -> ComposioToolSet:\n        \"\"\"Build the Composio toolset wrapper.\n\n        Returns:\n            ComposioToolSet: The initialized toolset.\n\n        Raises:\n            ValueError: If the API key is not found or invalid.\n        \"\"\"\n        try:\n            if not self.api_key:\n                msg = \"Composio API Key is required\"\n                raise ValueError(msg)\n            return ComposioToolSet(api_key=self.api_key, entity_id=self.entity_id)\n        except ValueError as e:\n            logger.error(f\"Error building Composio wrapper: {e}\")\n            msg = \"Please provide a valid Composio API Key in the component settings\"\n            raise ValueError(msg) from e\n"}, "entity_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Entity ID", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "entity_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "default"}, "username": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Username", "dynamic": true, "info": "Username for Basic authentication", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "username", "placeholder": "", "required": false, "show": false, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "ComposioAPI"}, "dragging": false, "id": "ComposioAPI-Tdreq", "measured": {"height": 497, "width": 320}, "position": {"x": -137.53986902236176, "y": 20.325147658297382}, "selected": true, "type": "genericNode"}], "viewport": {"x": 666.3549315745112, "y": 178.32327136900147, "zoom": 0.8590936972080208}}, "description": "与 Gmail 进行交互，以实现发送邮件、创建草稿和获取消息的功能。", "endpoint_name": null, "id": "db962555-dbb0-477f-85cc-536c68b32ee8", "is_component": false, "last_tested_version": "1.2.0", "name": "Gmail 代理", "tags": ["agents"]}