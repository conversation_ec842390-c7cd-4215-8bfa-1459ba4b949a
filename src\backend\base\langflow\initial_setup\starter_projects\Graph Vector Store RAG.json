{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIEmbeddings", "id": "OpenAIEmbeddings-Z4ej5", "name": "embeddings", "output_types": ["Embeddings"]}, "targetHandle": {"fieldName": "embedding_model", "id": "AstraDBGraph-WdeLY", "inputTypes": ["Embeddings"], "type": "other"}}, "id": "reactflow__edge-OpenAIEmbeddings-Z4ej5{œdataTypeœ:œOpenAIEmbeddingsœ,œidœ:œOpenAIEmbeddings-Z4ej5œ,œnameœ:œembeddingsœ,œoutput_typesœ:[œEmbeddingsœ]}-AstraDBGraph-WdeLY{œfieldNameœ:œembedding_modelœ,œidœ:œAstraDBGraph-WdeLYœ,œinputTypesœ:[œEmbeddingsœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIEmbeddings-Z4ej5", "sourceHandle": "{œdataTypeœ: œOpenAIEmbeddingsœ, œidœ: œOpenAIEmbeddings-Z4ej5œ, œnameœ: œembeddingsœ, œoutput_typesœ: [œEmbeddingsœ]}", "target": "AstraDBGraph-WdeLY", "targetHandle": "{œfieldNameœ: œembedding_modelœ, œidœ: œAstraDBGraph-WdeLYœ, œinputTypesœ: [œEmbeddingsœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "AstraDBGraph", "id": "AstraDBGraph-WdeLY", "name": "search_results", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data", "id": "ParseData-s05ib", "inputTypes": ["Data"], "type": "other"}}, "id": "reactflow__edge-AstraDBGraph-WdeLY{œdataTypeœ:œAstraDBGraphœ,œidœ:œAstraDBGraph-WdeLYœ,œnameœ:œsearch_resultsœ,œoutput_typesœ:[œDataœ]}-ParseData-s05ib{œfieldNameœ:œdataœ,œidœ:œParseData-s05ibœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "AstraDBGraph-WdeLY", "sourceHandle": "{œdataTypeœ: œAstraDBGraphœ, œidœ: œAstraDBGraph-WdeLYœ, œnameœ: œsearch_resultsœ, œoutput_typesœ: [œDataœ]}", "target": "ParseData-s05ib", "targetHandle": "{œfieldNameœ: œdataœ, œidœ: œParseData-s05ibœ, œinputTypesœ: [œDataœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ParseData", "id": "ParseData-s05ib", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "context", "id": "Prompt-KZb70", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "reactflow__edge-ParseData-s05ib{œdataTypeœ:œParseDataœ,œidœ:œParseData-s05ibœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-KZb70{œfieldNameœ:œcontextœ,œidœ:œPrompt-KZb70œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "source": "ParseData-s05ib", "sourceHandle": "{œdataTypeœ: œParseDataœ, œidœ: œParseData-s05ibœ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-KZb70", "targetHandle": "{œfieldNameœ: œcontextœ, œidœ: œPrompt-KZb70œ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-KZb70", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "OpenAIModel-hhsjV", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-KZb70{œdataTypeœ:œPromptœ,œidœ:œPrompt-KZb70œ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-OpenAIModel-hhsjV{œfieldNameœ:œinput_valueœ,œidœ:œOpenAIModel-hhsjVœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-KZb70", "sourceHandle": "{œdataTypeœ: œPromptœ, œidœ: œPrompt-KZb70œ, œnameœ: œpromptœ, œoutput_typesœ: [œMessageœ]}", "target": "OpenAIModel-hhsjV", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œOpenAIModel-hhsjVœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-hhsjV", "name": "text_output", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-yHZfU", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-OpenAIModel-hhsjV{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-hhsjVœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-yHZfU{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-yHZfUœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "OpenAIModel-hhsjV", "sourceHandle": "{œdataTypeœ: œOpenAIModelœ, œidœ: œOpenAIModel-hhsjVœ, œnameœ: œtext_outputœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-yHZfU", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-yHZfUœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "URL", "id": "URL-eBpyO", "name": "data", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data_input", "id": "LanguageRecursiveTextSplitter-TG6vC", "inputTypes": ["Document", "Data"], "type": "other"}}, "id": "reactflow__edge-URL-eBpyO{œdataTypeœ:œURLœ,œidœ:œURL-eBpyOœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-LanguageRecursiveTextSplitter-TG6vC{œfieldNameœ:œdata_inputœ,œidœ:œLanguageRecursiveTextSplitter-TG6vCœ,œinputTypesœ:[œDocumentœ,œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "URL-eBpyO", "sourceHandle": "{œdataTypeœ: œURLœ, œidœ: œURL-eBpyOœ, œnameœ: œdataœ, œoutput_typesœ: [œDataœ]}", "target": "LanguageRecursiveTextSplitter-TG6vC", "targetHandle": "{œfieldNameœ: œdata_inputœ, œidœ: œLanguageRecursiveTextSplitter-TG6vCœ, œinputTypesœ: [œDocumentœ, œDataœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "LanguageRecursiveTextSplitter", "id": "LanguageRecursiveTextSplitter-TG6vC", "name": "data", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data_input", "id": "HtmlLinkExtractor-I8rzh", "inputTypes": ["Document", "Data"], "type": "other"}}, "id": "reactflow__edge-LanguageRecursiveTextSplitter-TG6vC{œdataTypeœ:œLanguageRecursiveTextSplitterœ,œidœ:œLanguageRecursiveTextSplitter-TG6vCœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-HtmlLinkExtractor-I8rzh{œfieldNameœ:œdata_inputœ,œidœ:œHtmlLinkExtractor-I8rzhœ,œinputTypesœ:[œDocumentœ,œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "LanguageRecursiveTextSplitter-TG6vC", "sourceHandle": "{œdataTypeœ: œLanguageRecursiveTextSplitterœ, œidœ: œLanguageRecursiveTextSplitter-TG6vCœ, œnameœ: œdataœ, œoutput_typesœ: [œDataœ]}", "target": "HtmlLinkExtractor-I8rzh", "targetHandle": "{œfieldNameœ: œdata_inputœ, œidœ: œHtmlLinkExtractor-I8rzhœ, œinputTypesœ: [œDocumentœ, œDataœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIEmbeddings", "id": "OpenAIEmbeddings-RD7F8", "name": "embeddings", "output_types": ["Embeddings"]}, "targetHandle": {"fieldName": "embedding_model", "id": "AstraDBGraph-OBH6o", "inputTypes": ["Embeddings"], "type": "other"}}, "id": "reactflow__edge-OpenAIEmbeddings-RD7F8{œdataTypeœ:œOpenAIEmbeddingsœ,œidœ:œOpenAIEmbeddings-RD7F8œ,œnameœ:œembeddingsœ,œoutput_typesœ:[œEmbeddingsœ]}-AstraDBGraph-OBH6o{œfieldNameœ:œembedding_modelœ,œidœ:œAstraDBGraph-OBH6oœ,œinputTypesœ:[œEmbeddingsœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIEmbeddings-RD7F8", "sourceHandle": "{œdataTypeœ: œOpenAIEmbeddingsœ, œidœ: œOpenAIEmbeddings-RD7F8œ, œnameœ: œembeddingsœ, œoutput_typesœ: [œEmbeddingsœ]}", "target": "AstraDBGraph-OBH6o", "targetHandle": "{œfieldNameœ: œembedding_modelœ, œidœ: œAstraDBGraph-OBH6oœ, œinputTypesœ: [œEmbeddingsœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "HtmlLinkExtractor", "id": "HtmlLinkExtractor-I8rzh", "name": "data", "output_types": ["Data"]}, "targetHandle": {"fieldName": "ingest_data", "id": "AstraDBGraph-OBH6o", "inputTypes": ["Data", "DataFrame"], "type": "other"}}, "id": "reactflow__edge-HtmlLinkExtractor-I8rzh{œdataTypeœ:œHtmlLinkExtractorœ,œidœ:œHtmlLinkExtractor-I8rzhœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-AstraDBGraph-OBH6o{œfieldNameœ:œingest_dataœ,œidœ:œAstraDBGraph-OBH6oœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "HtmlLinkExtractor-I8rzh", "sourceHandle": "{œdataTypeœ: œHtmlLinkExtractorœ, œidœ: œHtmlLinkExtractor-I8rzhœ, œnameœ: œdataœ, œoutput_typesœ: [œDataœ]}", "target": "AstraDBGraph-OBH6o", "targetHandle": "{œfieldNameœ: œingest_dataœ, œidœ: œAstraDBGraph-OBH6oœ, œinputTypesœ: [œDataœ, œDataFrameœ], œtypeœ: œotherœ}"}, {"animated": false, "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-2dZuD", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "search_query", "id": "AstraDBGraph-WdeLY", "inputTypes": ["Message"], "type": "str"}}, "id": "xy-edge__ChatInput-2dZuD{œdataTypeœ:œChatInputœ,œidœ:œChatInput-2dZuDœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-AstraDBGraph-WdeLY{œfieldNameœ:œsearch_queryœ,œidœ:œAstraDBGraph-WdeLYœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-2dZuD", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-2dZuDœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "AstraDBGraph-WdeLY", "targetHandle": "{œfieldNameœ: œsearch_queryœ, œidœ: œAstraDBGraph-WdeLYœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-2dZuD", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "question", "id": "Prompt-KZb70", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "xy-edge__ChatInput-2dZuD{œdataTypeœ:œChatInputœ,œidœ:œChatInput-2dZuDœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-Prompt-KZb70{œfieldNameœ:œquestionœ,œidœ:œPrompt-KZb70œ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-2dZuD", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-2dZuDœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-KZb70", "targetHandle": "{œfieldNameœ: œquestionœ, œidœ: œPrompt-KZb70œ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}], "nodes": [{"data": {"id": "OpenAIEmbeddings-Z4ej5", "node": {"base_classes": ["Embeddings"], "beta": false, "category": "embeddings", "conditional_paths": [], "custom_fields": {}, "description": "Generate embeddings using OpenAI models.", "display_name": "OpenAI Embeddings", "documentation": "", "edited": false, "field_order": ["default_headers", "default_query", "chunk_size", "client", "deployment", "embedding_ctx_length", "max_retries", "model", "model_kwargs", "openai_api_key", "openai_api_base", "openai_api_type", "openai_api_version", "openai_organization", "openai_proxy", "request_timeout", "show_progress_bar", "skip_empty", "tiktoken_model_name", "tiktoken_enable", "dimensions"], "frozen": false, "icon": "OpenAI", "key": "OpenAIEmbeddings", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "嵌入向量", "method": "build_embeddings", "name": "embeddings", "required_inputs": ["openai_api_key"], "selected": "Embeddings", "tool_mode": true, "types": ["Embeddings"], "value": "__UNDEFINED__"}], "pinned": false, "score": 5.2003277518821525e-05, "template": {"_type": "Component", "chunk_size": {"_input_type": "IntInput", "advanced": true, "display_name": "分块大小", "dynamic": false, "info": "", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "client": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "客户端", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "client", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import OpenAIEmbeddings\n\nfrom langflow.base.embeddings.model import LCEmbeddingsModel\nfrom langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES\nfrom langflow.field_typing import Embeddings\nfrom langflow.io import BoolInput, DictInput, DropdownInput, FloatInput, IntInput, MessageTextInput, SecretStrInput\n\n\nclass OpenAIEmbeddingsComponent(LCEmbeddingsModel):\n    display_name = \"OpenAI嵌入\"  # \"OpenAI Embeddings\"\n    description = \"使用 OpenAI 模型生成嵌入。\"  # \"Generate embeddings using OpenAI models.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIEmbeddings\"\n\n    inputs = [\n        DictInput(\n            name=\"default_headers\",\n            display_name=\"默认请求头\",  # \"Default Headers\"\n            advanced=True,\n            info=\"用于 API 请求的默认请求头。\",  # \"Default headers to use for the API request.\"\n        ),\n        DictInput(\n            name=\"default_query\",\n            display_name=\"默认查询参数\",  # \"Default Query\"\n            advanced=True,\n            info=\"用于 API 请求的默认查询参数。\",  # \"Default query parameters to use for the API request.\"\n        ),\n        IntInput(\n            name=\"chunk_size\",\n            display_name=\"分块大小\",  # \"Chunk Size\"\n            advanced=True,\n            value=1000,\n        ),\n        MessageTextInput(\n            name=\"client\",\n            display_name=\"客户端\",  # \"Client\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"deployment\",\n            display_name=\"部署\",  # \"Deployment\"\n            advanced=True,\n        ),\n        IntInput(\n            name=\"embedding_ctx_length\",\n            display_name=\"嵌入上下文长度\",  # \"Embedding Context Length\"\n            advanced=True,\n            value=1536,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # \"Max Retries\"\n            value=3,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"模型\",  # \"Model\"\n            advanced=False,\n            options=OPENAI_EMBEDDING_MODEL_NAMES,\n            value=\"text-embedding-3-small\",\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # \"Model Kwargs\"\n            advanced=True,\n        ),\n        SecretStrInput(\n            name=\"openai_api_key\",\n            display_name=\"OpenAI API 密钥\",  # \"OpenAI API Key\"\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础 URL\",  # \"OpenAI API Base\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_type\",\n            display_name=\"OpenAI API 类型\",  # \"OpenAI API Type\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_version\",\n            display_name=\"OpenAI API 版本\",  # \"OpenAI API Version\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_organization\",\n            display_name=\"OpenAI 组织\",  # \"OpenAI Organization\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_proxy\",\n            display_name=\"OpenAI 代理\",  # \"OpenAI Proxy\"\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"request_timeout\",\n            display_name=\"请求超时时间\",  # \"Request Timeout\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"show_progress_bar\",\n            display_name=\"显示进度条\",  # \"Show Progress Bar\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"skip_empty\",\n            display_name=\"跳过空值\",  # \"Skip Empty\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"tiktoken_model_name\",\n            display_name=\"TikToken 模型名称\",  # \"TikToken Model Name\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"tiktoken_enable\",\n            display_name=\"启用 TikToken\",  # \"TikToken Enable\"\n            advanced=True,\n            value=True,\n            info=\"如果为 False，则必须安装 transformers。\",  # \"If False, you must have transformers installed.\"\n        ),\n        IntInput(\n            name=\"dimensions\",\n            display_name=\"维度\",  # \"Dimensions\"\n            info=\"生成的嵌入输出的维度数量，仅某些模型支持。\",  # \"The number of dimensions the resulting output embeddings should have. Only supported by certain models.\"\n            advanced=True,\n        ),\n    ]\n\n    def build_embeddings(self) -> Embeddings:\n        return OpenAIEmbeddings(\n            client=self.client or None,\n            model=self.model,\n            dimensions=self.dimensions or None,\n            deployment=self.deployment or None,\n            api_version=self.openai_api_version or None,\n            base_url=self.openai_api_base or None,\n            openai_api_type=self.openai_api_type or None,\n            openai_proxy=self.openai_proxy or None,\n            embedding_ctx_length=self.embedding_ctx_length,\n            api_key=self.openai_api_key or None,\n            organization=self.openai_organization or None,\n            allowed_special=\"all\",\n            disallowed_special=\"all\",\n            chunk_size=self.chunk_size,\n            max_retries=self.max_retries,\n            timeout=self.request_timeout or None,\n            tiktoken_enabled=self.tiktoken_enable,\n            tiktoken_model_name=self.tiktoken_model_name or None,\n            show_progress_bar=self.show_progress_bar,\n            model_kwargs=self.model_kwargs,\n            skip_empty=self.skip_empty,\n            default_headers=self.default_headers or None,\n            default_query=self.default_query or None,\n        )\n"}, "default_headers": {"_input_type": "DictInput", "advanced": true, "display_name": "默认请求头", "dynamic": false, "info": "用于 API 请求的默认请求头。", "list": false, "name": "default_headers", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "default_query": {"_input_type": "DictInput", "advanced": true, "display_name": "默认查询参数", "dynamic": false, "info": "用于 API 请求的默认查询参数。", "list": false, "name": "default_query", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "deployment": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "部署", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "deployment", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "dimensions": {"_input_type": "IntInput", "advanced": true, "display_name": "维度", "dynamic": false, "info": "生成的嵌入输出的维度数量，仅某些模型支持。", "list": false, "name": "dimensions", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": ""}, "embedding_ctx_length": {"_input_type": "IntInput", "advanced": true, "display_name": "嵌入上下文长度", "dynamic": false, "info": "", "list": false, "name": "embedding_ctx_length", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1536}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "", "list": false, "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 3}, "model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "模型", "dynamic": false, "info": "", "name": "model", "options": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text-embedding-3-small"}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "", "list": false, "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "openai_api_base": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 基础 URL", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "", "input_types": ["Message"], "load_from_db": true, "name": "openai_api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "openai_api_type": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 类型", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_type", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_version": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 版本", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_version", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_organization": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 组织", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_organization", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_proxy": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 代理", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_proxy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "request_timeout": {"_input_type": "FloatInput", "advanced": true, "display_name": "请求超时时间", "dynamic": false, "info": "", "list": false, "name": "request_timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "float", "value": ""}, "show_progress_bar": {"_input_type": "BoolInput", "advanced": true, "display_name": "显示进度条", "dynamic": false, "info": "", "list": false, "name": "show_progress_bar", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "skip_empty": {"_input_type": "BoolInput", "advanced": true, "display_name": "跳过空值", "dynamic": false, "info": "", "list": false, "name": "skip_empty", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "tiktoken_enable": {"_input_type": "BoolInput", "advanced": true, "display_name": "启用 TikToken", "dynamic": false, "info": "如果为 False，则必须安装 transformers。", "list": false, "name": "tiktoken_enable", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "tiktoken_model_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "TikToken 模型名称", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tiktoken_model_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "OpenAIEmbeddings"}, "dragging": false, "height": 320, "id": "OpenAIEmbeddings-Z4ej5", "measured": {"height": 320, "width": 320}, "position": {"x": -1794.2005649575194, "y": 7363.047766731913}, "positionAbsolute": {"x": -1530.978455316274, "y": 6600.325433283265}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Implementation of Graph Vector Store using Astra DB", "display_name": "Astra DB Graph", "id": "AstraDBGraph-WdeLY", "node": {"base_classes": ["Data"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Implementation of Graph Vector Store using Astra DB", "display_name": "Astra DB Graph", "documentation": "", "edited": false, "field_order": ["token", "api_endpoint", "collection_name", "metadata_incoming_links_key", "search_query", "ingest_data", "keyspace", "embedding_model", "metric", "batch_size", "bulk_insert_batch_concurrency", "bulk_insert_overwrite_concurrency", "bulk_delete_concurrency", "setup_mode", "pre_delete_collection", "metadata_indexing_include", "metadata_indexing_exclude", "collection_indexing_policy", "number_of_results", "search_type", "search_score_threshold", "search_filter"], "frozen": false, "icon": "AstraDB", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "搜索结果", "method": "search_documents", "name": "search_results", "required_inputs": ["api_endpoint", "collection_name", "token"], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据框", "method": "as_dataframe", "name": "dataframe", "required_inputs": [], "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "api_endpoint": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "API Endpoint", "dynamic": false, "info": "API endpoint URL for the Astra DB service.", "input_types": ["Message"], "load_from_db": true, "name": "api_endpoint", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_API_ENDPOINT"}, "batch_size": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "info": "Optional number of data to process in a single batch.", "list": false, "name": "batch_size", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "bulk_delete_concurrency": {"_input_type": "IntInput", "advanced": true, "display_name": "Bulk Delete Concurrency", "dynamic": false, "info": "Optional concurrency level for bulk delete operations.", "list": false, "name": "bulk_delete_concurrency", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "bulk_insert_batch_concurrency": {"_input_type": "IntInput", "advanced": true, "display_name": "Bulk Insert Batch Concurrency", "dynamic": false, "info": "Optional concurrency level for bulk insert operations.", "list": false, "name": "bulk_insert_batch_concurrency", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "bulk_insert_overwrite_concurrency": {"_input_type": "IntInput", "advanced": true, "display_name": "Bulk Insert Overwrite Concurrency", "dynamic": false, "info": "Optional concurrency level for bulk insert operations that overwrite existing data.", "list": false, "name": "bulk_insert_overwrite_concurrency", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import os\n\nimport orjson\nfrom astrapy.admin import parse_api_endpoint\n\nfrom langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store\nfrom langflow.helpers import docs_to_data\nfrom langflow.inputs import (\n    BoolInput,\n    DictInput,\n    DropdownInput,\n    FloatInput,\n    HandleInput,\n    IntInput,\n    SecretStrInput,\n    StrInput,\n)\nfrom langflow.schema import Data\n\n\nclass AstraDBGraphVectorStoreComponent(LCVectorStoreComponent):\n    display_name: str = \"Astra DB Graph\"\n    description: str = \"Implementation of Graph Vector Store using Astra DB\"\n    name = \"AstraDBGraph\"\n    icon: str = \"AstraDB\"\n\n    inputs = [\n        SecretStrInput(\n            name=\"token\",\n            display_name=\"Astra DB Application Token\",\n            info=\"Authentication token for accessing Astra DB.\",\n            value=\"ASTRA_DB_APPLICATION_TOKEN\",\n            required=True,\n            advanced=os.getenv(\"ASTRA_ENHANCED\", \"false\").lower() == \"true\",\n        ),\n        SecretStrInput(\n            name=\"api_endpoint\",\n            display_name=\"Database\" if os.getenv(\"ASTRA_ENHANCED\", \"false\").lower() == \"true\" else \"API Endpoint\",\n            info=\"API endpoint URL for the Astra DB service.\",\n            value=\"ASTRA_DB_API_ENDPOINT\",\n            required=True,\n        ),\n        StrInput(\n            name=\"collection_name\",\n            display_name=\"Collection Name\",\n            info=\"The name of the collection within Astra DB where the vectors will be stored.\",\n            required=True,\n        ),\n        StrInput(\n            name=\"metadata_incoming_links_key\",\n            display_name=\"Metadata incoming links key\",\n            info=\"Metadata key used for incoming links.\",\n            advanced=True,\n        ),\n        *LCVectorStoreComponent.inputs,\n        StrInput(\n            name=\"keyspace\",\n            display_name=\"Keyspace\",\n            info=\"Optional keyspace within Astra DB to use for the collection.\",\n            advanced=True,\n        ),\n        HandleInput(\n            name=\"embedding_model\",\n            display_name=\"Embedding Model\",\n            input_types=[\"Embeddings\"],\n            info=\"Allows an embedding model configuration.\",\n        ),\n        DropdownInput(\n            name=\"metric\",\n            display_name=\"Metric\",\n            info=\"Optional distance metric for vector comparisons in the vector store.\",\n            options=[\"cosine\", \"dot_product\", \"euclidean\"],\n            value=\"cosine\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"batch_size\",\n            display_name=\"Batch Size\",\n            info=\"Optional number of data to process in a single batch.\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"bulk_insert_batch_concurrency\",\n            display_name=\"Bulk Insert Batch Concurrency\",\n            info=\"Optional concurrency level for bulk insert operations.\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"bulk_insert_overwrite_concurrency\",\n            display_name=\"Bulk Insert Overwrite Concurrency\",\n            info=\"Optional concurrency level for bulk insert operations that overwrite existing data.\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"bulk_delete_concurrency\",\n            display_name=\"Bulk Delete Concurrency\",\n            info=\"Optional concurrency level for bulk delete operations.\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"setup_mode\",\n            display_name=\"Setup Mode\",\n            info=\"Configuration mode for setting up the vector store, with options like 'Sync', or 'Off'.\",\n            options=[\"Sync\", \"Off\"],\n            advanced=True,\n            value=\"Sync\",\n        ),\n        BoolInput(\n            name=\"pre_delete_collection\",\n            display_name=\"Pre Delete Collection\",\n            info=\"Boolean flag to determine whether to delete the collection before creating a new one.\",\n            advanced=True,\n            value=False,\n        ),\n        StrInput(\n            name=\"metadata_indexing_include\",\n            display_name=\"Metadata Indexing Include\",\n            info=\"Optional list of metadata fields to include in the indexing.\",\n            advanced=True,\n            list=True,\n        ),\n        StrInput(\n            name=\"metadata_indexing_exclude\",\n            display_name=\"Metadata Indexing Exclude\",\n            info=\"Optional list of metadata fields to exclude from the indexing.\",\n            advanced=True,\n            list=True,\n        ),\n        StrInput(\n            name=\"collection_indexing_policy\",\n            display_name=\"Collection Indexing Policy\",\n            info='Optional JSON string for the \"indexing\" field of the collection. '\n            \"See https://docs.datastax.com/en/astra-db-serverless/api-reference/collections.html#the-indexing-option\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"number_of_results\",\n            display_name=\"Number of Results\",\n            info=\"Number of results to return.\",\n            advanced=True,\n            value=4,\n        ),\n        DropdownInput(\n            name=\"search_type\",\n            display_name=\"Search Type\",\n            info=\"Search type to use\",\n            options=[\n                \"Similarity\",\n                \"Similarity with score threshold\",\n                \"MMR (Max Marginal Relevance)\",\n                \"Graph Traversal\",\n                \"MMR (Max Marginal Relevance) Graph Traversal\",\n            ],\n            value=\"MMR (Max Marginal Relevance) Graph Traversal\",\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"search_score_threshold\",\n            display_name=\"Search Score Threshold\",\n            info=\"Minimum similarity score threshold for search results. \"\n            \"(when using 'Similarity with score threshold')\",\n            value=0,\n            advanced=True,\n        ),\n        DictInput(\n            name=\"search_filter\",\n            display_name=\"Search Metadata Filter\",\n            info=\"Optional dictionary of filters to apply to the search query.\",\n            advanced=True,\n            is_list=True,\n        ),\n    ]\n\n    @check_cached_vector_store\n    def build_vector_store(self):\n        try:\n            from langchain_astradb import AstraDBGraphVectorStore\n            from langchain_astradb.utils.astradb import SetupMode\n        except ImportError as e:\n            msg = (\n                \"Could not import langchain Astra DB integration package. \"\n                \"Please install it with `pip install langchain-astradb`.\"\n            )\n            raise ImportError(msg) from e\n\n        try:\n            if not self.setup_mode:\n                self.setup_mode = self._inputs[\"setup_mode\"].options[0]\n\n            setup_mode_value = SetupMode[self.setup_mode.upper()]\n        except KeyError as e:\n            msg = f\"Invalid setup mode: {self.setup_mode}\"\n            raise ValueError(msg) from e\n\n        try:\n            self.log(f\"Initializing Graph Vector Store {self.collection_name}\")\n\n            vector_store = AstraDBGraphVectorStore(\n                embedding=self.embedding_model,\n                collection_name=self.collection_name,\n                metadata_incoming_links_key=self.metadata_incoming_links_key or \"incoming_links\",\n                token=self.token,\n                api_endpoint=self.api_endpoint,\n                namespace=self.keyspace or None,\n                environment=parse_api_endpoint(self.api_endpoint).environment if self.api_endpoint else None,\n                metric=self.metric or None,\n                batch_size=self.batch_size or None,\n                bulk_insert_batch_concurrency=self.bulk_insert_batch_concurrency or None,\n                bulk_insert_overwrite_concurrency=self.bulk_insert_overwrite_concurrency or None,\n                bulk_delete_concurrency=self.bulk_delete_concurrency or None,\n                setup_mode=setup_mode_value,\n                pre_delete_collection=self.pre_delete_collection,\n                metadata_indexing_include=[s for s in self.metadata_indexing_include if s] or None,\n                metadata_indexing_exclude=[s for s in self.metadata_indexing_exclude if s] or None,\n                collection_indexing_policy=orjson.loads(self.collection_indexing_policy.encode(\"utf-8\"))\n                if self.collection_indexing_policy\n                else None,\n            )\n        except Exception as e:\n            msg = f\"Error initializing AstraDBGraphVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        self.log(f\"Vector Store initialized: {vector_store.astra_env.collection_name}\")\n        self._add_documents_to_vector_store(vector_store)\n\n        return vector_store\n\n    def _add_documents_to_vector_store(self, vector_store) -> None:\n        self.ingest_data = self._prepare_ingest_data()\n\n        documents = []\n        for _input in self.ingest_data or []:\n            if isinstance(_input, Data):\n                documents.append(_input.to_lc_document())\n            else:\n                msg = \"Vector Store Inputs must be Data objects.\"\n                raise TypeError(msg)\n\n        if documents:\n            self.log(f\"Adding {len(documents)} documents to the Vector Store.\")\n            try:\n                vector_store.add_documents(documents)\n            except Exception as e:\n                msg = f\"Error adding documents to AstraDBGraphVectorStore: {e}\"\n                raise ValueError(msg) from e\n        else:\n            self.log(\"No documents to add to the Vector Store.\")\n\n    def _map_search_type(self) -> str:\n        match self.search_type:\n            case \"Similarity\":\n                return \"similarity\"\n            case \"Similarity with score threshold\":\n                return \"similarity_score_threshold\"\n            case \"MMR (Max Marginal Relevance)\":\n                return \"mmr\"\n            case \"Graph Traversal\":\n                return \"traversal\"\n            case \"MMR (Max Marginal Relevance) Graph Traversal\":\n                return \"mmr_traversal\"\n            case _:\n                return \"similarity\"\n\n    def _build_search_args(self):\n        args = {\n            \"k\": self.number_of_results,\n            \"score_threshold\": self.search_score_threshold,\n        }\n\n        if self.search_filter:\n            clean_filter = {k: v for k, v in self.search_filter.items() if k and v}\n            if len(clean_filter) > 0:\n                args[\"filter\"] = clean_filter\n        return args\n\n    def search_documents(self, vector_store=None) -> list[Data]:\n        if not vector_store:\n            vector_store = self.build_vector_store()\n\n        self.log(\"Searching for documents in AstraDBGraphVectorStore.\")\n        self.log(f\"Search query: {self.search_query}\")\n        self.log(f\"Search type: {self.search_type}\")\n        self.log(f\"Number of results: {self.number_of_results}\")\n\n        if self.search_query and isinstance(self.search_query, str) and self.search_query.strip():\n            try:\n                search_type = self._map_search_type()\n                search_args = self._build_search_args()\n\n                docs = vector_store.search(query=self.search_query, search_type=search_type, **search_args)\n\n                # Drop links from the metadata. At this point the links don't add any value for building the\n                # context and haven't been restored to json which causes the conversion to fail.\n                self.log(\"Removing links from metadata.\")\n                for doc in docs:\n                    if \"links\" in doc.metadata:\n                        doc.metadata.pop(\"links\")\n\n            except Exception as e:\n                msg = f\"Error performing search in AstraDBGraphVectorStore: {e}\"\n                raise ValueError(msg) from e\n\n            self.log(f\"Retrieved documents: {len(docs)}\")\n\n            data = docs_to_data(docs)\n\n            self.log(f\"Converted documents to data: {len(data)}\")\n\n            self.status = data\n            return data\n        self.log(\"No search input provided. Skipping search.\")\n        return []\n\n    def get_retriever_kwargs(self):\n        search_args = self._build_search_args()\n        return {\n            \"search_type\": self._map_search_type(),\n            \"search_kwargs\": search_args,\n        }\n"}, "collection_indexing_policy": {"_input_type": "StrInput", "advanced": true, "display_name": "Collection Indexing Policy", "dynamic": false, "info": "Optional JSON string for the \"indexing\" field of the collection. See https://docs.datastax.com/en/astra-db-serverless/api-reference/collections.html#the-indexing-option", "list": false, "load_from_db": false, "name": "collection_indexing_policy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "collection_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Collection Name", "dynamic": false, "info": "The name of the collection within Astra DB where the vectors will be stored.", "list": false, "load_from_db": false, "name": "collection_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "haskell_chunked"}, "embedding_model": {"_input_type": "HandleInput", "advanced": false, "display_name": "Embedding Model", "dynamic": false, "info": "Allows an embedding model configuration.", "input_types": ["Embeddings"], "list": false, "name": "embedding_model", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "ingest_data": {"_input_type": "DataInput", "advanced": false, "display_name": "导入数据", "dynamic": false, "info": "", "input_types": ["Data", "DataFrame"], "list": true, "name": "ingest_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "keyspace": {"_input_type": "StrInput", "advanced": true, "display_name": "Keyspace", "dynamic": false, "info": "Optional keyspace within Astra DB to use for the collection.", "list": false, "load_from_db": false, "name": "keyspace", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "metadata_incoming_links_key": {"_input_type": "StrInput", "advanced": true, "display_name": "Metadata incoming links key", "dynamic": false, "info": "Metadata key used for incoming links.", "list": false, "load_from_db": false, "name": "metadata_incoming_links_key", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "metadata_indexing_exclude": {"_input_type": "StrInput", "advanced": true, "display_name": "Metadata Indexing Exclude", "dynamic": false, "info": "Optional list of metadata fields to exclude from the indexing.", "list": true, "load_from_db": false, "name": "metadata_indexing_exclude", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": [""]}, "metadata_indexing_include": {"_input_type": "StrInput", "advanced": true, "display_name": "Metadata Indexing Include", "dynamic": false, "info": "Optional list of metadata fields to include in the indexing.", "list": true, "load_from_db": false, "name": "metadata_indexing_include", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "metric": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "display_name": "Metric", "dynamic": false, "info": "Optional distance metric for vector comparisons in the vector store.", "name": "metric", "options": ["cosine", "dot_product", "euclidean"], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "cosine"}, "number_of_results": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Results", "dynamic": false, "info": "Number of results to return.", "list": false, "load_from_db": false, "name": "number_of_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 10}, "pre_delete_collection": {"_input_type": "BoolInput", "advanced": true, "display_name": "Pre Delete Collection", "dynamic": false, "info": "Boolean flag to determine whether to delete the collection before creating a new one.", "list": false, "name": "pre_delete_collection", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "search_filter": {"_input_type": "DictInput", "advanced": true, "display_name": "Search Metadata Filter", "dynamic": false, "info": "Optional dictionary of filters to apply to the search query.", "list": true, "name": "search_filter", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "search_query": {"_input_type": "MultilineInput", "advanced": false, "display_name": "搜索查询", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "search_query", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "search_score_threshold": {"_input_type": "FloatInput", "advanced": true, "display_name": "Search Score Threshold", "dynamic": false, "info": "Minimum similarity score threshold for search results. (when using 'Similarity with score threshold')", "list": false, "load_from_db": false, "name": "search_score_threshold", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": -2}, "search_type": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "display_name": "Search Type", "dynamic": false, "info": "Search type to use", "name": "search_type", "options": ["Similarity", "Similarity with score threshold", "MMR (Max Marginal Relevance)", "Graph Traversal", "MMR (Max Marginal Relevance) Graph Traversal"], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "MMR (Max Marginal Relevance) Graph Traversal"}, "setup_mode": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "display_name": "Setup Mode", "dynamic": false, "info": "Configuration mode for setting up the vector store, with options like 'Sync', or 'Off'.", "name": "setup_mode", "options": ["Sync", "Off"], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Sync"}, "should_cache_vector_store": {"_input_type": "BoolInput", "advanced": true, "display_name": "缓存向量存储", "dynamic": false, "info": "如果为 True，则向量存储将在组件的当前构建中被缓存。这对于具有多个输出方法并希望共享相同向量存储的组件非常有用。", "list": false, "list_add_label": "Add More", "name": "should_cache_vector_store", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "token": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Astra DB Application Token", "dynamic": false, "info": "Authentication token for accessing Astra DB.", "input_types": ["Message"], "load_from_db": true, "name": "token", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_APPLICATION_TOKEN"}}, "tool_mode": false}, "type": "AstraDBGraph"}, "dragging": false, "id": "AstraDBGraph-WdeLY", "measured": {"height": 634, "width": 320}, "position": {"x": -1277.8700822899038, "y": 7080.542929356194}, "selected": false, "type": "genericNode"}, {"data": {"description": "Convert Data into plain text following a specified template.", "display_name": "Parse Data", "id": "ParseData-s05ib", "node": {"base_classes": ["Data", "Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Convert Data into plain text following a specified template.", "display_name": "Parse Data", "documentation": "", "edited": false, "field_order": ["data", "template", "sep"], "frozen": false, "icon": "message-square", "legacy": false, "lf_version": "1.1.1", "metadata": {"legacy_name": "解析数据"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "parse_data", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据列表", "method": "parse_data_as_list", "name": "data_list", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"数据转消息\"  # \"Data to Message\"\n    description = \"使用输入数据中的任意 {字段} 将 Data 对象转换为消息。\"  # \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n    metadata = {\n        \"legacy_name\": \"解析数据\",  # \"Parse Data\"\n    }\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"数据\",  # \"Data\"\n            info=\"要转换为文本的数据。\",  # \"The data to convert to text.\"\n            is_list=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"模板\",  # \"Template\"\n            info=(\n                \"用于格式化数据的模板。\"  # \"The template to use for formatting the data. \"\n\"它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。\"  # \"It can contain the keys {text}, {data} or any other key in the Data.\"\n            ),\n            value=\"{文本}\",\n            required=True,\n        ),\n        StrInput(\nname=\"sep\",\ndisplay_name=\"分隔符\",  # \"Separator\"\nadvanced=True,\nvalue=\"\\n\",\n),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"text\",\n            info=\"数据作为单个消息，每个输入数据由分隔符分隔。\",  # \"Data as a single Message, with each input Data separated by Separator.\"\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"数据列表\",  # \"Data List\"\n            name=\"data_list\",\n            info=\"数据作为新数据的列表，每个数据的 `text` 由模板格式化。\",  # \"Data as a list of new Data, each having `text` formatted by Template.\"\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n"}, "data": {"_input_type": "DataInput", "advanced": false, "display_name": "数据", "dynamic": false, "info": "要转换为文本的数据。", "input_types": ["Data"], "list": true, "name": "data", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "sep": {"_input_type": "StrInput", "advanced": true, "display_name": "分隔符", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n"}, "template": {"_input_type": "MultilineInput", "advanced": false, "display_name": "模板", "dynamic": false, "info": "用于格式化数据的模板。它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{data}"}}, "tool_mode": false}, "type": "ParseData"}, "dragging": false, "id": "ParseData-s05ib", "measured": {"height": 342, "width": 320}, "position": {"x": -711.6072978271402, "y": 7008.616446963303}, "selected": false, "type": "genericNode"}, {"data": {"id": "Prompt-KZb70", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": ["context", "question"]}, "description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "documentation": "", "edited": false, "field_order": ["template", "tool_placeholder"], "frozen": false, "icon": "prompts", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "提示消息", "method": "build_prompt", "name": "prompt", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"提示模板\"  # \"Prompt\"\n    description: str = \"创建带有动态变量的提示模板。\"  # \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"  # \"\"\n    trace_type = \"prompt\"  # \"prompt\"\n    name = \"Prompt\"  # \"\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"模板\"),  # \"Template\"\n        MessageTextInput(\n            name=\"tool_placeholder\",  # \"tool_placeholder\"\n            display_name=\"工具占位符\",  # \"Tool Placeholder\"\n            tool_mode=True,\n            advanced=True,\n            info=\"工具模式的占位符输入。\",  # \"A placeholder input for tool mode.\"\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"提示消息\", name=\"prompt\", method=\"build_prompt\"),  # \"Prompt Message\"\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "context": {"advanced": false, "display_name": "context", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "context", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "question": {"advanced": false, "display_name": "question", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "question", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "template": {"_input_type": "PromptInput", "advanced": false, "display_name": "模板", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "prompt", "value": "{context}\n\n---\n\nGiven the context above, answer the question as best as possible. If there isn't information on the context about the question, respond by saying so in a funny way\n\nQuestion: {question}\n\nAnswer: "}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "工具占位符", "dynamic": false, "info": "工具模式的占位符输入。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "Prompt"}, "dragging": false, "id": "Prompt-KZb70", "measured": {"height": 421, "width": 320}, "position": {"x": -241.95389238895368, "y": 7132.715577428476}, "selected": false, "type": "genericNode"}, {"data": {"id": "OpenAIModel-hhsjV", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Generates text using OpenAI LLMs.", "display_name": "OpenAI", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed"], "frozen": false, "icon": "OpenAI", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "语言模型", "method": "build_model", "name": "model_output", "required_inputs": ["api_key", "model_name"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "用于 OpenAI 模型的 OpenAI API 密钥。", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput\n\n# 替换硬编码的字符串为中文，并保留英文注释\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"  # OpenAI\n    description = \"使用 OpenAI LLMs 生成文本。\"  # Generates text using OpenAI LLMs.\n    icon = \"OpenAI\"  # OpenAI\n    name = \"OpenAIModel\"  # OpenAIModel\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"最大 Token 数\",  # Max Tokens\n            advanced=True,\n            info=\"生成的最大 token 数。设置为 0 表示无限制。\"  # The maximum number of tokens to generate. Set to 0 for unlimited tokens.\n            ,\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # Model Kwargs\n            advanced=True,\n            info=\"传递给模型的其他关键字参数。\"  # Additional keyword arguments to pass to the model.\n            ,\n        ),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON 模式\",  # JSON Mode\n            advanced=True,\n            info=\"如果为 True，则无论是否传递 schema，都会输出 JSON。\"  # If True, it will output JSON regardless of passing a schema.\n            ,\n        ),\n        StrInput(\n            name=\"model_name\",\n            display_name=\"模型名称\",  # Model Name\n            advanced=False,\n            info=\"所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、\",\n            required=True\n        ),\n        # DropdownInput(\n        #     name=\"model_name\",\n        #     display_name=\"模型名称\",  # Model Name\n        #     advanced=False,\n        #     options=OPENAI_MODEL_NAMES,\n        #     value=OPENAI_MODEL_NAMES[1],\n        #     combobox=True,\n        # ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础地址\",  # OpenAI API Base\n            advanced=True,\n            info=\"OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。\"\n            \"您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。\"  # The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.\n            ,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API 密钥\",  # OpenAI API Key\n            info=\"用于 OpenAI 模型的 OpenAI API 密钥。\"  # The OpenAI API Key to use for the OpenAI model.\n            ,\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"温度\",  # Temperature\n            value=0.1,\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n        IntInput(\n            name=\"seed\",\n            display_name=\"随机种子\",  # Seed\n            info=\"随机种子控制任务的可重复性。\"  # The seed controls the reproducibility of the job.\n            ,\n            advanced=True,\n            value=1,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # Max Retries\n            info=\"生成时的最大重试次数。\"  # The maximum number of retries to make when generating.\n            ,\n            advanced=True,\n            value=5,\n        ),\n        IntInput(\n            name=\"timeout\",\n            display_name=\"超时时间\",  # Timeout\n            info=\"请求 OpenAI 完成 API 的超时时间。\"  # The timeout for requests to OpenAI completion API.\n            ,\n            advanced=True,\n            value=700,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        openai_api_key = self.api_key\n        temperature = self.temperature\n        model_name: str = self.model_name\n        max_tokens = self.max_tokens\n        model_kwargs = self.model_kwargs or {}\n        openai_api_base = self.openai_api_base or \"https://api.openai.com/v1\"\n        json_mode = self.json_mode\n        seed = self.seed\n        max_retries = self.max_retries\n        timeout = self.timeout\n\n        api_key = SecretStr(openai_api_key).get_secret_value() if openai_api_key else None\n        output = ChatOpenAI(\n            max_tokens=max_tokens or None,\n            model_kwargs=model_kwargs,\n            model=model_name,\n            base_url=openai_api_base,\n            api_key=api_key,\n            temperature=temperature if temperature is not None else 0.1,\n            seed=seed,\n            max_retries=max_retries,\n            request_timeout=timeout,\n        )\n        if json_mode:\n            output = output.bind(response_format={\"type\": \"json_object\"})\n\n        return output\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"Get a message from an OpenAI exception.\n\n        Args:\n            e (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")\n            if message:\n                return message\n        return None\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON 模式", "dynamic": false, "info": "如果为 True，则无论是否传递 schema，都会输出 JSON。", "list": false, "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "生成时的最大重试次数。", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "最大 Token 数", "dynamic": false, "info": "生成的最大 token 数。设置为 0 表示无限制。", "list": false, "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "传递给模型的其他关键字参数。", "list": false, "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "模型名称", "dynamic": false, "info": "所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API 基础地址", "dynamic": false, "info": "OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。", "list": false, "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "str", "value": ""}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "随机种子", "dynamic": false, "info": "随机种子控制任务的可重复性。", "list": false, "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "流式输出", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "系统消息", "dynamic": false, "info": "传递给模型的系统消息。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "温度", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "type": "slider", "value": 0.1}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "超时时间", "dynamic": false, "info": "请求 OpenAI 完成 API 的超时时间。", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}}, "tool_mode": false}, "type": "OpenAIModel"}, "dragging": false, "id": "OpenAIModel-hhsjV", "measured": {"height": 656, "width": 320}, "position": {"x": 317.8221390146513, "y": 7078.366528905622}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatOutput-yHZfU", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "基本清理数据", "dynamic": false, "info": "是否清理数据。", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"聊天输出\"  # \"Chat Output\"\n    description = \"在练习场中显示聊天消息。\"  # \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatOutput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输出传递的消息。\",  # \"Message to be passed as output.\"\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",  # \"data_template\"\n            display_name=\"数据模板\",  # \"Data Template\"\n            value=\"{text}\",\n            advanced=True,\n            info=\"用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。\",  # \"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\"\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",  # \"clean_data\"\n            display_name=\"基本清理数据\",  # \"Basic Clean Data\"\n            value=True,\n            info=\"是否清理数据。\",  # \"Whether to clean the data.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "数据模板", "dynamic": false, "info": "用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输出传递的消息。", "input_types": ["Data", "DataFrame", "Message"], "list": false, "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "ChatOutput"}, "dragging": false, "id": "ChatOutput-yHZfU", "measured": {"height": 230, "width": 320}, "position": {"x": 804.9181649370599, "y": 7243.316205548675}, "selected": false, "type": "genericNode"}, {"data": {"id": "URL-eBpyO", "node": {"base_classes": ["Data", "Message"], "beta": false, "category": "data", "conditional_paths": [], "custom_fields": {}, "description": "Fetch content from one or more URLs.", "display_name": "URL", "documentation": "", "edited": false, "field_order": ["urls", "format"], "frozen": false, "icon": "layout-template", "key": "URL", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "Data", "method": "fetch_content", "name": "data", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Text", "method": "fetch_content_text", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "DataFrame", "method": "as_dataframe", "name": "dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "score": 2.220446049250313e-16, "template": {"_type": "Component", "clean_extra_whitespace": {"_input_type": "BoolInput", "advanced": false, "display_name": "Clean Extra Whitespace", "dynamic": false, "info": "Whether to clean excessive blank lines in the text output. Only applies to 'Text' format.", "list": false, "list_add_label": "Add More", "name": "clean_extra_whitespace", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import asyncio\nimport json\nimport re\n\nimport aiohttp\nfrom langchain_community.document_loaders import AsyncHtmlLoader, WebBaseLoader\n\nfrom langflow.custom import Component\nfrom langflow.io import BoolInput, DropdownInput, MessageTextInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.message import Message\n\n\nclass URLComponent(Component):\n    display_name = \"URL\"\n    description = (\n        \"Load and retrieve data from specified URLs. Supports output in plain text, raw HTML, \"\n        \"or JSON, with options for cleaning and separating multiple outputs.\"\n    )\n    icon = \"layout-template\"\n    name = \"URL\"\n\n    inputs = [\n        MessageTextInput(\n            name=\"urls\",\n            display_name=\"URLs\",\n            is_list=True,\n            tool_mode=True,\n            placeholder=\"Enter a URL...\",\n            list_add_label=\"Add URL\",\n        ),\n        DropdownInput(\n            name=\"format\",\n            display_name=\"Output Format\",\n            info=(\n                \"Output Format. Use 'Text' to extract text from the HTML, 'Raw HTML' for the raw HTML \"\n                \"content, or 'JSON' to extract JSON from the HTML.\"\n            ),\n            options=[\"Text\", \"Raw HTML\", \"JSON\"],\n            value=\"Text\",\n            real_time_refresh=True,\n        ),\n        StrInput(\n            name=\"separator\",\n            display_name=\"Separator\",\n            value=\"\\n\\n\",\n            show=True,\n            info=(\n                \"Specify the separator to use between multiple outputs. Default for Text is '\\\\n\\\\n'. \"\n                \"Default for Raw HTML is '\\\\n<!-- Separator -->\\\\n'.\"\n            ),\n        ),\n        BoolInput(\n            name=\"clean_extra_whitespace\",\n            display_name=\"Clean Extra Whitespace\",\n            value=True,\n            show=True,\n            info=\"Whether to clean excessive blank lines in the text output. Only applies to 'Text' format.\",\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"Data\", name=\"data\", method=\"fetch_content\"),\n        Output(display_name=\"Text\", name=\"text\", method=\"fetch_content_text\"),\n        Output(display_name=\"DataFrame\", name=\"dataframe\", method=\"as_dataframe\"),\n    ]\n\n    async def validate_json_content(self, url: str) -> bool:\n        \"\"\"Validates if the URL content is actually JSON.\"\"\"\n        try:\n            async with aiohttp.ClientSession() as session, session.get(url) as response:\n                http_ok = 200\n                if response.status != http_ok:\n                    return False\n\n                content = await response.text()\n                try:\n                    json.loads(content)\n                except json.JSONDecodeError:\n                    return False\n                else:\n                    return True\n        except (aiohttp.ClientError, asyncio.TimeoutError):\n            # Log specific error for debugging if needed\n            return False\n\n    def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:\n        \"\"\"Dynamically update fields based on selected format.\"\"\"\n        if field_name == \"format\":\n            is_text_mode = field_value == \"Text\"\n            is_json_mode = field_value == \"JSON\"\n            build_config[\"separator\"][\"value\"] = \"\\n\\n\" if is_text_mode else \"\\n<!-- Separator -->\\n\"\n            build_config[\"clean_extra_whitespace\"][\"show\"] = is_text_mode\n            build_config[\"separator\"][\"show\"] = not is_json_mode\n        return build_config\n\n    def ensure_url(self, string: str) -> str:\n        \"\"\"Ensures the given string is a valid URL.\"\"\"\n        if not string.startswith((\"http://\", \"https://\")):\n            string = \"http://\" + string\n\n        url_regex = re.compile(\n            r\"^(https?:\\/\\/)?\"\n            r\"(www\\.)?\"\n            r\"([a-zA-Z0-9.-]+)\"\n            r\"(\\.[a-zA-Z]{2,})?\"\n            r\"(:\\d+)?\"\n            r\"(\\/[^\\s]*)?$\",\n            re.IGNORECASE,\n        )\n\n        error_msg = \"Invalid URL - \" + string\n        if not url_regex.match(string):\n            raise ValueError(error_msg)\n\n        return string\n\n    def fetch_content(self) -> list[Data]:\n        \"\"\"Fetch content based on selected format.\"\"\"\n        urls = list({self.ensure_url(url.strip()) for url in self.urls if url.strip()})\n\n        no_urls_msg = \"No valid URLs provided.\"\n        if not urls:\n            raise ValueError(no_urls_msg)\n\n        # If JSON format is selected, validate JSON content first\n        if self.format == \"JSON\":\n            for url in urls:\n                is_json = asyncio.run(self.validate_json_content(url))\n                if not is_json:\n                    error_msg = \"Invalid JSON content from URL - \" + url\n                    raise ValueError(error_msg)\n\n        if self.format == \"Raw HTML\":\n            loader = AsyncHtmlLoader(web_path=urls, encoding=\"utf-8\")\n        else:\n            loader = WebBaseLoader(web_paths=urls, encoding=\"utf-8\")\n\n        docs = loader.load()\n\n        if self.format == \"JSON\":\n            data = []\n            for doc in docs:\n                try:\n                    json_content = json.loads(doc.page_content)\n                    data_dict = {\"text\": json.dumps(json_content, indent=2), **json_content, **doc.metadata}\n                    data.append(Data(**data_dict))\n                except json.JSONDecodeError as err:\n                    source = doc.metadata.get(\"source\", \"unknown URL\")\n                    error_msg = \"Invalid JSON content from \" + source\n                    raise ValueError(error_msg) from err\n            return data\n\n        return [Data(text=doc.page_content, **doc.metadata) for doc in docs]\n\n    def fetch_content_text(self) -> Message:\n        \"\"\"Fetch content and return as formatted text.\"\"\"\n        data = self.fetch_content()\n\n        if self.format == \"JSON\":\n            text_list = [item.text for item in data]\n            result = \"\\n\".join(text_list)\n        else:\n            text_list = [item.text for item in data]\n            if self.format == \"Text\" and self.clean_extra_whitespace:\n                text_list = [re.sub(r\"\\n{3,}\", \"\\n\\n\", text) for text in text_list]\n            result = self.separator.join(text_list)\n\n        self.status = result\n        return Message(text=result)\n\n    def as_dataframe(self) -> DataFrame:\n        \"\"\"Return fetched content as a DataFrame.\"\"\"\n        return DataFrame(self.fetch_content())\n"}, "format": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "display_name": "Output Format", "dynamic": false, "info": "Output Format. Use 'Text' to extract text from the HTML, 'Raw HTML' for the raw HTML content, or 'JSON' to extract JSON from the HTML.", "name": "format", "options": ["Text", "Raw HTML", "JSON"], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Raw HTML"}, "separator": {"_input_type": "StrInput", "advanced": false, "display_name": "Separator", "dynamic": false, "info": "Specify the separator to use between multiple outputs. Default for Text is '\\n\\n'. Default for Raw HTML is '\\n<!-- Separator -->\\n'.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "separator", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n\n"}, "urls": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "URLs", "dynamic": false, "info": "", "input_types": ["Message"], "list": true, "load_from_db": false, "name": "urls", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ["https://learnyouahaskell.com/introduction", "https://learnyouahaskell.com/starting-out", "https://learnyouahaskell.com/types-and-typeclasses", "https://learnyouahaskell.com/syntax-in-functions", "https://learnyouahaskell.com/recursion", "https://learnyouahaskell.com/higher-order-functions", "https://learnyouahaskell.com/modules", "https://learnyouahaskell.com/making-our-own-types-and-typeclasses", "https://learnyouahaskell.com/input-and-output", "https://learnyouahaskell.com/functionally-solving-problems", "https://learnyouahaskell.com/functors-applicative-functors-and-monoids", "https://learnyouahaskell.com/a-fistful-of-monads", "https://learnyouahaskell.com/for-a-few-monads-more", "https://learnyouahaskell.com/zippers"]}}, "tool_mode": false}, "showNode": true, "type": "URL"}, "dragging": false, "id": "URL-eBpyO", "measured": {"height": 1191, "width": 320}, "position": {"x": -1785.9330435348252, "y": 5716.282891868678}, "selected": false, "type": "genericNode"}, {"data": {"id": "LanguageRecursiveTextSplitter-TG6vC", "node": {"base_classes": ["Data"], "beta": false, "category": "langchain_utilities", "conditional_paths": [], "custom_fields": {}, "description": "Split text into chunks of a specified length based on language.", "display_name": "Language Recursive Text Splitter", "documentation": "https://docs.langflow.org/components/text-splitters#languagerecursivetextsplitter", "edited": false, "field_order": ["chunk_size", "chunk_overlap", "data_input", "code_language"], "frozen": false, "icon": "<PERSON><PERSON><PERSON><PERSON>", "key": "LanguageRecursiveTextSplitter", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "数据", "method": "transform_data", "name": "data", "required_inputs": [], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.12416523075924112, "template": {"_type": "Component", "chunk_overlap": {"_input_type": "IntInput", "advanced": false, "display_name": "块重叠", "dynamic": false, "info": "块之间的重叠量。", "list": false, "name": "chunk_overlap", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 200}, "chunk_size": {"_input_type": "IntInput", "advanced": false, "display_name": "块大小", "dynamic": false, "info": "每个块的最大长度。", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nfrom langchain_text_splitters import Language, RecursiveCharacterTextSplitter, TextSplitter\n\nfrom langflow.base.textsplitters.model import LCTextSplitterComponent\nfrom langflow.inputs import DataInput, DropdownInput, IntInput\n\n\nclass LanguageRecursiveTextSplitterComponent(LCTextSplitterComponent):\n    display_name: str = \"语言递归文本分割器\"    # \"Language Recursive Text Splitter\"\n    description: str = \"根据语言将文本拆分为指定长度的块。\"  # \"Split text into chunks of a specified length based on language.\"\n    documentation: str = \"https://docs.langflow.org/components/text-splitters#languagerecursivetextsplitter\"\n    name = \"LanguageRecursiveTextSplitter\"\n    icon = \"LangChain\"\n\n    inputs = [\n        IntInput(\n            name=\"chunk_size\",\n            display_name=\"块大小\",  # \"Chunk Size\"\n            info=\"每个块的最大长度。\",  # \"The maximum length of each chunk.\"\n            value=1000,\n        ),\n        IntInput(\n            name=\"chunk_overlap\",\n            display_name=\"块重叠\",  # \"Chunk Overlap\"\n            info=\"块之间的重叠量。\",  # \"The amount of overlap between chunks.\"\n            value=200,\n        ),\n        DataInput(\n            name=\"data_input\",\n            display_name=\"输入\",  # \"Input\"\n            info=\"要拆分的文本。\",  # \"The texts to split.\"\n            input_types=[\"Document\", \"Data\"],\n            required=True,\n        ),\n        DropdownInput(\n            name=\"code_language\",\n            display_name=\"代码语言\",  # \"Code Language\"\n            options=[x.value for x in Language],\n            value=\"python\",\n        ),\n    ]\n\n    def get_data_input(self) -> Any:\n        return self.data_input\n\n    def build_text_splitter(self) -> TextSplitter:\n        return RecursiveCharacterTextSplitter.from_language(\n            language=Language(self.code_language),\n            chunk_size=self.chunk_size,\n            chunk_overlap=self.chunk_overlap,\n        )\n"}, "code_language": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "代码语言", "dynamic": false, "info": "", "name": "code_language", "options": ["cpp", "go", "java", "kotlin", "js", "ts", "php", "proto", "python", "rst", "ruby", "rust", "scala", "swift", "markdown", "latex", "html", "sol", "csharp", "cobol", "c", "lua", "perl", "haskell", "elixir", "powershell"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "python"}, "data_input": {"_input_type": "DataInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "要拆分的文本。", "input_types": ["Document", "Data"], "list": false, "name": "data_input", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "LanguageRecursiveTextSplitter"}, "dragging": false, "id": "LanguageRecursiveTextSplitter-TG6vC", "measured": {"height": 459, "width": 320}, "position": {"x": -1312.6794416503267, "y": 5995.335519138236}, "selected": false, "type": "genericNode"}, {"data": {"id": "HtmlLinkExtractor-I8rzh", "node": {"base_classes": ["Data"], "beta": false, "category": "langchain_utilities", "conditional_paths": [], "custom_fields": {}, "description": "Extract hyperlinks from HTML content.", "display_name": "HTML Link Extractor", "documentation": "https://python.langchain.com/v0.2/api_reference/community/graph_vectorstores/langchain_community.graph_vectorstores.extractors.html_link_extractor.HtmlLinkExtractor.html", "edited": false, "field_order": ["kind", "drop_fragments", "data_input"], "frozen": false, "icon": "<PERSON><PERSON><PERSON><PERSON>", "key": "HtmlLinkExtractor", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "数据", "method": "transform_data", "name": "data", "required_inputs": [], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.01857804455091699, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nfrom langchain_community.graph_vectorstores.extractors import HtmlLinkExtractor, LinkExtractorTransformer\nfrom langchain_core.documents import BaseDocumentTransformer\n\nfrom langflow.base.document_transformers.model import LCDocumentTransformerComponent\nfrom langflow.inputs import BoolInput, DataInput, StrInput\n\n\nclass HtmlLinkExtractorComponent(LCDocumentTransformerComponent):\n    display_name = \"HTML链接提取器\"  # \"HTML Link Extractor\"\n    description = \"从 HTML 内容中提取超链接。\"  # \"Extract hyperlinks from HTML content.\"\n    documentation = \"https://python.langchain.com/v0.2/api_reference/community/graph_vectorstores/langchain_community.graph_vectorstores.extractors.html_link_extractor.HtmlLinkExtractor.html\"\n    name = \"HtmlLinkExtractor\"\n    icon = \"LangChain\"\n\n    inputs = [\n        StrInput(\n            name=\"kind\",\n            display_name=\"边的类型\",  # \"Kind of edge\"\n            value=\"hyperlink\",\n            required=False,\n        ),\n        BoolInput(\n            name=\"drop_fragments\",\n            display_name=\"移除 URL 片段\",  # \"Drop URL fragments\"\n            value=True,\n            required=False,\n        ),\n        DataInput(\n            name=\"data_input\",\n            display_name=\"输入\",  # \"Input\"\n            info=\"要从中提取链接的文本。\",  # \"The texts from which to extract links.\"\n            input_types=[\"Document\", \"Data\"],\n            required=True,\n        ),\n    ]\n\n    def get_data_input(self) -> Any:\n        return self.data_input\n\n    def build_document_transformer(self) -> BaseDocumentTransformer:\n        return LinkExtractorTransformer(\n            [HtmlLinkExtractor(kind=self.kind, drop_fragments=self.drop_fragments).as_document_extractor()]\n        )\n"}, "data_input": {"_input_type": "DataInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "要从中提取链接的文本。", "input_types": ["Document", "Data"], "list": false, "name": "data_input", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "drop_fragments": {"_input_type": "BoolInput", "advanced": false, "display_name": "移除 URL 片段", "dynamic": false, "info": "", "list": false, "name": "drop_fragments", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "kind": {"_input_type": "StrInput", "advanced": false, "display_name": "边的类型", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "kind", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "hyperlink"}}, "tool_mode": false}, "showNode": true, "type": "HtmlLinkExtractor"}, "dragging": false, "id": "HtmlLinkExtractor-I8rzh", "measured": {"height": 317, "width": 320}, "position": {"x": -819.2825457919793, "y": 6116.365935466731}, "selected": false, "type": "genericNode"}, {"data": {"id": "OpenAIEmbeddings-RD7F8", "node": {"base_classes": ["Embeddings"], "beta": false, "category": "embeddings", "conditional_paths": [], "custom_fields": {}, "description": "Generate embeddings using OpenAI models.", "display_name": "OpenAI Embeddings", "documentation": "", "edited": false, "field_order": ["default_headers", "default_query", "chunk_size", "client", "deployment", "embedding_ctx_length", "max_retries", "model", "model_kwargs", "openai_api_key", "openai_api_base", "openai_api_type", "openai_api_version", "openai_organization", "openai_proxy", "request_timeout", "show_progress_bar", "skip_empty", "tiktoken_model_name", "tiktoken_enable", "dimensions"], "frozen": false, "icon": "OpenAI", "key": "OpenAIEmbeddings", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "嵌入向量", "method": "build_embeddings", "name": "embeddings", "required_inputs": ["openai_api_key"], "selected": "Embeddings", "tool_mode": true, "types": ["Embeddings"], "value": "__UNDEFINED__"}], "pinned": false, "score": 5.2003277518821525e-05, "template": {"_type": "Component", "chunk_size": {"_input_type": "IntInput", "advanced": true, "display_name": "分块大小", "dynamic": false, "info": "", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "client": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "客户端", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "client", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import OpenAIEmbeddings\n\nfrom langflow.base.embeddings.model import LCEmbeddingsModel\nfrom langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES\nfrom langflow.field_typing import Embeddings\nfrom langflow.io import BoolInput, DictInput, DropdownInput, FloatInput, IntInput, MessageTextInput, SecretStrInput\n\n\nclass OpenAIEmbeddingsComponent(LCEmbeddingsModel):\n    display_name = \"OpenAI嵌入\"  # \"OpenAI Embeddings\"\n    description = \"使用 OpenAI 模型生成嵌入。\"  # \"Generate embeddings using OpenAI models.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIEmbeddings\"\n\n    inputs = [\n        DictInput(\n            name=\"default_headers\",\n            display_name=\"默认请求头\",  # \"Default Headers\"\n            advanced=True,\n            info=\"用于 API 请求的默认请求头。\",  # \"Default headers to use for the API request.\"\n        ),\n        DictInput(\n            name=\"default_query\",\n            display_name=\"默认查询参数\",  # \"Default Query\"\n            advanced=True,\n            info=\"用于 API 请求的默认查询参数。\",  # \"Default query parameters to use for the API request.\"\n        ),\n        IntInput(\n            name=\"chunk_size\",\n            display_name=\"分块大小\",  # \"Chunk Size\"\n            advanced=True,\n            value=1000,\n        ),\n        MessageTextInput(\n            name=\"client\",\n            display_name=\"客户端\",  # \"Client\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"deployment\",\n            display_name=\"部署\",  # \"Deployment\"\n            advanced=True,\n        ),\n        IntInput(\n            name=\"embedding_ctx_length\",\n            display_name=\"嵌入上下文长度\",  # \"Embedding Context Length\"\n            advanced=True,\n            value=1536,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # \"Max Retries\"\n            value=3,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"模型\",  # \"Model\"\n            advanced=False,\n            options=OPENAI_EMBEDDING_MODEL_NAMES,\n            value=\"text-embedding-3-small\",\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # \"Model Kwargs\"\n            advanced=True,\n        ),\n        SecretStrInput(\n            name=\"openai_api_key\",\n            display_name=\"OpenAI API 密钥\",  # \"OpenAI API Key\"\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础 URL\",  # \"OpenAI API Base\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_type\",\n            display_name=\"OpenAI API 类型\",  # \"OpenAI API Type\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_version\",\n            display_name=\"OpenAI API 版本\",  # \"OpenAI API Version\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_organization\",\n            display_name=\"OpenAI 组织\",  # \"OpenAI Organization\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_proxy\",\n            display_name=\"OpenAI 代理\",  # \"OpenAI Proxy\"\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"request_timeout\",\n            display_name=\"请求超时时间\",  # \"Request Timeout\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"show_progress_bar\",\n            display_name=\"显示进度条\",  # \"Show Progress Bar\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"skip_empty\",\n            display_name=\"跳过空值\",  # \"Skip Empty\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"tiktoken_model_name\",\n            display_name=\"TikToken 模型名称\",  # \"TikToken Model Name\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"tiktoken_enable\",\n            display_name=\"启用 TikToken\",  # \"TikToken Enable\"\n            advanced=True,\n            value=True,\n            info=\"如果为 False，则必须安装 transformers。\",  # \"If False, you must have transformers installed.\"\n        ),\n        IntInput(\n            name=\"dimensions\",\n            display_name=\"维度\",  # \"Dimensions\"\n            info=\"生成的嵌入输出的维度数量，仅某些模型支持。\",  # \"The number of dimensions the resulting output embeddings should have. Only supported by certain models.\"\n            advanced=True,\n        ),\n    ]\n\n    def build_embeddings(self) -> Embeddings:\n        return OpenAIEmbeddings(\n            client=self.client or None,\n            model=self.model,\n            dimensions=self.dimensions or None,\n            deployment=self.deployment or None,\n            api_version=self.openai_api_version or None,\n            base_url=self.openai_api_base or None,\n            openai_api_type=self.openai_api_type or None,\n            openai_proxy=self.openai_proxy or None,\n            embedding_ctx_length=self.embedding_ctx_length,\n            api_key=self.openai_api_key or None,\n            organization=self.openai_organization or None,\n            allowed_special=\"all\",\n            disallowed_special=\"all\",\n            chunk_size=self.chunk_size,\n            max_retries=self.max_retries,\n            timeout=self.request_timeout or None,\n            tiktoken_enabled=self.tiktoken_enable,\n            tiktoken_model_name=self.tiktoken_model_name or None,\n            show_progress_bar=self.show_progress_bar,\n            model_kwargs=self.model_kwargs,\n            skip_empty=self.skip_empty,\n            default_headers=self.default_headers or None,\n            default_query=self.default_query or None,\n        )\n"}, "default_headers": {"_input_type": "DictInput", "advanced": true, "display_name": "默认请求头", "dynamic": false, "info": "用于 API 请求的默认请求头。", "list": false, "name": "default_headers", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "default_query": {"_input_type": "DictInput", "advanced": true, "display_name": "默认查询参数", "dynamic": false, "info": "用于 API 请求的默认查询参数。", "list": false, "name": "default_query", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "deployment": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "部署", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "deployment", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "dimensions": {"_input_type": "IntInput", "advanced": true, "display_name": "维度", "dynamic": false, "info": "生成的嵌入输出的维度数量，仅某些模型支持。", "list": false, "name": "dimensions", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "embedding_ctx_length": {"_input_type": "IntInput", "advanced": true, "display_name": "嵌入上下文长度", "dynamic": false, "info": "", "list": false, "name": "embedding_ctx_length", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1536}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "", "list": false, "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 3}, "model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "模型", "dynamic": false, "info": "", "name": "model", "options": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text-embedding-3-small"}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "", "list": false, "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "openai_api_base": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 基础 URL", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "", "input_types": ["Message"], "load_from_db": true, "name": "openai_api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "openai_api_type": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 类型", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_type", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_version": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 版本", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_version", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_organization": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 组织", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_organization", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_proxy": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 代理", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_proxy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "request_timeout": {"_input_type": "FloatInput", "advanced": true, "display_name": "请求超时时间", "dynamic": false, "info": "", "list": false, "name": "request_timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": ""}, "show_progress_bar": {"_input_type": "BoolInput", "advanced": true, "display_name": "显示进度条", "dynamic": false, "info": "", "list": false, "name": "show_progress_bar", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "skip_empty": {"_input_type": "BoolInput", "advanced": true, "display_name": "跳过空值", "dynamic": false, "info": "", "list": false, "name": "skip_empty", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "tiktoken_enable": {"_input_type": "BoolInput", "advanced": true, "display_name": "启用 TikToken", "dynamic": false, "info": "如果为 False，则必须安装 transformers。", "list": false, "name": "tiktoken_enable", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "tiktoken_model_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "TikToken 模型名称", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tiktoken_model_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "OpenAIEmbeddings"}, "dragging": false, "id": "OpenAIEmbeddings-RD7F8", "measured": {"height": 312, "width": 320}, "position": {"x": -826.5880014143661, "y": 6515.86676043142}, "selected": false, "type": "genericNode"}, {"data": {"id": "note-Ja6UE", "node": {"description": "## 📖 README\n\nLoad your data into a vector database with the 📚 **Load Data** flow, and then use your data as chat context with the 🐕 **Retriever** flow.\n\n**🚨 Add your OpenAI API key as a global variable to easily add it to all of the OpenAI components in this flow.** \n\n**Quick start**\n1. Run the 📚 **Load Data** flow.\n2. Run the 🐕 **Retriever** flow.\n\n**Next steps** \n\n- Experiment by changing the prompt and the loaded data to see how the bot's responses change. ", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "height": 695, "id": "note-Ja6UE", "measured": {"height": 695, "width": 325}, "position": {"x": -2651.9749287591367, "y": 6278.741596879104}, "resizing": false, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "note-MN3j2", "node": {"description": "## 📚 1. Load Data Flow\n\nRun this first! Load data multiple urls, and embed it into the vector database in a graph based format.\n\nClick ▶️ **Run component** on the **Astra DB Graph** component to load your data.\n\n* If you're using OSS Langflow, add your Astra DB Application Token to the Astra DB component.\n\n#### Next steps:\n Experiment by changing the prompt and the contextual data to see how the retrieval flow's responses change.", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "height": 499, "id": "note-MN3j2", "measured": {"height": 499, "width": 326}, "position": {"x": -2156.877891666127, "y": 5887.194729165318}, "resizing": false, "selected": false, "type": "noteNode", "width": 325}, {"data": {"id": "note-EZgYP", "node": {"description": "## 🐕 2. Retriever Flow\n\nThis flow answers your questions with contextual data retrieved from your vector database. using graph RAG.\n\nOpen the **Playground** and ask, \n\n```\nhow to create a function in Haskell ?\n```\n", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "id": "note-EZgYP", "measured": {"height": 324, "width": 325}, "position": {"x": -2255.4854518934735, "y": 7190.4854518934735}, "selected": false, "type": "noteNode"}, {"data": {"id": "AstraDBGraph-OBH6o", "node": {"base_classes": ["Data", "DataFrame"], "beta": false, "category": "vectorstores", "conditional_paths": [], "custom_fields": {}, "description": "Implementation of Graph Vector Store using Astra DB", "display_name": "Astra DB Graph", "documentation": "", "edited": false, "field_order": ["token", "api_endpoint", "collection_name", "metadata_incoming_links_key", "ingest_data", "search_query", "should_cache_vector_store", "keyspace", "embedding_model", "metric", "batch_size", "bulk_insert_batch_concurrency", "bulk_insert_overwrite_concurrency", "bulk_delete_concurrency", "setup_mode", "pre_delete_collection", "metadata_indexing_include", "metadata_indexing_exclude", "collection_indexing_policy", "number_of_results", "search_type", "search_score_threshold", "search_filter"], "frozen": false, "icon": "AstraDB", "key": "AstraDBGraph", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "搜索结果", "method": "search_documents", "name": "search_results", "required_inputs": ["api_endpoint", "collection_name", "token"], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据框", "method": "as_dataframe", "name": "dataframe", "required_inputs": [], "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.01857804455091699, "template": {"_type": "Component", "api_endpoint": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "API Endpoint", "dynamic": false, "info": "API endpoint URL for the Astra DB service.", "input_types": ["Message"], "load_from_db": true, "name": "api_endpoint", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_API_ENDPOINT"}, "batch_size": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON><PERSON> Si<PERSON>", "dynamic": false, "info": "Optional number of data to process in a single batch.", "list": false, "list_add_label": "Add More", "name": "batch_size", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "bulk_delete_concurrency": {"_input_type": "IntInput", "advanced": true, "display_name": "Bulk Delete Concurrency", "dynamic": false, "info": "Optional concurrency level for bulk delete operations.", "list": false, "list_add_label": "Add More", "name": "bulk_delete_concurrency", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "bulk_insert_batch_concurrency": {"_input_type": "IntInput", "advanced": true, "display_name": "Bulk Insert Batch Concurrency", "dynamic": false, "info": "Optional concurrency level for bulk insert operations.", "list": false, "list_add_label": "Add More", "name": "bulk_insert_batch_concurrency", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "bulk_insert_overwrite_concurrency": {"_input_type": "IntInput", "advanced": true, "display_name": "Bulk Insert Overwrite Concurrency", "dynamic": false, "info": "Optional concurrency level for bulk insert operations that overwrite existing data.", "list": false, "list_add_label": "Add More", "name": "bulk_insert_overwrite_concurrency", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import os\n\nimport orjson\nfrom astrapy.admin import parse_api_endpoint\n\nfrom langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store\nfrom langflow.helpers import docs_to_data\nfrom langflow.inputs import (\n    BoolInput,\n    DictInput,\n    DropdownInput,\n    FloatInput,\n    HandleInput,\n    IntInput,\n    SecretStrInput,\n    StrInput,\n)\nfrom langflow.schema import Data\n\n\nclass AstraDBGraphVectorStoreComponent(LCVectorStoreComponent):\n    display_name: str = \"Astra DB Graph\"\n    description: str = \"Implementation of Graph Vector Store using Astra DB\"\n    name = \"AstraDBGraph\"\n    icon: str = \"AstraDB\"\n\n    inputs = [\n        SecretStrInput(\n            name=\"token\",\n            display_name=\"Astra DB Application Token\",\n            info=\"Authentication token for accessing Astra DB.\",\n            value=\"ASTRA_DB_APPLICATION_TOKEN\",\n            required=True,\n            advanced=os.getenv(\"ASTRA_ENHANCED\", \"false\").lower() == \"true\",\n        ),\n        SecretStrInput(\n            name=\"api_endpoint\",\n            display_name=\"Database\" if os.getenv(\"ASTRA_ENHANCED\", \"false\").lower() == \"true\" else \"API Endpoint\",\n            info=\"API endpoint URL for the Astra DB service.\",\n            value=\"ASTRA_DB_API_ENDPOINT\",\n            required=True,\n        ),\n        StrInput(\n            name=\"collection_name\",\n            display_name=\"Collection Name\",\n            info=\"The name of the collection within Astra DB where the vectors will be stored.\",\n            required=True,\n        ),\n        StrInput(\n            name=\"metadata_incoming_links_key\",\n            display_name=\"Metadata incoming links key\",\n            info=\"Metadata key used for incoming links.\",\n            advanced=True,\n        ),\n        *LCVectorStoreComponent.inputs,\n        StrInput(\n            name=\"keyspace\",\n            display_name=\"Keyspace\",\n            info=\"Optional keyspace within Astra DB to use for the collection.\",\n            advanced=True,\n        ),\n        HandleInput(\n            name=\"embedding_model\",\n            display_name=\"Embedding Model\",\n            input_types=[\"Embeddings\"],\n            info=\"Allows an embedding model configuration.\",\n        ),\n        DropdownInput(\n            name=\"metric\",\n            display_name=\"Metric\",\n            info=\"Optional distance metric for vector comparisons in the vector store.\",\n            options=[\"cosine\", \"dot_product\", \"euclidean\"],\n            value=\"cosine\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"batch_size\",\n            display_name=\"Batch Size\",\n            info=\"Optional number of data to process in a single batch.\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"bulk_insert_batch_concurrency\",\n            display_name=\"Bulk Insert Batch Concurrency\",\n            info=\"Optional concurrency level for bulk insert operations.\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"bulk_insert_overwrite_concurrency\",\n            display_name=\"Bulk Insert Overwrite Concurrency\",\n            info=\"Optional concurrency level for bulk insert operations that overwrite existing data.\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"bulk_delete_concurrency\",\n            display_name=\"Bulk Delete Concurrency\",\n            info=\"Optional concurrency level for bulk delete operations.\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"setup_mode\",\n            display_name=\"Setup Mode\",\n            info=\"Configuration mode for setting up the vector store, with options like 'Sync', or 'Off'.\",\n            options=[\"Sync\", \"Off\"],\n            advanced=True,\n            value=\"Sync\",\n        ),\n        BoolInput(\n            name=\"pre_delete_collection\",\n            display_name=\"Pre Delete Collection\",\n            info=\"Boolean flag to determine whether to delete the collection before creating a new one.\",\n            advanced=True,\n            value=False,\n        ),\n        StrInput(\n            name=\"metadata_indexing_include\",\n            display_name=\"Metadata Indexing Include\",\n            info=\"Optional list of metadata fields to include in the indexing.\",\n            advanced=True,\n            list=True,\n        ),\n        StrInput(\n            name=\"metadata_indexing_exclude\",\n            display_name=\"Metadata Indexing Exclude\",\n            info=\"Optional list of metadata fields to exclude from the indexing.\",\n            advanced=True,\n            list=True,\n        ),\n        StrInput(\n            name=\"collection_indexing_policy\",\n            display_name=\"Collection Indexing Policy\",\n            info='Optional JSON string for the \"indexing\" field of the collection. '\n            \"See https://docs.datastax.com/en/astra-db-serverless/api-reference/collections.html#the-indexing-option\",\n            advanced=True,\n        ),\n        IntInput(\n            name=\"number_of_results\",\n            display_name=\"Number of Results\",\n            info=\"Number of results to return.\",\n            advanced=True,\n            value=4,\n        ),\n        DropdownInput(\n            name=\"search_type\",\n            display_name=\"Search Type\",\n            info=\"Search type to use\",\n            options=[\n                \"Similarity\",\n                \"Similarity with score threshold\",\n                \"MMR (Max Marginal Relevance)\",\n                \"Graph Traversal\",\n                \"MMR (Max Marginal Relevance) Graph Traversal\",\n            ],\n            value=\"MMR (Max Marginal Relevance) Graph Traversal\",\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"search_score_threshold\",\n            display_name=\"Search Score Threshold\",\n            info=\"Minimum similarity score threshold for search results. \"\n            \"(when using 'Similarity with score threshold')\",\n            value=0,\n            advanced=True,\n        ),\n        DictInput(\n            name=\"search_filter\",\n            display_name=\"Search Metadata Filter\",\n            info=\"Optional dictionary of filters to apply to the search query.\",\n            advanced=True,\n            is_list=True,\n        ),\n    ]\n\n    @check_cached_vector_store\n    def build_vector_store(self):\n        try:\n            from langchain_astradb import AstraDBGraphVectorStore\n            from langchain_astradb.utils.astradb import SetupMode\n        except ImportError as e:\n            msg = (\n                \"Could not import langchain Astra DB integration package. \"\n                \"Please install it with `pip install langchain-astradb`.\"\n            )\n            raise ImportError(msg) from e\n\n        try:\n            if not self.setup_mode:\n                self.setup_mode = self._inputs[\"setup_mode\"].options[0]\n\n            setup_mode_value = SetupMode[self.setup_mode.upper()]\n        except KeyError as e:\n            msg = f\"Invalid setup mode: {self.setup_mode}\"\n            raise ValueError(msg) from e\n\n        try:\n            self.log(f\"Initializing Graph Vector Store {self.collection_name}\")\n\n            vector_store = AstraDBGraphVectorStore(\n                embedding=self.embedding_model,\n                collection_name=self.collection_name,\n                metadata_incoming_links_key=self.metadata_incoming_links_key or \"incoming_links\",\n                token=self.token,\n                api_endpoint=self.api_endpoint,\n                namespace=self.keyspace or None,\n                environment=parse_api_endpoint(self.api_endpoint).environment if self.api_endpoint else None,\n                metric=self.metric or None,\n                batch_size=self.batch_size or None,\n                bulk_insert_batch_concurrency=self.bulk_insert_batch_concurrency or None,\n                bulk_insert_overwrite_concurrency=self.bulk_insert_overwrite_concurrency or None,\n                bulk_delete_concurrency=self.bulk_delete_concurrency or None,\n                setup_mode=setup_mode_value,\n                pre_delete_collection=self.pre_delete_collection,\n                metadata_indexing_include=[s for s in self.metadata_indexing_include if s] or None,\n                metadata_indexing_exclude=[s for s in self.metadata_indexing_exclude if s] or None,\n                collection_indexing_policy=orjson.loads(self.collection_indexing_policy.encode(\"utf-8\"))\n                if self.collection_indexing_policy\n                else None,\n            )\n        except Exception as e:\n            msg = f\"Error initializing AstraDBGraphVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        self.log(f\"Vector Store initialized: {vector_store.astra_env.collection_name}\")\n        self._add_documents_to_vector_store(vector_store)\n\n        return vector_store\n\n    def _add_documents_to_vector_store(self, vector_store) -> None:\n        self.ingest_data = self._prepare_ingest_data()\n\n        documents = []\n        for _input in self.ingest_data or []:\n            if isinstance(_input, Data):\n                documents.append(_input.to_lc_document())\n            else:\n                msg = \"Vector Store Inputs must be Data objects.\"\n                raise TypeError(msg)\n\n        if documents:\n            self.log(f\"Adding {len(documents)} documents to the Vector Store.\")\n            try:\n                vector_store.add_documents(documents)\n            except Exception as e:\n                msg = f\"Error adding documents to AstraDBGraphVectorStore: {e}\"\n                raise ValueError(msg) from e\n        else:\n            self.log(\"No documents to add to the Vector Store.\")\n\n    def _map_search_type(self) -> str:\n        match self.search_type:\n            case \"Similarity\":\n                return \"similarity\"\n            case \"Similarity with score threshold\":\n                return \"similarity_score_threshold\"\n            case \"MMR (Max Marginal Relevance)\":\n                return \"mmr\"\n            case \"Graph Traversal\":\n                return \"traversal\"\n            case \"MMR (Max Marginal Relevance) Graph Traversal\":\n                return \"mmr_traversal\"\n            case _:\n                return \"similarity\"\n\n    def _build_search_args(self):\n        args = {\n            \"k\": self.number_of_results,\n            \"score_threshold\": self.search_score_threshold,\n        }\n\n        if self.search_filter:\n            clean_filter = {k: v for k, v in self.search_filter.items() if k and v}\n            if len(clean_filter) > 0:\n                args[\"filter\"] = clean_filter\n        return args\n\n    def search_documents(self, vector_store=None) -> list[Data]:\n        if not vector_store:\n            vector_store = self.build_vector_store()\n\n        self.log(\"Searching for documents in AstraDBGraphVectorStore.\")\n        self.log(f\"Search query: {self.search_query}\")\n        self.log(f\"Search type: {self.search_type}\")\n        self.log(f\"Number of results: {self.number_of_results}\")\n\n        if self.search_query and isinstance(self.search_query, str) and self.search_query.strip():\n            try:\n                search_type = self._map_search_type()\n                search_args = self._build_search_args()\n\n                docs = vector_store.search(query=self.search_query, search_type=search_type, **search_args)\n\n                # Drop links from the metadata. At this point the links don't add any value for building the\n                # context and haven't been restored to json which causes the conversion to fail.\n                self.log(\"Removing links from metadata.\")\n                for doc in docs:\n                    if \"links\" in doc.metadata:\n                        doc.metadata.pop(\"links\")\n\n            except Exception as e:\n                msg = f\"Error performing search in AstraDBGraphVectorStore: {e}\"\n                raise ValueError(msg) from e\n\n            self.log(f\"Retrieved documents: {len(docs)}\")\n\n            data = docs_to_data(docs)\n\n            self.log(f\"Converted documents to data: {len(data)}\")\n\n            self.status = data\n            return data\n        self.log(\"No search input provided. Skipping search.\")\n        return []\n\n    def get_retriever_kwargs(self):\n        search_args = self._build_search_args()\n        return {\n            \"search_type\": self._map_search_type(),\n            \"search_kwargs\": search_args,\n        }\n"}, "collection_indexing_policy": {"_input_type": "StrInput", "advanced": true, "display_name": "Collection Indexing Policy", "dynamic": false, "info": "Optional JSON string for the \"indexing\" field of the collection. See https://docs.datastax.com/en/astra-db-serverless/api-reference/collections.html#the-indexing-option", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "collection_indexing_policy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "collection_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Collection Name", "dynamic": false, "info": "The name of the collection within Astra DB where the vectors will be stored.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "collection_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "haskell_chunked"}, "embedding_model": {"_input_type": "HandleInput", "advanced": false, "display_name": "Embedding Model", "dynamic": false, "info": "Allows an embedding model configuration.", "input_types": ["Embeddings"], "list": false, "list_add_label": "Add More", "name": "embedding_model", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "ingest_data": {"_input_type": "DataInput", "advanced": false, "display_name": "导入数据", "dynamic": false, "info": "", "input_types": ["Data", "DataFrame"], "list": true, "list_add_label": "Add More", "name": "ingest_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "keyspace": {"_input_type": "StrInput", "advanced": true, "display_name": "Keyspace", "dynamic": false, "info": "Optional keyspace within Astra DB to use for the collection.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "keyspace", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "metadata_incoming_links_key": {"_input_type": "StrInput", "advanced": true, "display_name": "Metadata incoming links key", "dynamic": false, "info": "Metadata key used for incoming links.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "metadata_incoming_links_key", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "metadata_indexing_exclude": {"_input_type": "StrInput", "advanced": true, "display_name": "Metadata Indexing Exclude", "dynamic": false, "info": "Optional list of metadata fields to exclude from the indexing.", "list": true, "list_add_label": "Add More", "load_from_db": false, "name": "metadata_indexing_exclude", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "metadata_indexing_include": {"_input_type": "StrInput", "advanced": true, "display_name": "Metadata Indexing Include", "dynamic": false, "info": "Optional list of metadata fields to include in the indexing.", "list": true, "list_add_label": "Add More", "load_from_db": false, "name": "metadata_indexing_include", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "metric": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Metric", "dynamic": false, "info": "Optional distance metric for vector comparisons in the vector store.", "name": "metric", "options": ["cosine", "dot_product", "euclidean"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "cosine"}, "number_of_results": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Results", "dynamic": false, "info": "Number of results to return.", "list": false, "list_add_label": "Add More", "name": "number_of_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 4}, "pre_delete_collection": {"_input_type": "BoolInput", "advanced": true, "display_name": "Pre Delete Collection", "dynamic": false, "info": "Boolean flag to determine whether to delete the collection before creating a new one.", "list": false, "list_add_label": "Add More", "name": "pre_delete_collection", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "search_filter": {"_input_type": "DictInput", "advanced": true, "display_name": "Search Metadata Filter", "dynamic": false, "info": "Optional dictionary of filters to apply to the search query.", "list": true, "list_add_label": "Add More", "name": "search_filter", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "search_query": {"_input_type": "MultilineInput", "advanced": false, "display_name": "搜索查询", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "search_query", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "search_score_threshold": {"_input_type": "FloatInput", "advanced": true, "display_name": "Search Score Threshold", "dynamic": false, "info": "Minimum similarity score threshold for search results. (when using 'Similarity with score threshold')", "list": false, "list_add_label": "Add More", "name": "search_score_threshold", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": 0}, "search_type": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Search Type", "dynamic": false, "info": "Search type to use", "name": "search_type", "options": ["Similarity", "Similarity with score threshold", "MMR (Max Marginal Relevance)", "Graph Traversal", "MMR (Max Marginal Relevance) Graph Traversal"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "MMR (Max Marginal Relevance) Graph Traversal"}, "setup_mode": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Setup Mode", "dynamic": false, "info": "Configuration mode for setting up the vector store, with options like 'Sync', or 'Off'.", "name": "setup_mode", "options": ["Sync", "Off"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Sync"}, "should_cache_vector_store": {"_input_type": "BoolInput", "advanced": true, "display_name": "缓存向量存储", "dynamic": false, "info": "如果为 True，则向量存储将在组件的当前构建中被缓存。这对于具有多个输出方法并希望共享相同向量存储的组件非常有用。", "list": false, "list_add_label": "Add More", "name": "should_cache_vector_store", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "token": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Astra DB Application Token", "dynamic": false, "info": "Authentication token for accessing Astra DB.", "input_types": ["Message"], "load_from_db": true, "name": "token", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_APPLICATION_TOKEN"}}, "tool_mode": false}, "showNode": true, "type": "AstraDBGraph"}, "dragging": false, "id": "AstraDBGraph-OBH6o", "measured": {"height": 634, "width": 320}, "position": {"x": -351.01793894175796, "y": 6101.568873207264}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatInput-2dZuD", "node": {"base_classes": ["Message"], "beta": false, "category": "inputs", "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatInput", "legacy": false, "metadata": {}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 4.5030081906763545e-05, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"聊天输入\"  # \"Chat Input\"\n    description = \"从练习场获取聊天输入，仅支持上传图片解析。\"  # \"Get chat inputs from the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatInput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            value=\"\",\n            info=\"作为输入传递的消息。\",  # \"Message to be passed as input.\"\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",  # \"files\"\n            display_name=\"文件\",  # \"Files\"\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"随消息发送的文件。\",  # \"Files to be sent with the message.\"\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"消息\", name=\"message\", method=\"message_response\"),  # \"Message\"\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"_input_type": "FileInput", "advanced": true, "display_name": "文件", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "xlsx", "xls", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "随消息发送的文件。", "list": true, "list_add_label": "Add More", "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "copy_field": false, "display_name": "文本", "dynamic": false, "info": "作为输入传递的消息。", "input_types": [], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "How does <PERSON><PERSON> handle function composition and what are some practical examples of its use?"}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": true, "type": "ChatInput"}, "dragging": false, "id": "ChatInput-2dZuD", "measured": {"height": 230, "width": 320}, "position": {"x": -1812.4888606742027, "y": 7065.04010157287}, "selected": false, "type": "genericNode"}], "viewport": {"x": 973.789930112276, "y": -1828.0579580568942, "zoom": 0.3442093352914648}}, "description": "从网页中提取链接，并使用带有最大边缘相关性（MMR）遍历的图检索增强生成（Graph RAG）链来处理这些内容。", "endpoint_name": null, "id": "243e8c45-43a2-441a-85ff-d681d665d66e", "is_component": false, "last_tested_version": "1.2.0", "name": "RAG图", "tags": ["rag", "q-a"]}