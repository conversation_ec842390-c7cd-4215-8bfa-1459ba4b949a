{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "StructuredOutputComponent", "id": "StructuredOutputComponent-XYoUc", "name": "structured_output", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data", "id": "ParseData-HzweJ", "inputTypes": ["Data"], "type": "other"}}, "id": "reactflow__edge-StructuredOutputComponent-XYoUc{œdataTypeœ:œStructuredOutputComponentœ,œidœ:œStructuredOutputComponent-XYoUcœ,œnameœ:œstructured_outputœ,œoutput_typesœ:[œDataœ]}-ParseData-HzweJ{œfieldNameœ:œdataœ,œidœ:œParseData-HzweJœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "StructuredOutputComponent-XYoUc", "sourceHandle": "{œdataTypeœ: œStructuredOutputComponentœ, œidœ: œStructuredOutputComponent-XYoUcœ, œnameœ: œstructured_outputœ, œoutput_typesœ: [œDataœ]}", "target": "ParseData-HzweJ", "targetHandle": "{œfieldNameœ: œdataœ, œidœ: œParseData-HzweJœ, œinputTypesœ: [œDataœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ParseData", "id": "ParseData-HzweJ", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-xQxLm", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-ParseData-HzweJ{œdataTypeœ:œParseDataœ,œidœ:œParseData-HzweJœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-xQxLm{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-xQxLmœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ParseData-HzweJ", "sourceHandle": "{œdataTypeœ: œParseDataœ, œidœ: œParseData-HzweJœ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-xQxLm", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-xQxLmœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-rAWlE", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "OpenAIModel-cqeNw", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-rAWlE{œdataTypeœ:œChatInputœ,œidœ:œChatInput-rAWlEœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-OpenAIModel-cqeNw{œfieldNameœ:œinput_valueœ,œidœ:œOpenAIModel-cqeNwœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-rAWlE", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-rAWlEœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "OpenAIModel-cqeNw", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œOpenAIModel-cqeNwœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-AzK6t", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "system_message", "id": "OpenAIModel-cqeNw", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-AzK6t{œdataTypeœ:œPromptœ,œidœ:œPrompt-AzK6tœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-OpenAIModel-cqeNw{œfieldNameœ:œsystem_messageœ,œidœ:œOpenAIModel-cqeNwœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-AzK6t", "sourceHandle": "{œdataTypeœ: œPromptœ, œidœ: œPrompt-AzK6tœ, œnameœ: œpromptœ, œoutput_typesœ: [œMessageœ]}", "target": "OpenAIModel-cqeNw", "targetHandle": "{œfieldNameœ: œsystem_messageœ, œidœ: œOpenAIModel-cqeNwœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"className": "", "data": {"sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-cqeNw", "name": "text_output", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "StructuredOutputComponent-XYoUc", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-OpenAIModel-cqeNw{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-cqeNwœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}-StructuredOutputComponent-XYoUc{œfieldNameœ:œinput_valueœ,œidœ:œStructuredOutputComponent-XYoUcœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "OpenAIModel-cqeNw", "sourceHandle": "{œdataTypeœ: œOpenAIModelœ, œidœ: œOpenAIModel-cqeNwœ, œnameœ: œtext_outputœ, œoutput_typesœ: [œMessageœ]}", "target": "StructuredOutputComponent-XYoUc", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œStructuredOutputComponent-XYoUcœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"className": "", "data": {"sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-cqeNw", "name": "model_output", "output_types": ["LanguageModel"]}, "targetHandle": {"fieldName": "llm", "id": "StructuredOutputComponent-XYoUc", "inputTypes": ["LanguageModel"], "type": "other"}}, "id": "reactflow__edge-OpenAIModel-cqeNw{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-cqeNwœ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}-StructuredOutputComponent-XYoUc{œfieldNameœ:œllmœ,œidœ:œStructuredOutputComponent-XYoUcœ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIModel-cqeNw", "sourceHandle": "{œdataTypeœ: œOpenAIModelœ, œidœ: œOpenAIModel-cqeNwœ, œnameœ: œmodel_outputœ, œoutput_typesœ: [œLanguageModelœ]}", "target": "StructuredOutputComponent-XYoUc", "targetHandle": "{œfieldNameœ: œllmœ, œidœ: œStructuredOutputComponent-XYoUcœ, œinputTypesœ: [œLanguageModelœ], œtypeœ: œotherœ}"}], "nodes": [{"data": {"description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "id": "ChatInput-rAWlE", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Get chat inputs from the Playground.", "display_name": "Chat Input", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"聊天输入\"  # \"Chat Input\"\n    description = \"从练习场获取聊天输入，仅支持上传图片解析。\"  # \"Get chat inputs from the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatInput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            value=\"\",\n            info=\"作为输入传递的消息。\",  # \"Message to be passed as input.\"\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",  # \"files\"\n            display_name=\"文件\",  # \"Files\"\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"随消息发送的文件。\",  # \"Files to be sent with the message.\"\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"消息\", name=\"message\", method=\"message_response\"),  # \"Message\"\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"_input_type": "FileInput", "advanced": true, "display_name": "文件", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "xlsx", "xls", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "随消息发送的文件。", "list": true, "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输入传递的消息。", "input_types": [], "list": false, "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "ChatInput"}, "dragging": false, "height": 234, "id": "ChatInput-rAWlE", "measured": {"height": 234, "width": 320}, "position": {"x": 1258.8272095125978, "y": 367.0048451335054}, "positionAbsolute": {"x": 1258.8272095125978, "y": 367.0048451335054}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Display a chat message in the Playground.", "display_name": "Chat Output", "id": "ChatOutput-xQxLm", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "基本清理数据", "dynamic": false, "info": "是否清理数据。", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"聊天输出\"  # \"Chat Output\"\n    description = \"在练习场中显示聊天消息。\"  # \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatOutput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输出传递的消息。\",  # \"Message to be passed as output.\"\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",  # \"data_template\"\n            display_name=\"数据模板\",  # \"Data Template\"\n            value=\"{text}\",\n            advanced=True,\n            info=\"用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。\",  # \"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\"\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",  # \"clean_data\"\n            display_name=\"基本清理数据\",  # \"Basic Clean Data\"\n            value=True,\n            info=\"是否清理数据。\",  # \"Whether to clean the data.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "数据模板", "dynamic": false, "info": "用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输出传递的消息。", "input_types": ["Data", "DataFrame", "Message"], "list": false, "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "ChatOutput"}, "dragging": false, "height": 234, "id": "ChatOutput-xQxLm", "measured": {"height": 234, "width": 320}, "position": {"x": 2742.72534045604, "y": 681.9098282545469}, "positionAbsolute": {"x": 2742.72534045604, "y": 681.9098282545469}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-K9w8w", "node": {"description": "# Image Sentiment Analysis\nWelcome to the Image Sentiment Classifier - an AI tool for quick image sentiment analysis!\n\n## Instructions\n\n1. **Prepare Your Image**\n   - Image should be clear and visible\n\n2. **Upload Options**\n   - Open the Playground\n   - Click file attachment icon\n   - Or drag and drop into playground\n\n3. **Wait for Analysis**\n   - System will process the image\n   - Uses zero-shot learning\n   - Classification happens automatically\n\n4. **Review Results**\n   - Get classification: Positive/Negative/Neutral\n   - Review confidence level\n   - Check reasoning if provided\n\n5. **Expected Classifications**\n   - Positive: Happy scenes, smiles, celebrations\n   - Negative: Sad scenes, problems, conflicts\n   - Neutral: Objects, landscapes, neutral scenes\n\nRemember: The clearer the image, the more accurate the classification! 📸✨", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "height": 583, "id": "note-K9w8w", "measured": {"height": 570, "width": 325}, "position": {"x": 791.7294511578832, "y": 340.1333942936967}, "positionAbsolute": {"x": 791.7294511578832, "y": 340.1333942936967}, "resizing": false, "selected": false, "style": {"height": 583, "width": 324}, "type": "noteNode", "width": 324}, {"data": {"description": "Transforms LLM responses into **structured data formats**. Ideal for extracting specific information or creating consistent outputs.", "display_name": "Structured Output", "id": "StructuredOutputComponent-XYoUc", "node": {"base_classes": ["Data"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Transforms LLM responses into **structured data formats**. Ideal for extracting specific information or creating consistent outputs.", "display_name": "Structured Output", "documentation": "", "edited": false, "field_order": ["llm", "input_value", "schema_name", "output_schema", "multiple"], "frozen": false, "icon": "braces", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {}, "output_types": [], "outputs": [{"cache": true, "display_name": "Structured Output", "method": "build_structured_output", "name": "structured_output", "selected": "Data", "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import TYPE_CHECKING, cast\n\nfrom pydantic import BaseModel, Field, create_model\n\nfrom langflow.base.models.chat_result import get_chat_result\nfrom langflow.custom import Component\nfrom langflow.helpers.base_model import build_model_from_schema\nfrom langflow.io import BoolInput, HandleInput, MessageTextInput, Output, StrInput, TableInput\nfrom langflow.schema.data import Data\n\nif TYPE_CHECKING:\n    from langflow.field_typing.constants import LanguageModel\n\n\nclass StructuredOutputComponent(Component):\n    display_name = \"Structured Output\"\n    description = (\n        \"Transforms LLM responses into **structured data formats**. Ideal for extracting specific information \"\n        \"or creating consistent outputs.\"\n    )\n    icon = \"braces\"\n\n    inputs = [\n        HandleInput(\n            name=\"llm\",\n            display_name=\"Language Model\",\n            info=\"The language model to use to generate the structured output.\",\n            input_types=[\"LanguageModel\"],\n        ),\n        MessageTextInput(name=\"input_value\", display_name=\"Input message\"),\n        StrInput(\n            name=\"schema_name\",\n            display_name=\"Schema Name\",\n            info=\"Provide a name for the output data schema.\",\n        ),\n        TableInput(\n            name=\"output_schema\",\n            display_name=\"Output Schema\",\n            info=\"Define the structure and data types for the model's output.\",\n            table_schema=[\n                {\n                    \"name\": \"name\",\n                    \"display_name\": \"Name\",\n                    \"type\": \"str\",\n                    \"description\": \"Specify the name of the output field.\",\n                    \"default\": \"field\",\n                },\n                {\n                    \"name\": \"description\",\n                    \"display_name\": \"Description\",\n                    \"type\": \"str\",\n                    \"description\": \"Describe the purpose of the output field.\",\n                    \"default\": \"description of field\",\n                },\n                {\n                    \"name\": \"type\",\n                    \"display_name\": \"Type\",\n                    \"type\": \"str\",\n                    \"description\": (\n                        \"Indicate the data type of the output field (e.g., str, int, float, bool, list, dict).\"\n                    ),\n                    \"default\": \"text\",\n                },\n                {\n                    \"name\": \"multiple\",\n                    \"display_name\": \"Multiple\",\n                    \"type\": \"boolean\",\n                    \"description\": \"Set to True if this output field should be a list of the specified type.\",\n                    \"default\": \"False\",\n                },\n            ],\n            value=[{\"name\": \"field\", \"description\": \"description of field\", \"type\": \"text\", \"multiple\": \"False\"}],\n        ),\n        BoolInput(\n            name=\"multiple\",\n            display_name=\"Generate Multiple\",\n            info=\"Set to True if the model should generate a list of outputs instead of a single output.\",\n        ),\n    ]\n\n    outputs = [\n        Output(name=\"structured_output\", display_name=\"Structured Output\", method=\"build_structured_output\"),\n    ]\n\n    def build_structured_output(self) -> Data:\n        if not hasattr(self.llm, \"with_structured_output\"):\n            msg = \"Language model does not support structured output.\"\n            raise TypeError(msg)\n        if not self.output_schema:\n            msg = \"Output schema cannot be empty\"\n            raise ValueError(msg)\n\n        output_model_ = build_model_from_schema(self.output_schema)\n        if self.multiple:\n            output_model = create_model(\n                self.schema_name,\n                objects=(list[output_model_], Field(description=f\"A list of {self.schema_name}.\")),  # type: ignore[valid-type]\n            )\n        else:\n            output_model = output_model_\n        try:\n            llm_with_structured_output = cast(\"LanguageModel\", self.llm).with_structured_output(schema=output_model)  # type: ignore[valid-type, attr-defined]\n\n        except NotImplementedError as exc:\n            msg = f\"{self.llm.__class__.__name__} does not support structured output.\"\n            raise TypeError(msg) from exc\n        config_dict = {\n            \"run_name\": self.display_name,\n            \"project_name\": self.get_project_name(),\n            \"callbacks\": self.get_langchain_callbacks(),\n        }\n        output = get_chat_result(runnable=llm_with_structured_output, input_value=self.input_value, config=config_dict)\n        if isinstance(output, BaseModel):\n            output_dict = output.model_dump()\n        else:\n            msg = f\"Output should be a Pydantic BaseModel, got {type(output)} ({output})\"\n            raise TypeError(msg)\n        return Data(data=output_dict)\n"}, "input_value": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Input message", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "llm": {"_input_type": "HandleInput", "advanced": false, "display_name": "Language Model", "dynamic": false, "info": "The language model to use to generate the structured output.", "input_types": ["LanguageModel"], "list": false, "name": "llm", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "multiple": {"_input_type": "BoolInput", "advanced": false, "display_name": "Generate Multiple", "dynamic": false, "info": "Set to True if the model should generate a list of outputs instead of a single output.", "list": false, "name": "multiple", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "output_schema": {"_input_type": "TableInput", "advanced": false, "display_name": "Output Schema", "dynamic": false, "info": "Define the structure and data types for the model's output.", "is_list": true, "load_from_db": false, "name": "output_schema", "placeholder": "", "required": false, "show": true, "table_schema": {"columns": [{"description": "Specify the name of the output field.", "display_name": "Name", "filterable": true, "formatter": "text", "name": "name", "sortable": true, "type": "text"}, {"description": "Describe the purpose of the output field.", "display_name": "Description", "filterable": true, "formatter": "text", "name": "description", "sortable": true, "type": "text"}, {"default": "text", "description": "Indicate the data type of the output field (e.g., str, int, float, bool, list, dict).", "display_name": "Type", "filterable": true, "formatter": "text", "name": "type", "sortable": true, "type": "text"}, {"default": "False", "description": "Set to True if this output field should be a list of the specified type.", "display_name": "Multiple", "filterable": true, "formatter": "text", "name": "multiple", "sortable": true, "type": "boolean"}]}, "title_case": false, "trace_as_metadata": true, "type": "table", "value": [{"description": "A Positive|Negative value that represents the image.", "multiple": "False", "name": "sentiment", "type": "text"}, {"description": "Brief Description of the image", "multiple": "False", "name": "description", "type": "text"}]}, "schema_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Schema Name", "dynamic": false, "info": "Provide a name for the output data schema.", "list": false, "load_from_db": false, "name": "schema_name", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "str", "value": "image_classification"}}, "tool_mode": false}, "type": "StructuredOutputComponent"}, "dragging": false, "height": 541, "id": "StructuredOutputComponent-XYoUc", "measured": {"height": 541, "width": 320}, "position": {"x": 2029.441019694193, "y": 414.7974622616549}, "positionAbsolute": {"x": 2029.441019694193, "y": 414.7974622616549}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "ParseData-HzweJ", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Convert Data into plain text following a specified template.", "display_name": "Parse Data", "documentation": "", "edited": false, "field_order": ["data", "template", "sep"], "frozen": false, "icon": "message-square", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {"legacy_name": "解析数据"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "parse_data", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据列表", "method": "parse_data_as_list", "name": "data_list", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"数据转消息\"  # \"Data to Message\"\n    description = \"使用输入数据中的任意 {字段} 将 Data 对象转换为消息。\"  # \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n    metadata = {\n        \"legacy_name\": \"解析数据\",  # \"Parse Data\"\n    }\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"数据\",  # \"Data\"\n            info=\"要转换为文本的数据。\",  # \"The data to convert to text.\"\n            is_list=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"模板\",  # \"Template\"\n            info=(\n                \"用于格式化数据的模板。\"  # \"The template to use for formatting the data. \"\n\"它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。\"  # \"It can contain the keys {text}, {data} or any other key in the Data.\"\n            ),\n            value=\"{文本}\",\n            required=True,\n        ),\n        StrInput(\nname=\"sep\",\ndisplay_name=\"分隔符\",  # \"Separator\"\nadvanced=True,\nvalue=\"\\n\",\n),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"text\",\n            info=\"数据作为单个消息，每个输入数据由分隔符分隔。\",  # \"Data as a single Message, with each input Data separated by Separator.\"\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"数据列表\",  # \"Data List\"\n            name=\"data_list\",\n            info=\"数据作为新数据的列表，每个数据的 `text` 由模板格式化。\",  # \"Data as a list of new Data, each having `text` formatted by Template.\"\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n"}, "data": {"_input_type": "DataInput", "advanced": false, "display_name": "数据", "dynamic": false, "info": "要转换为文本的数据。", "input_types": ["Data"], "list": true, "name": "data", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "sep": {"_input_type": "StrInput", "advanced": true, "display_name": "分隔符", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "str", "value": "\n"}, "template": {"_input_type": "MultilineInput", "advanced": false, "display_name": "模板", "dynamic": false, "info": "用于格式化数据的模板。它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "Sentiment: {sentiment} \n\nDescription: {description} "}}, "tool_mode": false}, "type": "ParseData"}, "dragging": false, "height": 302, "id": "ParseData-HzweJ", "measured": {"height": 302, "width": 320}, "position": {"x": 2389.490977317181, "y": 646.9530981549555}, "positionAbsolute": {"x": 2389.490977317181, "y": 646.9530981549555}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "id": "Prompt-AzK6t", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": []}, "description": "Create a prompt template with dynamic variables.", "display_name": "Prompt", "documentation": "", "edited": false, "field_order": ["template"], "frozen": false, "icon": "prompts", "legacy": false, "lf_version": "1.0.19.post2", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "提示消息", "method": "build_prompt", "name": "prompt", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"提示模板\"  # \"Prompt\"\n    description: str = \"创建带有动态变量的提示模板。\"  # \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"  # \"\"\n    trace_type = \"prompt\"  # \"prompt\"\n    name = \"Prompt\"  # \"\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"模板\"),  # \"Template\"\n        MessageTextInput(\n            name=\"tool_placeholder\",  # \"tool_placeholder\"\n            display_name=\"工具占位符\",  # \"Tool Placeholder\"\n            tool_mode=True,\n            advanced=True,\n            info=\"工具模式的占位符输入。\",  # \"A placeholder input for tool mode.\"\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"提示消息\", name=\"prompt\", method=\"build_prompt\"),  # \"Prompt Message\"\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "template": {"_input_type": "PromptInput", "advanced": false, "display_name": "模板", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "prompt", "value": "Classify the image into neutral, negative or positive. "}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "工具占位符", "dynamic": false, "info": "工具模式的占位符输入。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "Prompt"}, "dragging": false, "height": 260, "id": "Prompt-AzK6t", "measured": {"height": 260, "width": 320}, "position": {"x": 1262.0179832573751, "y": 632.1360181124842}, "positionAbsolute": {"x": 1262.0179832573751, "y": 632.1360181124842}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "OpenAIModel-cqeNw", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "category": "models", "conditional_paths": [], "custom_fields": {}, "description": "Generates text using OpenAI LLMs.", "display_name": "OpenAI", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed"], "frozen": false, "icon": "OpenAI", "key": "OpenAIModel", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "语言模型", "method": "build_model", "name": "model_output", "required_inputs": ["api_key", "model_name"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.14285714285714285, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "用于 OpenAI 模型的 OpenAI API 密钥。", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput\n\n# 替换硬编码的字符串为中文，并保留英文注释\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"  # OpenAI\n    description = \"使用 OpenAI LLMs 生成文本。\"  # Generates text using OpenAI LLMs.\n    icon = \"OpenAI\"  # OpenAI\n    name = \"OpenAIModel\"  # OpenAIModel\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"最大 Token 数\",  # Max Tokens\n            advanced=True,\n            info=\"生成的最大 token 数。设置为 0 表示无限制。\"  # The maximum number of tokens to generate. Set to 0 for unlimited tokens.\n            ,\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # Model Kwargs\n            advanced=True,\n            info=\"传递给模型的其他关键字参数。\"  # Additional keyword arguments to pass to the model.\n            ,\n        ),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON 模式\",  # JSON Mode\n            advanced=True,\n            info=\"如果为 True，则无论是否传递 schema，都会输出 JSON。\"  # If True, it will output JSON regardless of passing a schema.\n            ,\n        ),\n        StrInput(\n            name=\"model_name\",\n            display_name=\"模型名称\",  # Model Name\n            advanced=False,\n            info=\"所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、\",\n            required=True\n        ),\n        # DropdownInput(\n        #     name=\"model_name\",\n        #     display_name=\"模型名称\",  # Model Name\n        #     advanced=False,\n        #     options=OPENAI_MODEL_NAMES,\n        #     value=OPENAI_MODEL_NAMES[1],\n        #     combobox=True,\n        # ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础地址\",  # OpenAI API Base\n            advanced=True,\n            info=\"OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。\"\n            \"您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。\"  # The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.\n            ,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API 密钥\",  # OpenAI API Key\n            info=\"用于 OpenAI 模型的 OpenAI API 密钥。\"  # The OpenAI API Key to use for the OpenAI model.\n            ,\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"温度\",  # Temperature\n            value=0.1,\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n        IntInput(\n            name=\"seed\",\n            display_name=\"随机种子\",  # Seed\n            info=\"随机种子控制任务的可重复性。\"  # The seed controls the reproducibility of the job.\n            ,\n            advanced=True,\n            value=1,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # Max Retries\n            info=\"生成时的最大重试次数。\"  # The maximum number of retries to make when generating.\n            ,\n            advanced=True,\n            value=5,\n        ),\n        IntInput(\n            name=\"timeout\",\n            display_name=\"超时时间\",  # Timeout\n            info=\"请求 OpenAI 完成 API 的超时时间。\"  # The timeout for requests to OpenAI completion API.\n            ,\n            advanced=True,\n            value=700,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        openai_api_key = self.api_key\n        temperature = self.temperature\n        model_name: str = self.model_name\n        max_tokens = self.max_tokens\n        model_kwargs = self.model_kwargs or {}\n        openai_api_base = self.openai_api_base or \"https://api.openai.com/v1\"\n        json_mode = self.json_mode\n        seed = self.seed\n        max_retries = self.max_retries\n        timeout = self.timeout\n\n        api_key = SecretStr(openai_api_key).get_secret_value() if openai_api_key else None\n        output = ChatOpenAI(\n            max_tokens=max_tokens or None,\n            model_kwargs=model_kwargs,\n            model=model_name,\n            base_url=openai_api_base,\n            api_key=api_key,\n            temperature=temperature if temperature is not None else 0.1,\n            seed=seed,\n            max_retries=max_retries,\n            request_timeout=timeout,\n        )\n        if json_mode:\n            output = output.bind(response_format={\"type\": \"json_object\"})\n\n        return output\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"Get a message from an OpenAI exception.\n\n        Args:\n            e (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")\n            if message:\n                return message\n        return None\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON 模式", "dynamic": false, "info": "如果为 True，则无论是否传递 schema，都会输出 JSON。", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "生成时的最大重试次数。", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "最大 Token 数", "dynamic": false, "info": "生成的最大 token 数。设置为 0 表示无限制。", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "传递给模型的其他关键字参数。", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "模型名称", "dynamic": false, "info": "所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API 基础地址", "dynamic": false, "info": "OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "随机种子", "dynamic": false, "info": "随机种子控制任务的可重复性。", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "流式输出", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "系统消息", "dynamic": false, "info": "传递给模型的系统消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "温度", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "超时时间", "dynamic": false, "info": "请求 OpenAI 完成 API 的超时时间。", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}}, "tool_mode": false}, "showNode": true, "type": "OpenAIModel"}, "dragging": false, "id": "OpenAIModel-cqeNw", "measured": {"height": 653, "width": 320}, "position": {"x": 1638.1662423437713, "y": 374.7199736704084}, "selected": false, "type": "genericNode"}], "viewport": {"x": -426.91879919031885, "y": 21.85679755101154, "zoom": 0.6313688898572775}}, "description": "运用零样本学习技术对图像进行分析，并将其分类为积极、消极或中性类别。", "endpoint_name": null, "gradient": "2", "icon": "Image", "id": "0caf0da8-c233-4fc5-9df3-41bb58403885", "is_component": false, "last_tested_version": "1.0.19.post2", "name": "图像情感分析", "tags": ["classification"]}