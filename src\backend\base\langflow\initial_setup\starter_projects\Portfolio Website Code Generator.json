{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ParseData", "id": "ParseData-uKULv", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "AnthropicModel-vog1V", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ParseData-uKULv{œdataTypeœ:œParseDataœ,œidœ:œParseData-uKULvœ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-AnthropicModel-vog1V{œfieldNameœ:œinput_valueœ,œidœ:œAnthropicModel-vog1Vœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ParseData-uKULv", "sourceHandle": "{œdataTypeœ: œParseDataœ, œidœ: œParseData-uKULvœ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "AnthropicModel-vog1V", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œAnthropicModel-vog1Vœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "TextInput", "id": "TextInput-FRxv5", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "system_message", "id": "AnthropicModel-vog1V", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-TextInput-FRxv5{œdataTypeœ:œTextInputœ,œidœ:œTextInput-FRxv5œ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-AnthropicModel-vog1V{œfieldNameœ:œsystem_messageœ,œidœ:œAnthropicModel-vog1Vœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "TextInput-FRxv5", "sourceHandle": "{œdataTypeœ: œTextInputœ, œidœ: œTextInput-FRxv5œ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "AnthropicModel-vog1V", "targetHandle": "{œfieldNameœ: œsystem_messageœ, œidœ: œAnthropicModel-vog1Vœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "AnthropicModel", "id": "AnthropicModel-vog1V", "name": "text_output", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-vpJbx", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-AnthropicModel-vog1V{œdataTypeœ:œAnthropicModelœ,œidœ:œAnthropicModel-vog1Vœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-vpJbx{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-vpJbxœ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "AnthropicModel-vog1V", "sourceHandle": "{œdataTypeœ: œAnthropicModelœ, œidœ: œAnthropicModel-vog1Vœ, œnameœ: œtext_outputœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-vpJbx", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-vpJbxœ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "AnthropicModel", "id": "AnthropicModel-5piN6", "name": "model_output", "output_types": ["LanguageModel"]}, "targetHandle": {"fieldName": "llm", "id": "StructuredOutput-ApbKx", "inputTypes": ["LanguageModel"], "type": "other"}}, "id": "reactflow__edge-AnthropicModel-5piN6{œdataTypeœ:œAnthropicModelœ,œidœ:œAnthropicModel-5piN6œ,œnameœ:œmodel_outputœ,œoutput_typesœ:[œLanguageModelœ]}-StructuredOutput-ApbKx{œfieldNameœ:œllmœ,œidœ:œStructuredOutput-ApbKxœ,œinputTypesœ:[œLanguageModelœ],œtypeœ:œotherœ}", "selected": false, "source": "AnthropicModel-5piN6", "sourceHandle": "{œdataTypeœ: œAnthropicModelœ, œidœ: œAnthropicModel-5piN6œ, œnameœ: œmodel_outputœ, œoutput_typesœ: [œLanguageModelœ]}", "target": "StructuredOutput-ApbKx", "targetHandle": "{œfieldNameœ: œllmœ, œidœ: œStructuredOutput-ApbKxœ, œinputTypesœ: [œLanguageModelœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ParseData", "id": "ParseData-sbo55", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "StructuredOutput-ApbKx", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ParseData-sbo55{œdataTypeœ:œParseDataœ,œidœ:œParseData-sbo55œ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-StructuredOutput-ApbKx{œfieldNameœ:œinput_valueœ,œidœ:œStructuredOutput-ApbKxœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ParseData-sbo55", "sourceHandle": "{œdataTypeœ: œParseDataœ, œidœ: œParseData-sbo55œ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "StructuredOutput-ApbKx", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œStructuredOutput-ApbKxœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "File", "id": "File-u0H8v", "name": "data", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data", "id": "ParseData-sbo55", "inputTypes": ["Data"], "type": "other"}}, "id": "reactflow__edge-File-u0H8v{œdataTypeœ:œFileœ,œidœ:œFile-u0H8vœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-ParseData-sbo55{œfieldNameœ:œdataœ,œidœ:œParseData-sbo55œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "File-u0H8v", "sourceHandle": "{œdataTypeœ: œFileœ, œidœ: œFile-u0H8vœ, œnameœ: œdataœ, œoutput_typesœ: [œDataœ]}", "target": "ParseData-sbo55", "targetHandle": "{œfieldNameœ: œdataœ, œidœ: œParseData-sbo55œ, œinputTypesœ: [œDataœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "StructuredOutput", "id": "StructuredOutput-ApbKx", "name": "structured_output", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data", "id": "ParseData-uKULv", "inputTypes": ["Data"], "type": "other"}}, "id": "reactflow__edge-StructuredOutput-ApbKx{œdataTypeœ:œStructuredOutputœ,œidœ:œStructuredOutput-ApbKxœ,œnameœ:œstructured_outputœ,œoutput_typesœ:[œDataœ]}-ParseData-uKULv{œfieldNameœ:œdataœ,œidœ:œParseData-uKULvœ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "StructuredOutput-ApbKx", "sourceHandle": "{œdataTypeœ: œStructuredOutputœ, œidœ: œStructuredOutput-ApbKxœ, œnameœ: œstructured_outputœ, œoutput_typesœ: [œDataœ]}", "target": "ParseData-uKULv", "targetHandle": "{œfieldNameœ: œdataœ, œidœ: œParseData-uKULvœ, œinputTypesœ: [œDataœ], œtypeœ: œotherœ}"}], "nodes": [{"data": {"id": "ParseData-sbo55", "node": {"base_classes": ["Data", "Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "display_name": "Data to Message", "documentation": "", "edited": false, "field_order": ["data", "template", "sep"], "frozen": false, "icon": "message-square", "legacy": false, "lf_version": "1.1.5", "metadata": {"legacy_name": "解析数据"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "parse_data", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据列表", "method": "parse_data_as_list", "name": "data_list", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"数据转消息\"  # \"Data to Message\"\n    description = \"使用输入数据中的任意 {字段} 将 Data 对象转换为消息。\"  # \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n    metadata = {\n        \"legacy_name\": \"解析数据\",  # \"Parse Data\"\n    }\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"数据\",  # \"Data\"\n            info=\"要转换为文本的数据。\",  # \"The data to convert to text.\"\n            is_list=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"模板\",  # \"Template\"\n            info=(\n                \"用于格式化数据的模板。\"  # \"The template to use for formatting the data. \"\n\"它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。\"  # \"It can contain the keys {text}, {data} or any other key in the Data.\"\n            ),\n            value=\"{文本}\",\n            required=True,\n        ),\n        StrInput(\nname=\"sep\",\ndisplay_name=\"分隔符\",  # \"Separator\"\nadvanced=True,\nvalue=\"\\n\",\n),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"text\",\n            info=\"数据作为单个消息，每个输入数据由分隔符分隔。\",  # \"Data as a single Message, with each input Data separated by Separator.\"\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"数据列表\",  # \"Data List\"\n            name=\"data_list\",\n            info=\"数据作为新数据的列表，每个数据的 `text` 由模板格式化。\",  # \"Data as a list of new Data, each having `text` formatted by Template.\"\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n"}, "data": {"_input_type": "DataInput", "advanced": false, "display_name": "数据", "dynamic": false, "info": "要转换为文本的数据。", "input_types": ["Data"], "list": true, "list_add_label": "Add More", "name": "data", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "sep": {"_input_type": "StrInput", "advanced": true, "display_name": "分隔符", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n"}, "template": {"_input_type": "MultilineInput", "advanced": false, "display_name": "模板", "dynamic": false, "info": "用于格式化数据的模板。它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}}, "tool_mode": false}, "showNode": true, "type": "ParseData"}, "dragging": false, "id": "ParseData-sbo55", "measured": {"height": 342, "width": 320}, "position": {"x": 305.18655230615144, "y": 482.32049719078225}, "selected": false, "type": "genericNode"}, {"data": {"id": "ParseData-uKULv", "node": {"base_classes": ["Data", "Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Convert Data objects into Messages using any {field_name} from input data.", "display_name": "Data to Message", "documentation": "", "edited": false, "field_order": ["data", "template", "sep"], "frozen": false, "icon": "message-square", "legacy": false, "lf_version": "1.1.5", "metadata": {"legacy_name": "解析数据"}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "parse_data", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据列表", "method": "parse_data_as_list", "name": "data_list", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"数据转消息\"  # \"Data to Message\"\n    description = \"使用输入数据中的任意 {字段} 将 Data 对象转换为消息。\"  # \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n    metadata = {\n        \"legacy_name\": \"解析数据\",  # \"Parse Data\"\n    }\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"数据\",  # \"Data\"\n            info=\"要转换为文本的数据。\",  # \"The data to convert to text.\"\n            is_list=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"模板\",  # \"Template\"\n            info=(\n                \"用于格式化数据的模板。\"  # \"The template to use for formatting the data. \"\n\"它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。\"  # \"It can contain the keys {text}, {data} or any other key in the Data.\"\n            ),\n            value=\"{文本}\",\n            required=True,\n        ),\n        StrInput(\nname=\"sep\",\ndisplay_name=\"分隔符\",  # \"Separator\"\nadvanced=True,\nvalue=\"\\n\",\n),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"text\",\n            info=\"数据作为单个消息，每个输入数据由分隔符分隔。\",  # \"Data as a single Message, with each input Data separated by Separator.\"\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"数据列表\",  # \"Data List\"\n            name=\"data_list\",\n            info=\"数据作为新数据的列表，每个数据的 `text` 由模板格式化。\",  # \"Data as a list of new Data, each having `text` formatted by Template.\"\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n"}, "data": {"_input_type": "DataInput", "advanced": false, "display_name": "数据", "dynamic": false, "info": "要转换为文本的数据。", "input_types": ["Data"], "list": true, "list_add_label": "Add More", "name": "data", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "sep": {"_input_type": "StrInput", "advanced": true, "display_name": "分隔符", "dynamic": false, "info": "", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n"}, "template": {"_input_type": "MultilineInput", "advanced": false, "display_name": "模板", "dynamic": false, "info": "用于格式化数据的模板。它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{results}"}}, "tool_mode": false}, "showNode": true, "type": "ParseData"}, "dragging": false, "id": "ParseData-uKULv", "measured": {"height": 342, "width": 320}, "position": {"x": 1743.003626296405, "y": 585.3047133010242}, "selected": false, "type": "genericNode"}, {"data": {"id": "TextInput-FRxv5", "node": {"base_classes": ["Message"], "beta": false, "category": "inputs", "conditional_paths": [], "custom_fields": {}, "description": "Get text inputs from the Playground.", "display_name": "Text Input", "documentation": "", "edited": false, "field_order": ["input_value"], "frozen": false, "icon": "type", "key": "TextInput", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.0020353564437605998, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.io.text import TextComponent\nfrom langflow.io import MultilineInput, Output\nfrom langflow.schema.message import Message\n\n\nclass TextInputComponent(TextComponent):\n    display_name = \"文本输入\"  # \"Text Input\"\n    description = \"从练习场获取文本输入。\"  # \"Get text inputs from the Playground.\"\n    icon = \"type\"  # \"文本类型\"\n    name = \"TextInput\"  # \"\"\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输入传递的文本。\",  # \"Text to be passed as input.\"\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"消息\", name=\"text\", method=\"text_response\"),  # \"Message\"\n    ]\n\n    def text_response(self) -> Message:\n        return Message(\n            text=self.input_value,\n        )\n"}, "input_value": {"_input_type": "MultilineInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输入传递的文本。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "Generate a single-page portfolio website using HTML and CSS that takes a resume in JSON format as input and dynamically renders the following sections with a well-structured and aesthetic layout:\n\n📌 Sections & Content Requirements:\n\t1.\tHeader:\n\t•\tDisplay the person’s name, job title, and a professional tagline prominently.\n\t•\tEnsure the name is bold and eye-catching, with subtle emphasis on the job title.\n\t2.\tAbout Me:\n\t•\tExtract and enhance the personal summary from the resume, making it engaging, concise, and readable.\n\t•\tUse short, well-structured sentences to improve clarity.\n\t3.\tExperience:\n\t•\tList past job roles with company names, durations, and a refined description of responsibilities.\n\t•\tEnsure descriptions are professionally formatted, with key contributions highlighted.\n\t4.\tProjects:\n\t•\tDisplay projects as a neatly styled list.\n\t•\tEach project includes a title, refined description, technologies used, and rounded buttons linking to GitHub or live demo (if available).\n\t5.\tSkills:\n\t•\tDisplay skills as aesthetic pill-style badges below the Skills section title. Display all skills mentioned\n\t•\tArrange in a well-balanced, ensuring readability and consistent spacing.\n\t6.\tEducation:\n\t•\tList degrees, institutions, and years attended in a clean and professional format.\n\t•\tMaintain uniformity in typography and spacing.\n\t7.\tAwards & Publications  (if any):\n\t•\tIf the resume contains awards or publications, display them in a separate section.\n\t•\tEach entry includes the title, organization, and year, ensuring clean alignment.\n\t8.\tContact:\n\t•\tDisplay email, social media links, and an optional contact button.\n\t•\tEnsure social media links are clearly visible, with modern and accessible icon buttons.\n\n🎨 Styling & Aesthetic Requirements:\n\n✅ Minimalist & Elegant:\n\t•\tClean layout with ample whitespace for breathing room.\n\t•\tConsistent spacing across all sections.\n\n✅ Fast & Lightweight:\n\t•\tUse only HTML & CSS (no JavaScript required).\n\t•\tEnsure a smooth, fast-loading experience.\n\n✅ Beautiful Typography:\n\t•\tUse a modern, professional Google Font that complements the design.\n\t•\tEnsure text readability with proper size, weight, and contrast.\n\n✅ Visually Harmonious Colors & Themes:\n\t•\tFollow a cohesive color palette that ensures a modern, professional feel.\n\t•\tEnsure background colors, text colors, and section dividers are consistent and complementary.\n\t•\tAvoid hard-to-read combinations (e.g., light text on a light background).\n\n✅ Responsive & Readable Design:\n\t•\tMobile-first approach, adapting to desktop, tablet, and mobile views.\n\t•\tMaintain consistency in padding, margins, and alignment.\n\n✅ Dark Mode Support:\n\t•\tAuto-detect system settings and adjust the theme accordingly.\n\t•\tEnsure clear contrasts and readability in both light and dark modes.\n\n✅ Embedded CSS:\n\t•\tEnsure CSS is written directly in the HTML file within <style> tags for easy integration.\n\n🚀 Key Enhancements for a Superior First Impression:\n\t•\tEnsure the color scheme is visually cohesive and all text is legible against the background.\n\t•\tMaintain uniform padding and spacing for a professional, structured appearance.\n\t•\tImprove text formatting, ensuring sections are balanced and visually engaging.\n\t•\tFollow aesthetically pleasing, simple yet modern design principles.\n\n🌟 End Goal:\n\nThe final output should be a well-balanced, visually stunning, and highly readable portfolio website that immediately impresses viewers. The design must be polished, with an intuitive layout, ensuring consistency, clarity, and elegance.\nEnsuring all details in resume are well displayed in portfolio website.\nInclude all experiences, projects and education details from resume in the html code generated.\n"}}, "tool_mode": false}, "showNode": true, "type": "TextInput"}, "dragging": false, "id": "TextInput-FRxv5", "measured": {"height": 230, "width": 320}, "position": {"x": 1750.5165749650018, "y": 1018.4979290286542}, "selected": false, "type": "genericNode"}, {"data": {"id": "AnthropicModel-5piN6", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "category": "models", "conditional_paths": [], "custom_fields": {}, "description": "Generate text using Anthropic Chat&Completion LLMs with prefill support.", "display_name": "Anthropic", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_name", "api_key", "temperature", "base_url", "tool_model_enabled", "prefill"], "frozen": false, "icon": "Anthropic", "key": "AnthropicModel", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "语言模型", "method": "build_model", "name": "model_output", "required_inputs": ["api_key"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.0005851173668140926, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Anthropic API Key", "dynamic": false, "info": "Your Anthropic API key.", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "ANTHROPIC_API_KEY"}, "base_url": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Anthropic API URL", "dynamic": false, "info": "Endpoint of the Anthropic API. Defaults to 'https://api.anthropic.com' if not specified.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "base_url", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "https://api.anthropic.com"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nimport requests\nfrom loguru import logger\n\nfrom langflow.base.models.anthropic_constants import ANTHROPIC_MODELS\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.io import BoolInput, DropdownInput, IntInput, MessageTextInput, SecretStrInput, SliderInput\nfrom langflow.schema.dotdict import dotdict\n\n\nclass AnthropicModelComponent(LCModelComponent):\n    display_name = \"Anthropic\"\n    description = \"Generate text using Anthropic Chat&Completion LLMs with prefill support.\"\n    icon = \"Anthropic\"\n    name = \"AnthropicModel\"\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"Max Tokens\",\n            advanced=True,\n            value=4096,\n            info=\"The maximum number of tokens to generate. Set to 0 for unlimited tokens.\",\n        ),\n        DropdownInput(\n            name=\"model_name\",\n            display_name=\"Model Name\",\n            options=ANTHROPIC_MODELS,\n            refresh_button=True,\n            value=ANTHROPIC_MODELS[0],\n            combobox=True,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"Anthropic API Key\",\n            info=\"Your Anthropic API key.\",\n            value=None,\n            required=True,\n            real_time_refresh=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"Temperature\",\n            value=0.1,\n            info=\"Run inference with this temperature. Must by in the closed interval [0.0, 1.0].\",\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"base_url\",\n            display_name=\"Anthropic API URL\",\n            info=\"Endpoint of the Anthropic API. Defaults to 'https://api.anthropic.com' if not specified.\",\n            value=\"https://api.anthropic.com\",\n            real_time_refresh=True,\n        ),\n        BoolInput(\n            name=\"tool_model_enabled\",\n            display_name=\"Enable Tool Models\",\n            info=(\n                \"Select if you want to use models that can work with tools. If yes, only those models will be shown.\"\n            ),\n            advanced=False,\n            value=False,\n            real_time_refresh=True,\n        ),\n        MessageTextInput(\n            name=\"prefill\", display_name=\"Prefill\", info=\"Prefill text to guide the model's response.\", advanced=True\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        try:\n            from langchain_anthropic.chat_models import ChatAnthropic\n        except ImportError as e:\n            msg = \"langchain_anthropic is not installed. Please install it with `pip install langchain_anthropic`.\"\n            raise ImportError(msg) from e\n        try:\n            output = ChatAnthropic(\n                model=self.model_name,\n                anthropic_api_key=self.api_key,\n                max_tokens_to_sample=self.max_tokens,\n                temperature=self.temperature,\n                anthropic_api_url=self.base_url,\n                streaming=self.stream,\n            )\n        except Exception as e:\n            msg = \"Could not connect to Anthropic API.\"\n            raise ValueError(msg) from e\n\n        return output\n\n    def get_models(self, tool_model_enabled: bool | None = None) -> list[str]:\n        try:\n            import anthropic\n\n            client = anthropic.Anthropic(api_key=self.api_key)\n            models = client.models.list(limit=20).data\n            model_ids = [model.id for model in models]\n        except (ImportError, ValueError, requests.exceptions.RequestException) as e:\n            logger.exception(f\"Error getting model names: {e}\")\n            model_ids = ANTHROPIC_MODELS\n        if tool_model_enabled:\n            try:\n                from langchain_anthropic.chat_models import ChatAnthropic\n            except ImportError as e:\n                msg = \"langchain_anthropic is not installed. Please install it with `pip install langchain_anthropic`.\"\n                raise ImportError(msg) from e\n            for model in model_ids:\n                model_with_tool = ChatAnthropic(\n                    model=self.model_name,\n                    anthropic_api_key=self.api_key,\n                    anthropic_api_url=self.base_url,\n                )\n                if not self.supports_tool_calling(model_with_tool):\n                    model_ids.remove(model)\n        return model_ids\n\n    def _get_exception_message(self, exception: Exception) -> str | None:\n        \"\"\"Get a message from an Anthropic exception.\n\n        Args:\n            exception (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from anthropic import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(exception, BadRequestError):\n            message = exception.body.get(\"error\", {}).get(\"message\")\n            if message:\n                return message\n        return None\n\n    def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):\n        if field_name in {\"base_url\", \"model_name\", \"tool_model_enabled\", \"api_key\"} and field_value:\n            try:\n                if len(self.api_key) == 0:\n                    ids = ANTHROPIC_MODELS\n                else:\n                    try:\n                        ids = self.get_models(tool_model_enabled=self.tool_model_enabled)\n                    except (ImportError, ValueError, requests.exceptions.RequestException) as e:\n                        logger.exception(f\"Error getting model names: {e}\")\n                        ids = ANTHROPIC_MODELS\n                build_config[\"model_name\"][\"options\"] = ids\n                build_config[\"model_name\"][\"value\"] = ids[0]\n            except Exception as e:\n                msg = f\"Error getting model names: {e}\"\n                raise ValueError(msg) from e\n        return build_config\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON>", "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 4096}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "", "name": "model_name", "options": ["claude-3-5-sonnet-latest", "claude-3-5-haiku-latest", "claude-3-opus-latest", "claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"], "options_metadata": [], "placeholder": "", "refresh_button": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "claude-3-5-sonnet-20241022"}, "prefill": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Prefill", "dynamic": false, "info": "Prefill text to guide the model's response.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "prefill", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "流式输出", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "系统消息", "dynamic": false, "info": "传递给模型的系统消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "Temperature", "dynamic": false, "info": "Run inference with this temperature. Must by in the closed interval [0.0, 1.0].", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "tool_model_enabled": {"_input_type": "BoolInput", "advanced": false, "display_name": "Enable Tool Models", "dynamic": false, "info": "Select if you want to use models that can work with tools. If yes, only those models will be shown.", "list": false, "list_add_label": "Add More", "name": "tool_model_enabled", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}}, "tool_mode": false}, "showNode": true, "type": "AnthropicModel"}, "dragging": false, "id": "AnthropicModel-5piN6", "measured": {"height": 670, "width": 320}, "position": {"x": 833.1645098419217, "y": -53.112118311795115}, "selected": false, "type": "genericNode"}, {"data": {"id": "AnthropicModel-vog1V", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "category": "models", "conditional_paths": [], "custom_fields": {}, "description": "Generate text using Anthropic Chat&Completion LLMs with prefill support.", "display_name": "Anthropic", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_name", "api_key", "temperature", "base_url", "tool_model_enabled", "prefill"], "frozen": false, "icon": "Anthropic", "key": "AnthropicModel", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "语言模型", "method": "build_model", "name": "model_output", "required_inputs": ["api_key"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.0005851173668140926, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Anthropic API Key", "dynamic": false, "info": "Your Anthropic API key.", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "ANTHROPIC_API_KEY"}, "base_url": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "Anthropic API URL", "dynamic": false, "info": "Endpoint of the Anthropic API. Defaults to 'https://api.anthropic.com' if not specified.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "base_url", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "https://api.anthropic.com"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import Any\n\nimport requests\nfrom loguru import logger\n\nfrom langflow.base.models.anthropic_constants import ANTHROPIC_MODELS\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.io import BoolInput, DropdownInput, IntInput, MessageTextInput, SecretStrInput, SliderInput\nfrom langflow.schema.dotdict import dotdict\n\n\nclass AnthropicModelComponent(LCModelComponent):\n    display_name = \"Anthropic\"\n    description = \"Generate text using Anthropic Chat&Completion LLMs with prefill support.\"\n    icon = \"Anthropic\"\n    name = \"AnthropicModel\"\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"Max Tokens\",\n            advanced=True,\n            value=4096,\n            info=\"The maximum number of tokens to generate. Set to 0 for unlimited tokens.\",\n        ),\n        DropdownInput(\n            name=\"model_name\",\n            display_name=\"Model Name\",\n            options=ANTHROPIC_MODELS,\n            refresh_button=True,\n            value=ANTHROPIC_MODELS[0],\n            combobox=True,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"Anthropic API Key\",\n            info=\"Your Anthropic API key.\",\n            value=None,\n            required=True,\n            real_time_refresh=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"Temperature\",\n            value=0.1,\n            info=\"Run inference with this temperature. Must by in the closed interval [0.0, 1.0].\",\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"base_url\",\n            display_name=\"Anthropic API URL\",\n            info=\"Endpoint of the Anthropic API. Defaults to 'https://api.anthropic.com' if not specified.\",\n            value=\"https://api.anthropic.com\",\n            real_time_refresh=True,\n        ),\n        BoolInput(\n            name=\"tool_model_enabled\",\n            display_name=\"Enable Tool Models\",\n            info=(\n                \"Select if you want to use models that can work with tools. If yes, only those models will be shown.\"\n            ),\n            advanced=False,\n            value=False,\n            real_time_refresh=True,\n        ),\n        MessageTextInput(\n            name=\"prefill\", display_name=\"Prefill\", info=\"Prefill text to guide the model's response.\", advanced=True\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        try:\n            from langchain_anthropic.chat_models import ChatAnthropic\n        except ImportError as e:\n            msg = \"langchain_anthropic is not installed. Please install it with `pip install langchain_anthropic`.\"\n            raise ImportError(msg) from e\n        try:\n            output = ChatAnthropic(\n                model=self.model_name,\n                anthropic_api_key=self.api_key,\n                max_tokens_to_sample=self.max_tokens,\n                temperature=self.temperature,\n                anthropic_api_url=self.base_url,\n                streaming=self.stream,\n            )\n        except Exception as e:\n            msg = \"Could not connect to Anthropic API.\"\n            raise ValueError(msg) from e\n\n        return output\n\n    def get_models(self, tool_model_enabled: bool | None = None) -> list[str]:\n        try:\n            import anthropic\n\n            client = anthropic.Anthropic(api_key=self.api_key)\n            models = client.models.list(limit=20).data\n            model_ids = [model.id for model in models]\n        except (ImportError, ValueError, requests.exceptions.RequestException) as e:\n            logger.exception(f\"Error getting model names: {e}\")\n            model_ids = ANTHROPIC_MODELS\n        if tool_model_enabled:\n            try:\n                from langchain_anthropic.chat_models import ChatAnthropic\n            except ImportError as e:\n                msg = \"langchain_anthropic is not installed. Please install it with `pip install langchain_anthropic`.\"\n                raise ImportError(msg) from e\n            for model in model_ids:\n                model_with_tool = ChatAnthropic(\n                    model=self.model_name,\n                    anthropic_api_key=self.api_key,\n                    anthropic_api_url=self.base_url,\n                )\n                if not self.supports_tool_calling(model_with_tool):\n                    model_ids.remove(model)\n        return model_ids\n\n    def _get_exception_message(self, exception: Exception) -> str | None:\n        \"\"\"Get a message from an Anthropic exception.\n\n        Args:\n            exception (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from anthropic import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(exception, BadRequestError):\n            message = exception.body.get(\"error\", {}).get(\"message\")\n            if message:\n                return message\n        return None\n\n    def update_build_config(self, build_config: dotdict, field_value: Any, field_name: str | None = None):\n        if field_name in {\"base_url\", \"model_name\", \"tool_model_enabled\", \"api_key\"} and field_value:\n            try:\n                if len(self.api_key) == 0:\n                    ids = ANTHROPIC_MODELS\n                else:\n                    try:\n                        ids = self.get_models(tool_model_enabled=self.tool_model_enabled)\n                    except (ImportError, ValueError, requests.exceptions.RequestException) as e:\n                        logger.exception(f\"Error getting model names: {e}\")\n                        ids = ANTHROPIC_MODELS\n                build_config[\"model_name\"][\"options\"] = ids\n                build_config[\"model_name\"][\"value\"] = ids[0]\n            except Exception as e:\n                msg = f\"Error getting model names: {e}\"\n                raise ValueError(msg) from e\n        return build_config\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "<PERSON>", "dynamic": false, "info": "The maximum number of tokens to generate. Set to 0 for unlimited tokens.", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 8192}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "Model Name", "dynamic": false, "info": "", "name": "model_name", "options": ["claude-3-5-sonnet-latest", "claude-3-5-haiku-latest", "claude-3-opus-latest", "claude-3-5-sonnet-20240620", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"], "options_metadata": [], "placeholder": "", "refresh_button": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "claude-3-5-sonnet-20241022"}, "prefill": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "Prefill", "dynamic": false, "info": "Prefill text to guide the model's response.", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "prefill", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "流式输出", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "系统消息", "dynamic": false, "info": "传递给模型的系统消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "Temperature", "dynamic": false, "info": "Run inference with this temperature. Must by in the closed interval [0.0, 1.0].", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "tool_model_enabled": {"_input_type": "BoolInput", "advanced": false, "display_name": "Enable Tool Models", "dynamic": false, "info": "Select if you want to use models that can work with tools. If yes, only those models will be shown.", "list": false, "list_add_label": "Add More", "name": "tool_model_enabled", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}}, "tool_mode": false}, "showNode": true, "type": "AnthropicModel"}, "dragging": false, "id": "AnthropicModel-vog1V", "measured": {"height": 670, "width": 320}, "position": {"x": 2310.3492481396656, "y": 257.7980880183202}, "selected": false, "type": "genericNode"}, {"data": {"id": "ChatOutput-vpJbx", "node": {"base_classes": ["Message"], "beta": false, "category": "outputs", "conditional_paths": [], "custom_fields": {}, "description": "Display a chat message in the Playground.", "display_name": "Chat Output", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "key": "ChatOutput", "legacy": false, "lf_version": "1.1.5", "metadata": {}, "minimized": true, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.003169567463043492, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "基本清理数据", "dynamic": false, "info": "是否清理数据。", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"聊天输出\"  # \"Chat Output\"\n    description = \"在练习场中显示聊天消息。\"  # \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatOutput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输出传递的消息。\",  # \"Message to be passed as output.\"\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",  # \"data_template\"\n            display_name=\"数据模板\",  # \"Data Template\"\n            value=\"{text}\",\n            advanced=True,\n            info=\"用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。\",  # \"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\"\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",  # \"clean_data\"\n            display_name=\"基本清理数据\",  # \"Basic Clean Data\"\n            value=True,\n            info=\"是否清理数据。\",  # \"Whether to clean the data.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "数据模板", "dynamic": false, "info": "用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输出传递的消息。", "input_types": ["Data", "DataFrame", "Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "list_add_label": "Add More", "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "showNode": false, "type": "ChatOutput"}, "dragging": false, "id": "ChatOutput-vpJbx", "measured": {"height": 66, "width": 192}, "position": {"x": 2791.628210329036, "y": 590.4917809253919}, "selected": false, "type": "genericNode"}, {"data": {"id": "note-KVOU7", "node": {"description": "### 💡 Upload your resume here", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 358, "id": "note-KVOU7", "measured": {"height": 358, "width": 345}, "position": {"x": -114.57765784557182, "y": 483.643773301725}, "resizing": false, "selected": false, "type": "noteNode", "width": 346}, {"data": {"id": "note-pq8w3", "node": {"description": "## 📝 Portfolio Website Code Generator\n\nYour uploaded resume is parsed into a structured format, and output as HTML/CSS code for your own resume website!\n\n✅ **Accepted Formats:** PDF or TXT  \n✅ To ensure readability, provide clear headings, bullet points, and proper formatting. \n### 📌 Structured output fields:\n1. 🏷 **Full Name** - Candidate's full name  \n2. 📧 **Email** - A valid email address  \n3. 📞 **Phone Number** - Contact number  \n4. 🔗 **LinkedIn** - LinkedIn profile URL  \n5. 🖥 **GitHub** - GitHub profile URL (if applicable)  \n6. 🌐 **Portfolio** - Personal website or portfolio URL  \n7. 🛂 **Visa Status** - Work authorization details (if applicable)  \n8. 📝 **Summary** - A brief professional summary or objective statement  \n9. 💼 **Experience** - Work experience details (in dictionary format)  \n10. 🎓 **Education** - Education details (in dictionary format)  \n11. 🛠 **Skills** - Skills mentioned in the resume (comma-separated)  \n12. 🚀 **Projects** - Titles, descriptions, and details of projects.", "display_name": "", "documentation": "", "template": {}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-pq8w3", "measured": {"height": 324, "width": 325}, "position": {"x": -510.6692280684694, "y": 451.2228866325788}, "resizing": false, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "note-4GusB", "node": {"description": "### 💡 Click **Open table** to view the schema", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 438, "id": "note-4GusB", "measured": {"height": 438, "width": 358}, "position": {"x": 1291.3796543651624, "y": 549.6773427834883}, "resizing": false, "selected": false, "type": "noteNode", "width": 359}, {"data": {"id": "note-Tqu2d", "node": {"description": "### 💡 Add your Anthropic API key here", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-Tqu2d", "measured": {"height": 324, "width": 361}, "position": {"x": 814.421927194915, "y": -106.9729752344411}, "resizing": false, "selected": false, "type": "noteNode", "width": 362}, {"data": {"id": "note-Tc72L", "node": {"description": "### 💡 Add your Anthropic API key here", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-Tc72L", "measured": {"height": 324, "width": 343}, "position": {"x": 2298.488837453492, "y": 201.45217675238473}, "resizing": false, "selected": false, "type": "noteNode", "width": 344}, {"data": {"id": "StructuredOutput-ApbKx", "node": {"base_classes": ["Data", "DataFrame"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Transforms LLM responses into **structured data formats**. Ideal for extracting specific information or creating consistent outputs.", "display_name": "Structured Output", "documentation": "", "edited": false, "field_order": ["llm", "input_value", "system_prompt", "schema_name", "output_schema"], "frozen": false, "icon": "braces", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "结构化输出", "method": "build_structured_output", "name": "structured_output", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据框", "method": "as_dataframe", "name": "structured_output_dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from typing import TYPE_CHECKING, cast\n\nfrom pydantic import BaseModel, Field, create_model\n\nfrom langflow.base.models.chat_result import get_chat_result\nfrom langflow.custom import Component\nfrom langflow.helpers.base_model import build_model_from_schema\nfrom langflow.io import (\n    BoolInput,\n    HandleInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n    TableInput,\n)\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import DataFrame\nfrom langflow.schema.table import EditMode\n\nif TYPE_CHECKING:\n    from langflow.field_typing.constants import LanguageModel\n\n\nclass StructuredOutputComponent(Component):\n    display_name = \"结构化输出\"  # \"Structured Output\"\n    description = (\n        \"将 LLM 响应转换为 **结构化数据格式**。非常适合提取特定信息或创建一致的输出。\"  # \"Transforms LLM responses into **structured data formats**. Ideal for extracting specific information or creating consistent outputs.\"\n    )\n    name = \"StructuredOutput\"\n    icon = \"braces\"\n\n    inputs = [\n        HandleInput(\n            name=\"llm\",\n            display_name=\"语言模型\",  # \"Language Model\"\n            info=\"用于生成结构化输出的语言模型。\",  # \"The language model to use to generate the structured output.\"\n            input_types=[\"LanguageModel\"],\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"input_value\",\n            display_name=\"输入消息\",  # \"Input Message\"\n            info=\"传递给语言模型的输入消息。\",  # \"The input message to the language model.\"\n            tool_mode=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"system_prompt\",\n            display_name=\"格式说明\",  # \"Format Instructions\"\n            info=\"提供给语言模型的用于格式化输出的说明。\",  # \"The instructions to the language model for formatting the output.\"\n            value=(\n                \"您是一个 AI 系统，旨在从非结构化文本中提取结构化信息。\"\n                \"根据输入文本，返回一个具有预定义键的 JSON 对象，基于预期的结构。\"\n                \"准确提取值并根据指定的类型（例如，字符串、整数、浮点数、日期）格式化它们。\"\n                \"如果缺少值或无法确定值，请返回默认值（例如，null、0 或 'N/A'）。\"\n                \"如果输入文本中存在多个预期结构的实例，请将每个实例作为单独的 JSON 对象流式返回。\"  # Original English instructions\n            ),\n            required=True,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"schema_name\",\n            display_name=\"模式名称\",  # \"Schema Name\"\n            info=\"为输出数据模式提供一个名称。\",  # \"Provide a name for the output data schema.\"\n            advanced=True,\n        ),\n        TableInput(\n            name=\"output_schema\",\n            display_name=\"输出模式\",  # \"Output Schema\"\n            info=\"定义模型输出的结构和数据类型。\",  # \"Define the structure and data types for the model's output.\"\n            required=True,\n            table_schema=[\n                {\n                    \"name\": \"name\",\n                    \"display_name\": \"名称\",  # \"Name\"\n                    \"type\": \"str\",\n                    \"description\": \"指定输出字段的名称。\",  # \"Specify the name of the output field.\"\n                    \"default\": \"field\",\n                    \"edit_mode\": EditMode.INLINE,\n                },\n                {\n                    \"name\": \"description\",\n                    \"display_name\": \"描述\",  # \"Description\"\n                    \"type\": \"str\",\n                    \"description\": \"描述输出字段的用途。\",  # \"Describe the purpose of the output field.\"\n                    \"default\": \"字段的描述\",  # \"description of field\"\n                    \"edit_mode\": EditMode.POPOVER,\n                },\n                {\n                    \"name\": \"type\",\n                    \"display_name\": \"类型\",  # \"Type\"\n                    \"type\": \"str\",\n                    \"edit_mode\": EditMode.INLINE,\n                    \"description\": (\n                        \"指示输出字段的数据类型（例如，str、int、float、bool、list、dict）。\"  # \"Indicate the data type of the output field (e.g., str, int, float, bool, list, dict).\"\n                    ),\n                    \"options\": [\"str\", \"int\", \"float\", \"bool\", \"list\", \"dict\"],\n                    \"default\": \"str\",\n                },\n                {\n                    \"name\": \"multiple\",\n                    \"display_name\": \"多个值\",  # \"Multiple\"\n                    \"type\": \"boolean\",\n                    \"description\": \"如果此输出字段应为指定类型的列表，则设置为 True。\",  # \"Set to True if this output field should be a list of the specified type.\"\n                    \"default\": \"False\",\n                    \"edit_mode\": EditMode.INLINE,\n                },\n            ],\n            value=[\n                {\n                    \"name\": \"field\",\n                    \"description\": \"字段的描述\",  # \"description of field\"\n                    \"type\": \"str\",\n                    \"multiple\": \"False\",\n                }\n            ],\n        ),\n        BoolInput(\n            name=\"multiple\",\n            advanced=True,\n            display_name=\"生成多个\",  # \"Generate Multiple\"\n            info=\"[已弃用] 始终设置为 True\",  # \"[Deprecated] Always set to True\"\n            value=True,\n        ),\n    ]\n\n    outputs = [\n        Output(\n            name=\"structured_output\",\n            display_name=\"结构化输出\",  # \"Structured Output\"\n            method=\"build_structured_output\",\n        ),\n        Output(\n            name=\"structured_output_dataframe\",\n            display_name=\"数据框\",  # \"DataFrame\"\n            method=\"as_dataframe\",\n        ),\n    ]\n\n    def build_structured_output_base(self) -> Data:\n        schema_name = self.schema_name or \"OutputModel\"\n\n        if not hasattr(self.llm, \"with_structured_output\"):\n            msg = \"语言模型不支持结构化输出。\"  # \"Language model does not support structured output.\"\n            raise TypeError(msg)\n        if not self.output_schema:\n            msg = \"输出模式不能为空。\"  # \"Output schema cannot be empty.\"\n            raise ValueError(msg)\n\n        output_model_ = build_model_from_schema(self.output_schema)\n\n        output_model = create_model(\n            schema_name,\n            objects=(list[output_model_], Field(description=f\"一个 {schema_name} 的列表。\")),  # \"A list of {schema_name}.\"\n        )\n\n        try:\n            llm_with_structured_output = cast(\"LanguageModel\", self.llm).with_structured_output(schema=output_model)\n\n        except NotImplementedError as exc:\n            msg = f\"{self.llm.__class__.__name__} 不支持结构化输出。\"  # \"{self.llm.__class__.__name__} does not support structured output.\"\n            raise TypeError(msg) from exc\n        config_dict = {\n            \"run_name\": self.display_name,\n            \"project_name\": self.get_project_name(),\n            \"callbacks\": self.get_langchain_callbacks(),\n        }\n        result = get_chat_result(\n            runnable=llm_with_structured_output,\n            system_message=self.system_prompt,\n            input_value=self.input_value,\n            config=config_dict,\n        )\n        if isinstance(result, BaseModel):\n            result = result.model_dump()\n        if \"objects\" in result:\n            return result[\"objects\"]\n        return result\n\n    def build_structured_output(self) -> Data:\n        output = self.build_structured_output_base()\n\n        return Data(results=output)\n\n    def as_dataframe(self) -> DataFrame:\n        output = self.build_structured_output_base()\n        if isinstance(output, list):\n            return DataFrame(data=output)\n        return DataFrame(data=[output])\n"}, "input_value": {"_input_type": "MessageTextInput", "advanced": false, "display_name": "输入消息", "dynamic": false, "info": "传递给语言模型的输入消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "llm": {"_input_type": "HandleInput", "advanced": false, "display_name": "语言模型", "dynamic": false, "info": "用于生成结构化输出的语言模型。", "input_types": ["LanguageModel"], "list": false, "list_add_label": "Add More", "name": "llm", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "multiple": {"_input_type": "BoolInput", "advanced": true, "display_name": "生成多个", "dynamic": false, "info": "[已弃用] 始终设置为 True", "list": false, "list_add_label": "Add More", "name": "multiple", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "output_schema": {"_input_type": "TableInput", "advanced": false, "display_name": "输出模式", "dynamic": false, "info": "定义模型输出的结构和数据类型。", "is_list": true, "list_add_label": "Add More", "load_from_db": false, "name": "output_schema", "placeholder": "", "required": true, "show": true, "table_icon": "Table", "table_schema": {"columns": [{"default": "field", "description": "Specify the name of the output field.", "disable_edit": false, "display_name": "Name", "edit_mode": "inline", "filterable": true, "formatter": "text", "hidden": false, "name": "name", "sortable": true, "type": "text"}, {"default": "description of field", "description": "Describe the purpose of the output field.", "disable_edit": false, "display_name": "Description", "edit_mode": "popover", "filterable": true, "formatter": "text", "hidden": false, "name": "description", "sortable": true, "type": "text"}, {"default": "text", "description": "Indicate the data type of the output field (e.g., str, int, float, bool, list, dict).", "disable_edit": false, "display_name": "Type", "edit_mode": "inline", "filterable": true, "formatter": "text", "hidden": false, "name": "type", "sortable": true, "type": "text"}, {"default": "False", "description": "Set to True if this output field should be a list of the specified type.", "disable_edit": false, "display_name": "Multiple", "edit_mode": "inline", "filterable": true, "formatter": "text", "hidden": false, "name": "multiple", "sortable": true, "type": "boolean"}]}, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "trigger_icon": "Table", "trigger_text": "Open table", "type": "table", "value": [{"description": "Full name of the candidate", "multiple": "False", "name": "full_name", "type": "text"}, {"description": "Email ID", "multiple": "False", "name": "email", "type": "text"}, {"description": "contact number", "multiple": "False", "name": "phone_number", "type": "text"}, {"description": "LinkedIn URL", "multiple": "False", "name": "linkedin", "type": "text"}, {"description": "GitHub profile URL (if applicable)", "multiple": "False", "name": "github", "type": "text"}, {"description": "Personal website or portfolio URL", "multiple": "False", "name": "portfolio", "type": "text"}, {"description": "Visa/work authorization details (if applicable)", "multiple": "False", "name": "visa_status", "type": "text"}, {"description": "Short professional summary or objective statement", "multiple": "False", "name": "summary", "type": "text"}, {"description": "dictionaries of experience details with following keys:\n    \"job_title\": Job position/title,\n\t\"company_name\": Employer or organization\n\t\"location\": Job location (remote/in-office)\n\t\"start_date\": Start date (YYYY-MM)\n\t\"end_date\": End date or \"Present\"\n\t\"responsibilities\": List of responsibilities and tasks\n\t\"achievements\": List of key achievements and contributions", "multiple": "True", "name": "experience", "type": "dict"}, {"description": "dictionaries of Education details with following keys:\n\"degree\": Degree obtained (e.g., B.Sc., M.Sc., Ph.D.),\n\"field_of_study\": Major or specialization,\n\"institution\": University/college name,\n\"location\": Location of institution,\n\"start_date\": Start date (YYYY-MM),\n\"end_date\": Graduation/completion date (YYYY-MM),\n\"gpa\": GPA/grade (if available),\n\"relevant_courses\": List of relevant coursework (if applicable)", "multiple": "True", "name": "education", "type": "dict"}, {"description": "skills mentioned in the resume.comma seperated.", "multiple": "False", "name": "skills", "type": "list"}, {"description": "title and description and details of the project. Including the skills and technologies used.", "multiple": "False", "name": "projects", "type": "text"}]}, "schema_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "模式名称", "dynamic": false, "info": "为输出数据模式提供一个名称。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "schema_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "system_prompt": {"_input_type": "MultilineInput", "advanced": true, "display_name": "格式说明", "dynamic": false, "info": "提供给语言模型的用于格式化输出的说明。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_prompt", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "You are an AI system designed to extract structured information from unstructured text.Given the input_text, return a JSON object with predefined keys based on the expected structure.Extract values accurately and format them according to the specified type (e.g., string, integer, float, date).If a value is missing or cannot be determined, return a default (e.g., null, 0, or 'N/A').If multiple instances of the expected structure exist within the input_text, stream each as a separate JSON object."}}, "tool_mode": false}, "showNode": true, "type": "StructuredOutput"}, "dragging": false, "id": "StructuredOutput-ApbKx", "measured": {"height": 447, "width": 320}, "position": {"x": 1306.940204747624, "y": 645.3388247558626}, "selected": false, "type": "genericNode"}, {"data": {"id": "File-u0H8v", "node": {"base_classes": ["Data"], "beta": false, "category": "data", "conditional_paths": [], "custom_fields": {}, "description": "Load a file to be used in your project.", "display_name": "File", "documentation": "", "edited": false, "field_order": ["path", "file_path", "silent_errors", "delete_server_file_after_processing", "ignore_unsupported_extensions", "ignore_unspecified_files", "use_multithreading", "concurrency_multithreading"], "frozen": false, "icon": "file-text", "key": "File", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "数据", "method": "load_files", "name": "data", "required_inputs": [], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据表", "method": "load_dataframe", "name": "dataframe", "required_inputs": [], "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "消息", "method": "load_message", "name": "message", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "score": 9.159206968830713e-17, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data import BaseFileComponent\nfrom langflow.base.data.utils import TEXT_FILE_TYPES, parallel_load_data, parse_text_file_to_data\nfrom langflow.io import BoolInput, IntInput\nfrom langflow.schema import Data\n\n\nclass FileComponent(BaseFileComponent):\n    \"\"\"Handles loading and processing of individual or zipped text files.\n\n    This component supports processing multiple valid files within a zip archive,\n    resolving paths, validating file types, and optionally using multithreading for processing.\n    \"\"\"\n\n    display_name = \"文件\"  # \"File\"\n    description = \"加载一个文件以在您的项目中使用。\"  # \"Load a file to be used in your project.\"\n    icon = \"file-text\"\n    name = \"File\"\n\n    VALID_EXTENSIONS = TEXT_FILE_TYPES\n\n    inputs = [\n        *BaseFileComponent._base_inputs,\n        BoolInput(\n            name=\"use_multithreading\",\n            display_name=\"[已弃用] 使用多线程\",  # \"[Deprecated] Use Multithreading\"\n            advanced=True,\n            value=True,\n            info=\"将“处理并发数”设置为大于 1 以启用多线程。\",  # \"Set 'Processing Concurrency' greater than 1 to enable multithreading.\"\n        ),\n        IntInput(\n            name=\"concurrency_multithreading\",\n            display_name=\"处理并发数\",  # \"Processing Concurrency\"\n            advanced=True,\n            info=\"当处理多个文件时，同时处理的文件数量。\",  # \"When multiple files are being processed, the number of files to process concurrently.\"\n            value=1,\n        ),\n    ]\n\n    outputs = [\n        *BaseFileComponent._base_outputs,\n    ]\n\n    def process_files(self, file_list: list[BaseFileComponent.BaseFile]) -> list[BaseFileComponent.BaseFile]:\n        \"\"\"Processes files either sequentially or in parallel, depending on concurrency settings.\n\n        Args:\n            file_list (list[BaseFileComponent.BaseFile]): List of files to process.\n\n        Returns:\n            list[BaseFileComponent.BaseFile]: Updated list of files with merged data.\n        \"\"\"\n\n        def process_file(file_path: str, *, silent_errors: bool = False) -> Data | None:\n            \"\"\"Processes a single file and returns its Data object.\"\"\"\n            try:\n                return parse_text_file_to_data(file_path, silent_errors=silent_errors)\n            except FileNotFoundError as e:\n                msg = f\"文件未找到: {file_path}。错误: {e}\"  # \"File not found: {file_path}. Error: {e}\"\n                self.log(msg)\n                if not silent_errors:\n                    raise\n                return None\n            except Exception as e:\n                msg = f\"处理 {file_path} 时发生意外错误: {e}\"  # \"Unexpected error processing {file_path}: {e}\"\n                self.log(msg)\n                if not silent_errors:\n                    raise\n                return None\n\n        if not file_list:\n            msg = \"没有要处理的文件。\"  # \"No files to process.\"\n            raise ValueError(msg)\n\n        concurrency = 1 if not self.use_multithreading else max(1, self.concurrency_multithreading)\n        file_count = len(file_list)\n\n        parallel_processing_threshold = 2\n        if concurrency < parallel_processing_threshold or file_count < parallel_processing_threshold:\n            if file_count > 1:\n                self.log(f\"顺序处理 {file_count} 个文件。\")  # \"Processing {file_count} files sequentially.\"\n            processed_data = [process_file(str(file.path), silent_errors=self.silent_errors) for file in file_list]\n        else:\n            self.log(f\"开始并行处理 {file_count} 个文件，并发数: {concurrency}。\")  # \"Starting parallel processing of {file_count} files with concurrency: {concurrency}.\"\n            file_paths = [str(file.path) for file in file_list]\n            processed_data = parallel_load_data(\n                file_paths,\n                silent_errors=self.silent_errors,\n                load_function=process_file,\n                max_concurrency=concurrency,\n            )\n\n        # Use rollup_basefile_data to merge processed data with BaseFile objects\n        return self.rollup_data(file_list, processed_data)\n"}, "concurrency_multithreading": {"_input_type": "IntInput", "advanced": true, "display_name": "处理并发数", "dynamic": false, "info": "当处理多个文件时，同时处理的文件数量。", "list": false, "list_add_label": "Add More", "name": "concurrency_multithreading", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "delete_server_file_after_processing": {"_input_type": "BoolInput", "advanced": true, "display_name": "处理后删除服务器文件", "dynamic": false, "info": "如果为 true，处理后将删除服务器文件路径。", "list": false, "list_add_label": "Add More", "name": "delete_server_file_after_processing", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "file_path": {"_input_type": "HandleInput", "advanced": true, "display_name": "服务器文件路径", "dynamic": false, "info": "包含 'file_path' 属性指向服务器文件的 Data 对象 或包含文件路径的 Message 对象。优先于 'Path'，但支持相同的文件类型。", "input_types": ["Data", "Message"], "list": true, "list_add_label": "Add More", "name": "file_path", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "ignore_unspecified_files": {"_input_type": "BoolInput", "advanced": true, "display_name": "忽略未指定的文件", "dynamic": false, "info": "如果为 true，将忽略没有 'file_path' 属性的 Data。", "list": false, "list_add_label": "Add More", "name": "ignore_unspecified_files", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "ignore_unsupported_extensions": {"_input_type": "BoolInput", "advanced": true, "display_name": "忽略不支持的扩展名", "dynamic": false, "info": "如果为 true，将不会处理具有不支持扩展名的文件。", "list": false, "list_add_label": "Add More", "name": "ignore_unsupported_extensions", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "path": {"_input_type": "FileInput", "advanced": false, "display_name": "文件", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "xlsx", "xls", "zip", "tar", "tgz", "bz2", "gz"], "file_path": [], "info": "支持的文件扩展名: txt, md, mdx, csv, json, yaml, yml, xml, html, htm, pdf, docx, py, sh, sql, js, ts, tsx, xlsx, xls; 可选的打包文件扩展名: zip, tar, tgz, bz2, gz", "list": true, "list_add_label": "Add More", "name": "path", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "separator": {"_input_type": "StrInput", "advanced": true, "display_name": "分隔符", "dynamic": false, "info": "指定在 Message 格式中多个输出之间使用的分隔符。", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "separator", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n\n"}, "silent_errors": {"_input_type": "BoolInput", "advanced": true, "display_name": "静默错误", "dynamic": false, "info": "如果为 true，错误将不会引发异常。", "list": false, "list_add_label": "Add More", "name": "silent_errors", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "use_multithreading": {"_input_type": "BoolInput", "advanced": true, "display_name": "[已弃用] 使用多线程", "dynamic": false, "info": "将“处理并发数”设置为大于 1 以启用多线程。", "list": false, "list_add_label": "Add More", "name": "use_multithreading", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}}, "tool_mode": false}, "showNode": true, "type": "File"}, "dragging": false, "id": "File-u0H8v", "measured": {"height": 336, "width": 320}, "position": {"x": -103.41952640765028, "y": 527.2136756214662}, "selected": false, "type": "genericNode"}], "viewport": {"x": 258.19631043779543, "y": 250.36039957268596, "zoom": 0.3767176834743995}}, "description": "此模板可将 PDF 或 TXT 格式的简历文档转换为结构化的 JSON 格式，并根据这些结构化数据生成一个个人作品集网站的 HTML 代码。", "endpoint_name": null, "id": "c8719bf8-c53b-4d8d-a195-139891cad0f5", "is_component": false, "last_tested_version": "1.2.0", "name": "个人作品集网站代码生成器", "tags": ["chatbots", "coding"]}