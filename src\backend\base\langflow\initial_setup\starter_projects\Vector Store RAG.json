{"data": {"edges": [{"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ParseData", "id": "ParseData-Pc764", "name": "text", "output_types": ["Message"]}, "targetHandle": {"fieldName": "context", "id": "Prompt-T1JLQ", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "reactflow__edge-ParseData-Pc764{œdataTypeœ:œParseDataœ,œidœ:œParseData-Pc764œ,œnameœ:œtextœ,œoutput_typesœ:[œMessageœ]}-Prompt-T1JLQ{œfieldNameœ:œcontextœ,œidœ:œPrompt-T1JLQœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "source": "ParseData-Pc764", "sourceHandle": "{œdataTypeœ: œParseDataœ, œidœ: œParseData-Pc764œ, œnameœ: œtextœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-T1JLQ", "targetHandle": "{œfieldNameœ: œcontextœ, œidœ: œPrompt-T1JLQœ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-5reba", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "question", "id": "Prompt-T1JLQ", "inputTypes": ["Message", "Text"], "type": "str"}}, "id": "reactflow__edge-ChatInput-5reba{œdataTypeœ:œChatInputœ,œidœ:œChatInput-5rebaœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-Prompt-T1JLQ{œfieldNameœ:œquestionœ,œidœ:œPrompt-T1JLQœ,œinputTypesœ:[œMessageœ,œTextœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-5reba", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-5rebaœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "Prompt-T1JLQ", "targetHandle": "{œfieldNameœ: œquestionœ, œidœ: œPrompt-T1JLQœ, œinputTypesœ: [œMessageœ, œTextœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "File", "id": "File-IwyQk", "name": "data", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data_inputs", "id": "SplitText-EixdW", "inputTypes": ["Data", "DataFrame"], "type": "other"}}, "id": "reactflow__edge-File-IwyQk{œdataTypeœ:œFileœ,œidœ:œFile-IwyQkœ,œnameœ:œdataœ,œoutput_typesœ:[œDataœ]}-SplitText-EixdW{œfieldNameœ:œdata_inputsœ,œidœ:œSplitText-EixdWœ,œinputTypesœ:[œDataœ,œDataFrameœ],œtypeœ:œotherœ}", "selected": false, "source": "File-IwyQk", "sourceHandle": "{œdataTypeœ: œFileœ, œidœ: œFile-IwyQkœ, œnameœ: œdataœ, œoutput_typesœ: [œDataœ]}", "target": "SplitText-EixdW", "targetHandle": "{œfieldNameœ: œdata_inputsœ, œidœ: œSplitText-EixdWœ, œinputTypesœ: [œDataœ, œDataFrameœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "Prompt", "id": "Prompt-T1JLQ", "name": "prompt", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "OpenAIModel-e49kI", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-Prompt-T1JLQ{œdataTypeœ:œPromptœ,œidœ:œPrompt-T1JLQœ,œnameœ:œpromptœ,œoutput_typesœ:[œMessageœ]}-OpenAIModel-e49kI{œfieldNameœ:œinput_valueœ,œidœ:œOpenAIModel-e49kIœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "Prompt-T1JLQ", "sourceHandle": "{œdataTypeœ: œPromptœ, œidœ: œPrompt-T1JLQœ, œnameœ: œpromptœ, œoutput_typesœ: [œMessageœ]}", "target": "OpenAIModel-e49kI", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œOpenAIModel-e49kIœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIModel", "id": "OpenAIModel-e49kI", "name": "text_output", "output_types": ["Message"]}, "targetHandle": {"fieldName": "input_value", "id": "ChatOutput-aQXd2", "inputTypes": ["Data", "DataFrame", "Message"], "type": "str"}}, "id": "reactflow__edge-OpenAIModel-e49kI{œdataTypeœ:œOpenAIModelœ,œidœ:œOpenAIModel-e49kIœ,œnameœ:œtext_outputœ,œoutput_typesœ:[œMessageœ]}-ChatOutput-aQXd2{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-aQXd2œ,œinputTypesœ:[œDataœ,œDataFrameœ,œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "OpenAIModel-e49kI", "sourceHandle": "{œdataTypeœ: œOpenAIModelœ, œidœ: œOpenAIModel-e49kIœ, œnameœ: œtext_outputœ, œoutput_typesœ: [œMessageœ]}", "target": "ChatOutput-aQXd2", "targetHandle": "{œfieldNameœ: œinput_valueœ, œidœ: œChatOutput-aQXd2œ, œinputTypesœ: [œDataœ, œDataFrameœ, œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIEmbeddings", "id": "OpenAIEmbeddings-fftff", "name": "embeddings", "output_types": ["Embeddings"]}, "targetHandle": {"fieldName": "embedding_model", "id": "AstraDB-JF2lS", "inputTypes": ["Embeddings"], "type": "other"}}, "id": "reactflow__edge-OpenAIEmbeddings-fftff{œdataTypeœ:œOpenAIEmbeddingsœ,œidœ:œOpenAIEmbeddings-fftffœ,œnameœ:œembeddingsœ,œoutput_typesœ:[œEmbeddingsœ]}-AstraDB-JF2lS{œfieldNameœ:œembedding_modelœ,œidœ:œAstraDB-JF2lSœ,œinputTypesœ:[œEmbeddingsœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIEmbeddings-fftff", "sourceHandle": "{œdataTypeœ: œOpenAIEmbeddingsœ, œidœ: œOpenAIEmbeddings-fftffœ, œnameœ: œembeddingsœ, œoutput_typesœ: [œEmbeddingsœ]}", "target": "AstraDB-JF2lS", "targetHandle": "{œfieldNameœ: œembedding_modelœ, œidœ: œAstraDB-JF2lSœ, œinputTypesœ: [œEmbeddingsœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "SplitText", "id": "SplitText-EixdW", "name": "chunks", "output_types": ["Data"]}, "targetHandle": {"fieldName": "ingest_data", "id": "AstraDB-JF2lS", "inputTypes": ["Data", "DataFrame"], "type": "other"}}, "id": "reactflow__edge-SplitText-EixdW{œdataTypeœ:œSplitTextœ,œidœ:œSplitText-EixdWœ,œnameœ:œchunksœ,œoutput_typesœ:[œDataœ]}-AstraDB-JF2lS{œfieldNameœ:œingest_dataœ,œidœ:œAstraDB-JF2lSœ,œinputTypesœ:[œDataœ,œDataFrameœ],œtypeœ:œotherœ}", "selected": false, "source": "SplitText-EixdW", "sourceHandle": "{œdataTypeœ: œSplitTextœ, œidœ: œSplitText-EixdWœ, œnameœ: œchunksœ, œoutput_typesœ: [œDataœ]}", "target": "AstraDB-JF2lS", "targetHandle": "{œfieldNameœ: œingest_dataœ, œidœ: œAstraDB-JF2lSœ, œinputTypesœ: [œDataœ, œDataFrameœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "OpenAIEmbeddings", "id": "OpenAIEmbeddings-t4aMI", "name": "embeddings", "output_types": ["Embeddings"]}, "targetHandle": {"fieldName": "embedding_model", "id": "AstraDB-8CuoQ", "inputTypes": ["Embeddings"], "type": "other"}}, "id": "reactflow__edge-OpenAIEmbeddings-t4aMI{œdataTypeœ:œOpenAIEmbeddingsœ,œidœ:œOpenAIEmbeddings-t4aMIœ,œnameœ:œembeddingsœ,œoutput_typesœ:[œEmbeddingsœ]}-AstraDB-8CuoQ{œfieldNameœ:œembedding_modelœ,œidœ:œAstraDB-8CuoQœ,œinputTypesœ:[œEmbeddingsœ],œtypeœ:œotherœ}", "selected": false, "source": "OpenAIEmbeddings-t4aMI", "sourceHandle": "{œdataTypeœ: œOpenAIEmbeddingsœ, œidœ: œOpenAIEmbeddings-t4aMIœ, œnameœ: œembeddingsœ, œoutput_typesœ: [œEmbeddingsœ]}", "target": "AstraDB-8CuoQ", "targetHandle": "{œfieldNameœ: œembedding_modelœ, œidœ: œAstraDB-8CuoQœ, œinputTypesœ: [œEmbeddingsœ], œtypeœ: œotherœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "ChatInput", "id": "ChatInput-5reba", "name": "message", "output_types": ["Message"]}, "targetHandle": {"fieldName": "search_query", "id": "AstraDB-8CuoQ", "inputTypes": ["Message"], "type": "str"}}, "id": "reactflow__edge-ChatInput-5reba{œdataTypeœ:œChatInputœ,œidœ:œChatInput-5rebaœ,œnameœ:œmessageœ,œoutput_typesœ:[œMessageœ]}-AstraDB-8CuoQ{œfieldNameœ:œsearch_queryœ,œidœ:œAstraDB-8CuoQœ,œinputTypesœ:[œMessageœ],œtypeœ:œstrœ}", "selected": false, "source": "ChatInput-5reba", "sourceHandle": "{œdataTypeœ: œChatInputœ, œidœ: œChatInput-5rebaœ, œnameœ: œmessageœ, œoutput_typesœ: [œMessageœ]}", "target": "AstraDB-8CuoQ", "targetHandle": "{œfieldNameœ: œsearch_queryœ, œidœ: œAstraDB-8CuoQœ, œinputTypesœ: [œMessageœ], œtypeœ: œstrœ}"}, {"animated": false, "className": "", "data": {"sourceHandle": {"dataType": "AstraDB", "id": "AstraDB-8CuoQ", "name": "search_results", "output_types": ["Data"]}, "targetHandle": {"fieldName": "data", "id": "ParseData-Pc764", "inputTypes": ["Data"], "type": "other"}}, "id": "reactflow__edge-AstraDB-8CuoQ{œdataTypeœ:œAstraDBœ,œidœ:œAstraDB-8CuoQœ,œnameœ:œsearch_resultsœ,œoutput_typesœ:[œDataœ]}-ParseData-Pc764{œfieldNameœ:œdataœ,œidœ:œParseData-Pc764œ,œinputTypesœ:[œDataœ],œtypeœ:œotherœ}", "selected": false, "source": "AstraDB-8CuoQ", "sourceHandle": "{œdataTypeœ: œAstraDBœ, œidœ: œAstraDB-8CuoQœ, œnameœ: œsearch_resultsœ, œoutput_typesœ: [œDataœ]}", "target": "ParseData-Pc764", "targetHandle": "{œfieldNameœ: œdataœ, œidœ: œParseData-Pc764œ, œinputTypesœ: [œDataœ], œtypeœ: œotherœ}"}], "nodes": [{"data": {"description": "从练习场获取聊天输入。", "display_name": "聊天输入", "id": "ChatInput-5reba", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "从练习场获取聊天输入。", "display_name": "聊天输入", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "files"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data.utils import IMG_FILE_TYPES, TEXT_FILE_TYPES\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.io import (\n    DropdownInput,\n    FileInput,\n    MessageTextInput,\n    MultilineInput,\n    Output,\n)\nfrom langflow.schema.message import Message\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_USER,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"聊天输入\"  # \"Chat Input\"\n    description = \"从练习场获取聊天输入，仅支持上传图片解析。\"  # \"Get chat inputs from the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatInput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        MultilineInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            value=\"\",\n            info=\"作为输入传递的消息。\",  # \"Message to be passed as input.\"\n            input_types=[],\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_USER,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_USER,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        FileInput(\n            name=\"files\",  # \"files\"\n            display_name=\"文件\",  # \"Files\"\n            file_types=TEXT_FILE_TYPES + IMG_FILE_TYPES,\n            info=\"随消息发送的文件。\",  # \"Files to be sent with the message.\"\n            advanced=True,\n            is_list=True,\n            temp_file=True,\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(display_name=\"消息\", name=\"message\", method=\"message_response\"),  # \"Message\"\n    ]\n\n    async def message_response(self) -> Message:\n        background_color = self.background_color\n        text_color = self.text_color\n        icon = self.chat_icon\n\n        message = await Message.create(\n            text=self.input_value,\n            sender=self.sender,\n            sender_name=self.sender_name,\n            session_id=self.session_id,\n            files=self.files,\n            properties={\n                \"background_color\": background_color,\n                \"text_color\": text_color,\n                \"icon\": icon,\n            },\n        )\n        if self.session_id and isinstance(message, Message) and self.should_store_message:\n            stored_message = await self.send_message(\n                message,\n            )\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n"}, "files": {"advanced": true, "display_name": "文件", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "xlsx", "xls", "jpg", "jpeg", "png", "bmp", "image"], "file_path": "", "info": "随消息发送的文件。", "list": true, "name": "files", "placeholder": "", "required": false, "show": true, "temp_file": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "input_value": {"advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输入传递的消息。", "input_types": [], "list": false, "load_from_db": false, "multiline": true, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "What is the document is about?"}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "User"}, "sender_name": {"advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "User"}, "session_id": {"advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}}, "type": "ChatInput"}, "dragging": false, "height": 234, "id": "ChatInput-5reba", "measured": {"height": 234, "width": 320}, "position": {"x": 827.4877596995269, "y": 421.8759496538444}, "positionAbsolute": {"x": 743.9745420290319, "y": 463.6977510207854}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "将数据转换为纯文本，遵循指定的模板。", "display_name": "解析数据", "id": "ParseData-Pc764", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "将数据转换为纯文本，遵循指定的模板。", "display_name": "解析数据", "documentation": "", "edited": false, "field_order": ["data", "template", "sep"], "frozen": false, "icon": "message-square", "legacy": false, "lf_version": "1.1.1", "metadata": {"legacy_name": "解析数据"}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "parse_data", "name": "text", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据列表", "method": "parse_data_as_list", "name": "data_list", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.custom import Component\nfrom langflow.helpers.data import data_to_text, data_to_text_list\nfrom langflow.io import DataInput, MultilineInput, Output, StrInput\nfrom langflow.schema import Data\nfrom langflow.schema.message import Message\n\n\nclass ParseDataComponent(Component):\n    display_name = \"数据转消息\"  # \"Data to Message\"\n    description = \"使用输入数据中的任意 {字段} 将 Data 对象转换为消息。\"  # \"Convert Data objects into Messages using any {field_name} from input data.\"\n    icon = \"message-square\"\n    name = \"ParseData\"\n    metadata = {\n        \"legacy_name\": \"解析数据\",  # \"Parse Data\"\n    }\n\n    inputs = [\n        DataInput(\n            name=\"data\",\n            display_name=\"数据\",  # \"Data\"\n            info=\"要转换为文本的数据。\",  # \"The data to convert to text.\"\n            is_list=True,\n            required=True,\n        ),\n        MultilineInput(\n            name=\"template\",\n            display_name=\"模板\",  # \"Template\"\n            info=(\n                \"用于格式化数据的模板。\"  # \"The template to use for formatting the data. \"\n\"它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。\"  # \"It can contain the keys {text}, {data} or any other key in the Data.\"\n            ),\n            value=\"{文本}\",\n            required=True,\n        ),\n        StrInput(\nname=\"sep\",\ndisplay_name=\"分隔符\",  # \"Separator\"\nadvanced=True,\nvalue=\"\\n\",\n),\n    ]\n\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"text\",\n            info=\"数据作为单个消息，每个输入数据由分隔符分隔。\",  # \"Data as a single Message, with each input Data separated by Separator.\"\n            method=\"parse_data\",\n        ),\n        Output(\n            display_name=\"数据列表\",  # \"Data List\"\n            name=\"data_list\",\n            info=\"数据作为新数据的列表，每个数据的 `text` 由模板格式化。\",  # \"Data as a list of new Data, each having `text` formatted by Template.\"\n            method=\"parse_data_as_list\",\n        ),\n    ]\n\n    def _clean_args(self) -> tuple[list[Data], str, str]:\n        data = self.data if isinstance(self.data, list) else [self.data]\n        template = self.template\n        sep = self.sep\n        return data, template, sep\n\n    def parse_data(self) -> Message:\n        data, template, sep = self._clean_args()\n        result_string = data_to_text(template, data, sep)\n        self.status = result_string\n        return Message(text=result_string)\n\n    def parse_data_as_list(self) -> list[Data]:\n        data, template, _ = self._clean_args()\n        text_list, data_list = data_to_text_list(template, data)\n        for item, text in zip(data_list, text_list, strict=True):\n            item.set_text(text)\n        self.status = data_list\n        return data_list\n"}, "data": {"advanced": false, "display_name": "数据", "dynamic": false, "info": "要转换为文本的数据。", "input_types": ["Data"], "list": true, "name": "data", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "sep": {"advanced": true, "display_name": "分隔符", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "sep", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "str", "value": "\n"}, "template": {"advanced": false, "display_name": "模板", "dynamic": false, "info": "用于格式化数据的模板。它可以包含键 {文本}、{数据} 或 Data 中的任何其他键。", "input_types": ["Message"], "list": false, "load_from_db": false, "multiline": true, "name": "template", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}}}, "type": "ParseData"}, "dragging": false, "height": 350, "id": "ParseData-Pc764", "measured": {"height": 350, "width": 320}, "position": {"x": 1606.0595305373527, "y": 751.4473696960695}, "positionAbsolute": {"x": 1606.0595305373527, "y": 751.4473696960695}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "创建带有动态变量的提示模板。", "display_name": "提示", "id": "Prompt-T1JLQ", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {"template": ["context", "question"]}, "description": "创建带有动态变量的提示模板。", "display_name": "提示", "documentation": "", "edited": false, "error": null, "field_order": ["template"], "frozen": false, "full_path": null, "icon": "prompts", "is_composition": null, "is_input": null, "is_output": null, "legacy": false, "lf_version": "1.1.1", "metadata": {}, "name": "", "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "提示消息", "method": "build_prompt", "name": "prompt", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.prompts.api_utils import process_prompt_template\nfrom langflow.custom import Component\nfrom langflow.inputs.inputs import Defa<PERSON><PERSON><PERSON>pt<PERSON><PERSON>\nfrom langflow.io import MessageTextInput, Output, PromptInput\nfrom langflow.schema.message import Message\nfrom langflow.template.utils import update_template_values\n\n\nclass PromptComponent(Component):\n    display_name: str = \"提示模板\"  # \"Prompt\"\n    description: str = \"创建带有动态变量的提示模板。\"  # \"Create a prompt template with dynamic variables.\"\n    icon = \"prompts\"  # \"\"\n    trace_type = \"prompt\"  # \"prompt\"\n    name = \"Prompt\"  # \"\"\n\n    inputs = [\n        PromptInput(name=\"template\", display_name=\"模板\"),  # \"Template\"\n        MessageTextInput(\n            name=\"tool_placeholder\",  # \"tool_placeholder\"\n            display_name=\"工具占位符\",  # \"Tool Placeholder\"\n            tool_mode=True,\n            advanced=True,\n            info=\"工具模式的占位符输入。\",  # \"A placeholder input for tool mode.\"\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"提示消息\", name=\"prompt\", method=\"build_prompt\"),  # \"Prompt Message\"\n    ]\n\n    async def build_prompt(self) -> Message:\n        prompt = Message.from_template(**self._attributes)\n        self.status = prompt.text\n        return prompt\n\n    def _update_template(self, frontend_node: dict):\n        prompt_template = frontend_node[\"template\"][\"template\"][\"value\"]\n        custom_fields = frontend_node[\"custom_fields\"]\n        frontend_node_template = frontend_node[\"template\"]\n        _ = process_prompt_template(\n            template=prompt_template,\n            name=\"template\",\n            custom_fields=custom_fields,\n            frontend_node_template=frontend_node_template,\n        )\n        return frontend_node\n\n    async def update_frontend_node(self, new_frontend_node: dict, current_frontend_node: dict):\n        \"\"\"This function is called after the code validation is done.\"\"\"\n        frontend_node = await super().update_frontend_node(new_frontend_node, current_frontend_node)\n        template = frontend_node[\"template\"][\"template\"][\"value\"]\n        # Kept it duplicated for backwards compatibility\n        _ = process_prompt_template(\n            template=template,\n            name=\"template\",\n            custom_fields=frontend_node[\"custom_fields\"],\n            frontend_node_template=frontend_node[\"template\"],\n        )\n        # Now that template is updated, we need to grab any values that were set in the current_frontend_node\n        # and update the frontend_node with those values\n        update_template_values(new_template=frontend_node, previous_template=current_frontend_node[\"template\"])\n        return frontend_node\n\n    def _get_fallback_input(self, **kwargs):\n        return DefaultPromptField(**kwargs)\n"}, "context": {"advanced": false, "display_name": "context", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "context", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "question": {"advanced": false, "display_name": "question", "dynamic": false, "field_type": "str", "fileTypes": [], "file_path": "", "info": "", "input_types": ["Message", "Text"], "list": false, "load_from_db": false, "multiline": true, "name": "question", "placeholder": "", "required": false, "show": true, "title_case": false, "type": "str", "value": ""}, "template": {"advanced": false, "display_name": "模板", "dynamic": false, "info": "", "list": false, "load_from_db": false, "name": "template", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "prompt", "value": "{context}\n\n---\n\nGiven the context above, answer the question as best as possible.\n\nQuestion: {question}\n\nAnswer: "}, "tool_placeholder": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "工具占位符", "dynamic": false, "info": "工具模式的占位符输入。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tool_placeholder", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "Prompt"}, "dragging": false, "height": 433, "id": "Prompt-T1JLQ", "measured": {"height": 433, "width": 320}, "position": {"x": 1977.9097981422992, "y": 640.5656416923846}, "positionAbsolute": {"x": 1977.9097981422992, "y": 640.5656416923846}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"description": "根据指定条件将文本拆分为块。", "display_name": "拆分文本", "id": "SplitText-EixdW", "node": {"base_classes": ["Data"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "根据指定条件将文本拆分为块。", "display_name": "拆分文本", "documentation": "", "edited": false, "field_order": ["data_inputs", "chunk_overlap", "chunk_size", "separator"], "frozen": false, "icon": "scissors-line-dashed", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "块", "method": "split_text", "name": "chunks", "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据表", "method": "as_dataframe", "name": "dataframe", "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "chunk_overlap": {"advanced": false, "display_name": "块重叠", "dynamic": false, "info": "块之间重叠的字符数。", "list": false, "name": "chunk_overlap", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 200}, "chunk_size": {"advanced": false, "display_name": "块大小", "dynamic": false, "info": "每个块的最大长度。文本首先按分隔符拆分，然后将块合并到此大小。单个拆分大于此大小的不会进一步拆分。", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_text_splitters import CharacterTextSplitter\n\nfrom langflow.custom import Component\nfrom langflow.io import DropdownInput, HandleInput, IntInput, MessageTextInput, Output\nfrom langflow.schema import Data, DataFrame\nfrom langflow.utils.util import unescape_string\n\n\nclass SplitTextComponent(Component):\n    display_name: str = \"拆分文本\"  # \"Split Text\"\n    description: str = \"根据指定的条件将文本拆分为块。\"  # \"Split text into chunks based on specified criteria.\"\n    icon = \"scissors-line-dashed\"\n    name = \"SplitText\"\n\n    inputs = [\n        HandleInput(\n            name=\"data_inputs\",\n            display_name=\"数据或数据表\",  # \"Data or DataFrame\"\n            info=\"包含要拆分为块的文本的数据。\",  # \"The data with texts to split in chunks.\"\n            input_types=[\"Data\", \"DataFrame\"],\n            required=True,\n        ),\n        IntInput(\n            name=\"chunk_overlap\",\n            display_name=\"块重叠\",  # \"Chunk Overlap\"\n            info=\"块之间重叠的字符数。\",  # \"Number of characters to overlap between chunks.\"\n            value=200,\n        ),\n        IntInput(\n            name=\"chunk_size\",\n            display_name=\"块大小\",  # \"Chunk Size\"\n            info=(\n\"每个块的最大长度。文本首先按分隔符拆分，\"  # \"The maximum length of each chunk. Text is first split by separator, \"\n\"然后将块合并到此大小。\"  # \"then chunks are merged up to this size. \"\n\"单个拆分大于此大小的不会进一步拆分。\"  # \"Individual splits larger than this won't be further divided.\"\n            ),\n            value=1000,\n        ),\n        MessageTextInput(\n            name=\"separator\",\n            display_name=\"分隔符\",  # \"Separator\"\n            info=(\n\"用于拆分的字符。使用 \\\\n 表示换行符。\"  # \"The character to split on. Use \\\\n for newline. \"\n\"示例：\\\\n\\\\n 表示段落，\\\\n 表示行，. 表示句子。\"  # \"Examples: \\\\n\\\\n for paragraphs, \\\\n for lines, . for sentences.\"\n            ),\n            value=\"\\n\",\n        ),\n        MessageTextInput(\n            name=\"text_key\",\n            display_name=\"文本键\",  # \"Text Key\"\n            info=\"用于文本列的键。\",  # \"The key to use for the text column.\"\n            value=\"text\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"keep_separator\",\n            display_name=\"保留分隔符\",  # \"Keep Separator\"\n            info=\"是否在输出块中保留分隔符以及将其放置的位置。\",  # \"Whether to keep the separator in the output chunks and where to place it.\"\n            options=[\"False\", \"True\", \"Start\", \"End\"],\n            value=\"False\",\n            advanced=True,\n        ),\n    ]\n\n    outputs = [\n        Output(display_name=\"块\", name=\"chunks\", method=\"split_text\"),  # \"Chunks\"\n        Output(display_name=\"数据表\", name=\"dataframe\", method=\"as_dataframe\"),  # \"DataFrame\"\n    ]\n\n    def _docs_to_data(self, docs) -> list[Data]:\n        return [Data(text=doc.page_content, data=doc.metadata) for doc in docs]\n\n    def _fix_separator(self, separator: str) -> str:\n        \"\"\"Fix common separator issues and convert to proper format.\"\"\"\n        if separator == \"/n\":\n            return \"\\n\"\n        if separator == \"/t\":\n            return \"\\t\"\n        return separator\n\n    def split_text_base(self):\n        separator = self._fix_separator(self.separator)\n        separator = unescape_string(separator)\n\n        if isinstance(self.data_inputs, DataFrame):\n            if not len(self.data_inputs):\n                msg = \"数据表为空\"  # \"DataFrame is empty\"\n                raise TypeError(msg)\n\n            self.data_inputs.text_key = self.text_key\n            try:\n                documents = self.data_inputs.to_lc_documents()\n            except Exception as e:\n                msg = f\"将数据表转换为文档时出错：{e}\"  # \"Error converting DataFrame to documents: {e}\"\n                raise TypeError(msg) from e\n        else:\n            if not self.data_inputs:\n                msg = \"未提供数据输入\"  # \"No data inputs provided\"\n                raise TypeError(msg)\n\n            documents = []\n            if isinstance(self.data_inputs, Data):\n                self.data_inputs.text_key = self.text_key\n                documents = [self.data_inputs.to_lc_document()]\n            else:\n                try:\n                    documents = [input_.to_lc_document() for input_ in self.data_inputs if isinstance(input_, Data)]\n                    if not documents:\n                        msg = f\"在 {type(self.data_inputs)} 中未找到有效的数据输入\"  # \"No valid Data inputs found in {type(self.data_inputs)}\"\n                        raise TypeError(msg)\n                except AttributeError as e:\n                    msg = f\"集合中的输入类型无效：{e}\"  # \"Invalid input type in collection: {e}\"\n                    raise TypeError(msg) from e\n        try:\n            # Convert string 'False'/'True' to boolean\n            keep_sep = self.keep_separator\n            if isinstance(keep_sep, str):\n                if keep_sep.lower() == \"false\":\n                    keep_sep = False\n                elif keep_sep.lower() == \"true\":\n                    keep_sep = True\n                # 'start' and 'end' are kept as strings\n\n            splitter = CharacterTextSplitter(\n                chunk_overlap=self.chunk_overlap,\n                chunk_size=self.chunk_size,\n                separator=separator,\n                keep_separator=keep_sep,\n            )\n            return splitter.split_documents(documents)\n        except Exception as e:\n            msg = f\"拆分文本时出错：{e}\"  # \"Error splitting text: {e}\"\n            raise TypeError(msg) from e\n\n    def split_text(self) -> list[Data]:\n        return self._docs_to_data(self.split_text_base())\n\n    def as_dataframe(self) -> DataFrame:\n        return DataFrame(self.split_text())\n"}, "data_inputs": {"advanced": false, "display_name": "数据或数据表", "dynamic": false, "info": "包含要拆分为块的文本的数据。", "input_types": ["Data", "DataFrame"], "list": false, "name": "data_inputs", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "keep_separator": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "保留分隔符", "dynamic": false, "info": "是否在输出块中保留分隔符以及将其放置的位置。", "name": "keep_separator", "options": ["False", "True", "Start", "End"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "False"}, "separator": {"advanced": false, "display_name": "分隔符", "dynamic": false, "info": "用于拆分的字符。使用 \\n 表示换行符。示例：\\n\\n 表示段落，\\n 表示行，. 表示句子。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "separator", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "\n"}, "text_key": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本键", "dynamic": false, "info": "用于文本列的键。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "text_key", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "text"}}}, "type": "SplitText"}, "dragging": false, "height": 475, "id": "SplitText-EixdW", "measured": {"height": 475, "width": 320}, "position": {"x": 1692.461995335383, "y": 1328.2681481569232}, "positionAbsolute": {"x": 1683.4543896546102, "y": 1350.7871623588553}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-En1Ka", "node": {"description": "## 🐕 2. Retriever Flow\n\nThis flow answers your questions with contextual data retrieved from your vector database.\n\nOpen the **Playground** and ask, \n\n```\nWhat is this document about?\n```\n", "display_name": "", "documentation": "", "template": {"backgroundColor": "neutral"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-En1Ka", "measured": {"height": 324, "width": 325}, "position": {"x": 374.388314931542, "y": 486.18094072679895}, "positionAbsolute": {"x": 374.388314931542, "y": 486.18094072679895}, "resizing": false, "selected": false, "style": {"height": 324, "width": 324}, "type": "noteNode", "width": 324}, {"data": {"id": "note-SHxvt", "node": {"description": "## 📖 自述文件\n\n通过 📚 **加载数据** 流将您的数据加载到向量数据库中，然后使用 🐕 **检索器** 流将您的数据用作聊天上下文。\n\n**🚨 将您的 OpenAI API 密钥添加为全局变量，以便轻松将其添加到此流中的所有 OpenAI 组件中。**\n\n**快速开始**\n1. 运行 📚 **加载数据** 流。\n2. 运行 🐕 **检索器** 流。\n\n**下一步**\n\n- 通过更改提示和加载的数据进行实验，观察机器人的响应如何变化。\n\n有关更多信息，请参阅 [Langflow 文档](https://docs.langflow.org/starter-projects-vector-store-rag)。", "display_name": "自述文件", "documentation": "", "template": {"backgroundColor": "neutral"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-SHxvt", "measured": {"height": 324, "width": 325}, "position": {"x": 191.12162720143235, "y": 1157.6038620251531}, "positionAbsolute": {"x": 94.28986613312418, "y": 907.6428043837066}, "resizing": false, "selected": true, "style": {"height": 324, "width": 324}, "type": "noteNode", "width": 324}, {"data": {"description": "在练习场中显示聊天消息。", "display_name": "聊天输出", "id": "ChatOutput-aQXd2", "node": {"base_classes": ["Message"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "在练习场中显示聊天消息。", "display_name": "聊天输出", "documentation": "", "edited": false, "field_order": ["input_value", "should_store_message", "sender", "sender_name", "session_id", "data_template", "background_color", "chat_icon", "text_color"], "frozen": false, "icon": "MessagesSquare", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "message_response", "name": "message", "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "background_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "背景颜色", "dynamic": false, "info": "图标的背景颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "background_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "chat_icon": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "图标", "dynamic": false, "info": "消息的图标。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "chat_icon", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "clean_data": {"_input_type": "BoolInput", "advanced": true, "display_name": "基本清理数据", "dynamic": false, "info": "是否清理数据。", "list": false, "list_add_label": "Add More", "name": "clean_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from collections.abc import Generator\nfrom typing import Any\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.inputs import BoolInput\nfrom langflow.inputs.inputs import HandleInput\nfrom langflow.io import DropdownInput, MessageTextInput, Output\nfrom langflow.schema.data import Data\nfrom langflow.schema.dataframe import Data<PERSON>rame\nfrom langflow.schema.message import Message\nfrom langflow.schema.properties import Source\nfrom langflow.utils.constants import (\n    MESSAGE_SENDER_AI,\n    MESSAGE_SENDER_NAME_AI,\n    MESSAGE_SENDER_USER,\n)\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"聊天输出\"  # \"Chat Output\"\n    description = \"在练习场中显示聊天消息。\"  # \"Display a chat message in the Playground.\"\n    icon = \"MessagesSquare\"  # \"消息方块\"\n    name = \"ChatOutput\"  # \"\"\n    minimized = True\n\n    inputs = [\n        HandleInput(\n            name=\"input_value\",  # \"input_value\"\n            display_name=\"文本\",  # \"Text\"\n            info=\"作为输出传递的消息。\",  # \"Message to be passed as output.\"\n            input_types=[\"Data\", \"DataFrame\", \"Message\"],\n            required=True,\n        ),\n        BoolInput(\n            name=\"should_store_message\",  # \"should_store_message\"\n            display_name=\"存储消息\",  # \"Store Messages\"\n            info=\"将消息存储在历史记录中。\",  # \"Store the message in the history.\"\n            value=True,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"sender\",  # \"sender\"\n            display_name=\"发送者类型\",  # \"Sender Type\"\n            options=[MESSAGE_SENDER_AI, MESSAGE_SENDER_USER],\n            value=MESSAGE_SENDER_AI,\n            advanced=True,\n            info=\"发送者的类型。\",  # \"Type of sender.\"\n        ),\n        MessageTextInput(\n            name=\"sender_name\",  # \"sender_name\"\n            display_name=\"发送者名称\",  # \"Sender Name\"\n            info=\"发送者的名称。\",  # \"Name of the sender.\"\n            value=MESSAGE_SENDER_NAME_AI,\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"session_id\",  # \"session_id\"\n            display_name=\"会话 ID\",  # \"Session ID\"\n            info=\"聊天的会话 ID。如果为空，将使用当前会话 ID 参数。\",  # \"The session ID of the chat. If empty, the current session ID parameter will be used.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"data_template\",  # \"data_template\"\n            display_name=\"数据模板\",  # \"Data Template\"\n            value=\"{text}\",\n            advanced=True,\n            info=\"用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。\",  # \"Template to convert Data to Text. If left empty, it will be dynamically set to the Data's text key.\"\n        ),\n        MessageTextInput(\n            name=\"background_color\",  # \"background_color\"\n            display_name=\"背景颜色\",  # \"Background Color\"\n            info=\"图标的背景颜色。\",  # \"The background color of the icon.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"chat_icon\",  # \"chat_icon\"\n            display_name=\"图标\",  # \"Icon\"\n            info=\"消息的图标。\",  # \"The icon of the message.\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"text_color\",  # \"text_color\"\n            display_name=\"文本颜色\",  # \"Text Color\"\n            info=\"名称的文本颜色。\",  # \"The text color of the name.\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"clean_data\",  # \"clean_data\"\n            display_name=\"基本清理数据\",  # \"Basic Clean Data\"\n            value=True,\n            info=\"是否清理数据。\",  # \"Whether to clean the data.\"\n            advanced=True,\n        ),\n    ]\n    outputs = [\n        Output(\n            display_name=\"消息\",  # \"Message\"\n            name=\"message\",\n            method=\"message_response\",\n        ),\n    ]\n\n    def _build_source(self, id_: str | None, display_name: str | None, source: str | None) -> Source:\n        source_dict = {}\n        if id_:\n            source_dict[\"id\"] = id_\n        if display_name:\n            source_dict[\"display_name\"] = display_name\n        if source:\n            # Handle case where source is a ChatOpenAI object\n            if hasattr(source, \"model_name\"):\n                source_dict[\"source\"] = source.model_name\n            elif hasattr(source, \"model\"):\n                source_dict[\"source\"] = str(source.model)\n            else:\n                source_dict[\"source\"] = str(source)\n        return Source(**source_dict)\n\n    async def message_response(self) -> Message:\n        # First convert the input to string if needed\n        text = self.convert_to_string()\n        # Get source properties\n        source, icon, display_name, source_id = self.get_properties_from_source_component()\n        background_color = self.background_color\n        text_color = self.text_color\n        if self.chat_icon:\n            icon = self.chat_icon\n\n        # Create or use existing Message object\n        if isinstance(self.input_value, Message):\n            message = self.input_value\n            # Update message properties\n            message.text = text\n        else:\n            message = Message(text=text)\n\n        # Set message properties\n        message.sender = self.sender\n        message.sender_name = self.sender_name\n        message.session_id = self.session_id\n        message.flow_id = self.graph.flow_id if hasattr(self, \"graph\") else None\n        message.properties.source = self._build_source(source_id, display_name, source)\n        message.properties.icon = icon\n        message.properties.background_color = background_color\n        message.properties.text_color = text_color\n\n        # Store message if needed\n        if self.session_id and self.should_store_message:\n            stored_message = await self.send_message(message)\n            self.message.value = stored_message\n            message = stored_message\n\n        self.status = message\n        return message\n\n    def _validate_input(self) -> None:\n        \"\"\"Validate the input data and raise ValueError if invalid.\"\"\"\n        if self.input_value is None:\n            msg = \"Input data cannot be None\"\n            raise ValueError(msg)\n        if isinstance(self.input_value, list) and not all(\n            isinstance(item, Message | Data | DataFrame | str) for item in self.input_value\n        ):\n            invalid_types = [\n                type(item).__name__\n                for item in self.input_value\n                if not isinstance(item, Message | Data | DataFrame | str)\n            ]\n            msg = f\"Expected Data or DataFrame or Message or str, got {invalid_types}\"\n            raise TypeError(msg)\n        if not isinstance(\n            self.input_value,\n            Message | Data | DataFrame | str | list | Generator | type(None),\n        ):\n            type_name = type(self.input_value).__name__\n            msg = f\"Expected Data or DataFrame or Message or str, Generator or None, got {type_name}\"\n            raise TypeError(msg)\n\n    def _safe_convert(self, data: Any) -> str:\n        \"\"\"Safely convert input data to string.\"\"\"\n        try:\n            if isinstance(data, str):\n                return data\n            if isinstance(data, Message):\n                return data.get_text()\n            if isinstance(data, Data):\n                if data.get_text() is None:\n                    msg = \"Empty Data object\"\n                    raise ValueError(msg)\n                return data.get_text()\n            if isinstance(data, DataFrame):\n                if self.clean_data:\n                    # Remove empty rows\n                    data = data.dropna(how=\"all\")\n                    # Remove empty lines in each cell\n                    data = data.replace(r\"^\\s*$\", \"\", regex=True)\n                    # Replace multiple newlines with a single newline\n                    data = data.replace(r\"\\n+\", \"\\n\", regex=True)\n\n                # Replace pipe characters to avoid markdown table issues\n                processed_data = data.replace(r\"\\|\", r\"\\\\|\", regex=True)\n\n                processed_data = processed_data.map(\n                    lambda x: str(x).replace(\"\\n\", \"<br/>\") if isinstance(x, str) else x\n                )\n\n                return processed_data.to_markdown(index=False)\n            return str(data)\n        except (ValueError, TypeError, AttributeError) as e:\n            msg = f\"Error converting data: {e!s}\"\n            raise ValueError(msg) from e\n\n    def convert_to_string(self) -> str | Generator[Any, None, None]:\n        \"\"\"Convert input data to string with proper error handling.\"\"\"\n        self._validate_input()\n        if isinstance(self.input_value, list):\n            return \"\\n\".join([self._safe_convert(item) for item in self.input_value])\n        if isinstance(self.input_value, Generator):\n            return self.input_value\n        return self._safe_convert(self.input_value)\n"}, "data_template": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "数据模板", "dynamic": false, "info": "用于将数据转换为文本的模板。如果留空，将动态设置为数据的文本键。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "data_template", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "{text}"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "文本", "dynamic": false, "info": "作为输出传递的消息。", "input_types": ["Data", "DataFrame", "Message"], "list": false, "load_from_db": false, "name": "input_value", "placeholder": "", "required": true, "show": true, "title_case": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "sender": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "发送者类型", "dynamic": false, "info": "发送者的类型。", "name": "sender", "options": ["Machine", "User"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Machine"}, "sender_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "发送者名称", "dynamic": false, "info": "发送者的名称。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "sender_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": "AI"}, "session_id": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "会话 ID", "dynamic": false, "info": "聊天的会话 ID。如果为空，将使用当前会话 ID 参数。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "session_id", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "should_store_message": {"_input_type": "BoolInput", "advanced": true, "display_name": "存储消息", "dynamic": false, "info": "将消息存储在历史记录中。", "list": false, "name": "should_store_message", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "text_color": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "文本颜色", "dynamic": false, "info": "名称的文本颜色。", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "text_color", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "ChatOutput"}, "dragging": false, "height": 234, "id": "ChatOutput-aQXd2", "measured": {"height": 234, "width": 320}, "position": {"x": 2738.611008351098, "y": 829.6219994149209}, "positionAbsolute": {"x": 2734.385670401691, "y": 810.6079786425926}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "OpenAIEmbeddings-t4aMI", "node": {"base_classes": ["Embeddings"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "使用 OpenAI 模型生成嵌入向量。", "display_name": "OpenAI 嵌入", "documentation": "", "edited": false, "field_order": ["default_headers", "default_query", "chunk_size", "client", "deployment", "embedding_ctx_length", "max_retries", "model", "model_kwargs", "openai_api_key", "openai_api_base", "openai_api_type", "openai_api_version", "openai_organization", "openai_proxy", "request_timeout", "show_progress_bar", "skip_empty", "tiktoken_model_name", "tiktoken_enable", "dimensions"], "frozen": false, "icon": "OpenAI", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "嵌入向量", "method": "build_embeddings", "name": "embeddings", "required_inputs": ["openai_api_key"], "selected": "Embeddings", "tool_mode": true, "types": ["Embeddings"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "chunk_size": {"_input_type": "IntInput", "advanced": true, "display_name": "分块大小", "dynamic": false, "info": "", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "client": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "客户端", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "client", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import OpenAIEmbeddings\n\nfrom langflow.base.embeddings.model import LCEmbeddingsModel\nfrom langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES\nfrom langflow.field_typing import Embeddings\nfrom langflow.io import BoolInput, DictInput, DropdownInput, FloatInput, IntInput, MessageTextInput, SecretStrInput\n\n\nclass OpenAIEmbeddingsComponent(LCEmbeddingsModel):\n    display_name = \"OpenAI嵌入\"  # \"OpenAI Embeddings\"\n    description = \"使用 OpenAI 模型生成嵌入。\"  # \"Generate embeddings using OpenAI models.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIEmbeddings\"\n\n    inputs = [\n        DictInput(\n            name=\"default_headers\",\n            display_name=\"默认请求头\",  # \"Default Headers\"\n            advanced=True,\n            info=\"用于 API 请求的默认请求头。\",  # \"Default headers to use for the API request.\"\n        ),\n        DictInput(\n            name=\"default_query\",\n            display_name=\"默认查询参数\",  # \"Default Query\"\n            advanced=True,\n            info=\"用于 API 请求的默认查询参数。\",  # \"Default query parameters to use for the API request.\"\n        ),\n        IntInput(\n            name=\"chunk_size\",\n            display_name=\"分块大小\",  # \"Chunk Size\"\n            advanced=True,\n            value=1000,\n        ),\n        MessageTextInput(\n            name=\"client\",\n            display_name=\"客户端\",  # \"Client\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"deployment\",\n            display_name=\"部署\",  # \"Deployment\"\n            advanced=True,\n        ),\n        IntInput(\n            name=\"embedding_ctx_length\",\n            display_name=\"嵌入上下文长度\",  # \"Embedding Context Length\"\n            advanced=True,\n            value=1536,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # \"Max Retries\"\n            value=3,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"模型\",  # \"Model\"\n            advanced=False,\n            options=OPENAI_EMBEDDING_MODEL_NAMES,\n            value=\"text-embedding-3-small\",\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # \"Model Kwargs\"\n            advanced=True,\n        ),\n        SecretStrInput(\n            name=\"openai_api_key\",\n            display_name=\"OpenAI API 密钥\",  # \"OpenAI API Key\"\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础 URL\",  # \"OpenAI API Base\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_type\",\n            display_name=\"OpenAI API 类型\",  # \"OpenAI API Type\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_version\",\n            display_name=\"OpenAI API 版本\",  # \"OpenAI API Version\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_organization\",\n            display_name=\"OpenAI 组织\",  # \"OpenAI Organization\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_proxy\",\n            display_name=\"OpenAI 代理\",  # \"OpenAI Proxy\"\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"request_timeout\",\n            display_name=\"请求超时时间\",  # \"Request Timeout\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"show_progress_bar\",\n            display_name=\"显示进度条\",  # \"Show Progress Bar\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"skip_empty\",\n            display_name=\"跳过空值\",  # \"Skip Empty\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"tiktoken_model_name\",\n            display_name=\"TikToken 模型名称\",  # \"TikToken Model Name\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"tiktoken_enable\",\n            display_name=\"启用 TikToken\",  # \"TikToken Enable\"\n            advanced=True,\n            value=True,\n            info=\"如果为 False，则必须安装 transformers。\",  # \"If False, you must have transformers installed.\"\n        ),\n        IntInput(\n            name=\"dimensions\",\n            display_name=\"维度\",  # \"Dimensions\"\n            info=\"生成的嵌入输出的维度数量，仅某些模型支持。\",  # \"The number of dimensions the resulting output embeddings should have. Only supported by certain models.\"\n            advanced=True,\n        ),\n    ]\n\n    def build_embeddings(self) -> Embeddings:\n        return OpenAIEmbeddings(\n            client=self.client or None,\n            model=self.model,\n            dimensions=self.dimensions or None,\n            deployment=self.deployment or None,\n            api_version=self.openai_api_version or None,\n            base_url=self.openai_api_base or None,\n            openai_api_type=self.openai_api_type or None,\n            openai_proxy=self.openai_proxy or None,\n            embedding_ctx_length=self.embedding_ctx_length,\n            api_key=self.openai_api_key or None,\n            organization=self.openai_organization or None,\n            allowed_special=\"all\",\n            disallowed_special=\"all\",\n            chunk_size=self.chunk_size,\n            max_retries=self.max_retries,\n            timeout=self.request_timeout or None,\n            tiktoken_enabled=self.tiktoken_enable,\n            tiktoken_model_name=self.tiktoken_model_name or None,\n            show_progress_bar=self.show_progress_bar,\n            model_kwargs=self.model_kwargs,\n            skip_empty=self.skip_empty,\n            default_headers=self.default_headers or None,\n            default_query=self.default_query or None,\n        )\n"}, "default_headers": {"_input_type": "DictInput", "advanced": true, "display_name": "默认请求头", "dynamic": false, "info": "用于 API 请求的默认请求头。", "list": false, "name": "default_headers", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "default_query": {"_input_type": "DictInput", "advanced": true, "display_name": "默认查询参数", "dynamic": false, "info": "用于 API 请求的默认查询参数。", "list": false, "name": "default_query", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "deployment": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "部署", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "deployment", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "dimensions": {"_input_type": "IntInput", "advanced": true, "display_name": "维度", "dynamic": false, "info": "生成的嵌入输出的维度数量，仅某些模型支持。", "list": false, "name": "dimensions", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": ""}, "embedding_ctx_length": {"_input_type": "IntInput", "advanced": true, "display_name": "嵌入上下文长度", "dynamic": false, "info": "", "list": false, "name": "embedding_ctx_length", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1536}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "", "list": false, "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 3}, "model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "模型", "dynamic": false, "info": "", "name": "model", "options": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text-embedding-3-small"}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "", "list": false, "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "openai_api_base": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 基础 URL", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "", "input_types": ["Message"], "load_from_db": true, "name": "openai_api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "openai_api_type": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 类型", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_type", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_version": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 版本", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_version", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_organization": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 组织", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_organization", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_proxy": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 代理", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_proxy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "request_timeout": {"_input_type": "FloatInput", "advanced": true, "display_name": "请求超时时间", "dynamic": false, "info": "", "list": false, "name": "request_timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "float", "value": ""}, "show_progress_bar": {"_input_type": "BoolInput", "advanced": true, "display_name": "显示进度条", "dynamic": false, "info": "", "list": false, "name": "show_progress_bar", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "skip_empty": {"_input_type": "BoolInput", "advanced": true, "display_name": "跳过空值", "dynamic": false, "info": "", "list": false, "name": "skip_empty", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "tiktoken_enable": {"_input_type": "BoolInput", "advanced": true, "display_name": "启用 TikToken", "dynamic": false, "info": "如果为 False，则必须安装 transformers。", "list": false, "name": "tiktoken_enable", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "tiktoken_model_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "TikToken 模型名称", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tiktoken_model_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "OpenAIEmbeddings"}, "dragging": false, "height": 320, "id": "OpenAIEmbeddings-t4aMI", "measured": {"height": 320, "width": 320}, "position": {"x": 825.435626932521, "y": 739.6327999745448}, "positionAbsolute": {"x": 825.435626932521, "y": 739.6327999745448}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-PYicu", "node": {"description": "## 📚 1. Load Data Flow\n\nRun this first! Load data from a local file and embed it into the vector database.\n\nSelect a Database and a Collection, or create new ones. \n\nClick ▶️ **Run component** on the **Astra DB** component to load your data.\n\n* If you're using OSS Langflow, add your Astra DB Application Token to the Astra DB component.\n\n#### Next steps:\n Experiment by changing the prompt and the contextual data to see how the retrieval flow's responses change.", "display_name": "", "documentation": "", "template": {"backgroundColor": "neutral"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-PYicu", "measured": {"height": 324, "width": 325}, "position": {"x": 955.3277857006676, "y": 1552.171191793604}, "positionAbsolute": {"x": 955.3277857006676, "y": 1552.171191793604}, "resizing": false, "selected": false, "style": {"height": 324, "width": 324}, "type": "noteNode", "width": 324}, {"data": {"id": "OpenAIEmbeddings-fftff", "node": {"base_classes": ["Embeddings"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Generate embeddings using OpenAI models.", "display_name": "OpenAI 嵌入", "documentation": "", "edited": false, "field_order": ["default_headers", "default_query", "chunk_size", "client", "deployment", "embedding_ctx_length", "max_retries", "model", "model_kwargs", "openai_api_key", "openai_api_base", "openai_api_type", "openai_api_version", "openai_organization", "openai_proxy", "request_timeout", "show_progress_bar", "skip_empty", "tiktoken_model_name", "tiktoken_enable", "dimensions"], "frozen": false, "icon": "OpenAI", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "嵌入向量", "method": "build_embeddings", "name": "embeddings", "required_inputs": ["openai_api_key"], "selected": "Embeddings", "tool_mode": true, "types": ["Embeddings"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "chunk_size": {"_input_type": "IntInput", "advanced": true, "display_name": "分块大小", "dynamic": false, "info": "", "list": false, "name": "chunk_size", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1000}, "client": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "客户端", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "client", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import OpenAIEmbeddings\n\nfrom langflow.base.embeddings.model import LCEmbeddingsModel\nfrom langflow.base.models.openai_constants import OPENAI_EMBEDDING_MODEL_NAMES\nfrom langflow.field_typing import Embeddings\nfrom langflow.io import BoolInput, DictInput, DropdownInput, FloatInput, IntInput, MessageTextInput, SecretStrInput\n\n\nclass OpenAIEmbeddingsComponent(LCEmbeddingsModel):\n    display_name = \"OpenAI嵌入\"  # \"OpenAI Embeddings\"\n    description = \"使用 OpenAI 模型生成嵌入。\"  # \"Generate embeddings using OpenAI models.\"\n    icon = \"OpenAI\"\n    name = \"OpenAIEmbeddings\"\n\n    inputs = [\n        DictInput(\n            name=\"default_headers\",\n            display_name=\"默认请求头\",  # \"Default Headers\"\n            advanced=True,\n            info=\"用于 API 请求的默认请求头。\",  # \"Default headers to use for the API request.\"\n        ),\n        DictInput(\n            name=\"default_query\",\n            display_name=\"默认查询参数\",  # \"Default Query\"\n            advanced=True,\n            info=\"用于 API 请求的默认查询参数。\",  # \"Default query parameters to use for the API request.\"\n        ),\n        IntInput(\n            name=\"chunk_size\",\n            display_name=\"分块大小\",  # \"Chunk Size\"\n            advanced=True,\n            value=1000,\n        ),\n        MessageTextInput(\n            name=\"client\",\n            display_name=\"客户端\",  # \"Client\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"deployment\",\n            display_name=\"部署\",  # \"Deployment\"\n            advanced=True,\n        ),\n        IntInput(\n            name=\"embedding_ctx_length\",\n            display_name=\"嵌入上下文长度\",  # \"Embedding Context Length\"\n            advanced=True,\n            value=1536,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # \"Max Retries\"\n            value=3,\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"model\",\n            display_name=\"模型\",  # \"Model\"\n            advanced=False,\n            options=OPENAI_EMBEDDING_MODEL_NAMES,\n            value=\"text-embedding-3-small\",\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # \"Model Kwargs\"\n            advanced=True,\n        ),\n        SecretStrInput(\n            name=\"openai_api_key\",\n            display_name=\"OpenAI API 密钥\",  # \"OpenAI API Key\"\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础 URL\",  # \"OpenAI API Base\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_type\",\n            display_name=\"OpenAI API 类型\",  # \"OpenAI API Type\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_api_version\",\n            display_name=\"OpenAI API 版本\",  # \"OpenAI API Version\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_organization\",\n            display_name=\"OpenAI 组织\",  # \"OpenAI Organization\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"openai_proxy\",\n            display_name=\"OpenAI 代理\",  # \"OpenAI Proxy\"\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"request_timeout\",\n            display_name=\"请求超时时间\",  # \"Request Timeout\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"show_progress_bar\",\n            display_name=\"显示进度条\",  # \"Show Progress Bar\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"skip_empty\",\n            display_name=\"跳过空值\",  # \"Skip Empty\"\n            advanced=True,\n        ),\n        MessageTextInput(\n            name=\"tiktoken_model_name\",\n            display_name=\"TikToken 模型名称\",  # \"TikToken Model Name\"\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"tiktoken_enable\",\n            display_name=\"启用 TikToken\",  # \"TikToken Enable\"\n            advanced=True,\n            value=True,\n            info=\"如果为 False，则必须安装 transformers。\",  # \"If False, you must have transformers installed.\"\n        ),\n        IntInput(\n            name=\"dimensions\",\n            display_name=\"维度\",  # \"Dimensions\"\n            info=\"生成的嵌入输出的维度数量，仅某些模型支持。\",  # \"The number of dimensions the resulting output embeddings should have. Only supported by certain models.\"\n            advanced=True,\n        ),\n    ]\n\n    def build_embeddings(self) -> Embeddings:\n        return OpenAIEmbeddings(\n            client=self.client or None,\n            model=self.model,\n            dimensions=self.dimensions or None,\n            deployment=self.deployment or None,\n            api_version=self.openai_api_version or None,\n            base_url=self.openai_api_base or None,\n            openai_api_type=self.openai_api_type or None,\n            openai_proxy=self.openai_proxy or None,\n            embedding_ctx_length=self.embedding_ctx_length,\n            api_key=self.openai_api_key or None,\n            organization=self.openai_organization or None,\n            allowed_special=\"all\",\n            disallowed_special=\"all\",\n            chunk_size=self.chunk_size,\n            max_retries=self.max_retries,\n            timeout=self.request_timeout or None,\n            tiktoken_enabled=self.tiktoken_enable,\n            tiktoken_model_name=self.tiktoken_model_name or None,\n            show_progress_bar=self.show_progress_bar,\n            model_kwargs=self.model_kwargs,\n            skip_empty=self.skip_empty,\n            default_headers=self.default_headers or None,\n            default_query=self.default_query or None,\n        )\n"}, "default_headers": {"_input_type": "DictInput", "advanced": true, "display_name": "默认请求头", "dynamic": false, "info": "用于 API 请求的默认请求头。", "list": false, "name": "default_headers", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "default_query": {"_input_type": "DictInput", "advanced": true, "display_name": "默认查询参数", "dynamic": false, "info": "用于 API 请求的默认查询参数。", "list": false, "name": "default_query", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "deployment": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "部署", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "deployment", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "dimensions": {"_input_type": "IntInput", "advanced": true, "display_name": "维度", "dynamic": false, "info": "生成的嵌入输出的维度数量，仅某些模型支持。", "list": false, "name": "dimensions", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": ""}, "embedding_ctx_length": {"_input_type": "IntInput", "advanced": true, "display_name": "嵌入上下文长度", "dynamic": false, "info": "", "list": false, "name": "embedding_ctx_length", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 1536}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "", "list": false, "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 3}, "model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "模型", "dynamic": false, "info": "", "name": "model", "options": ["text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "text-embedding-3-small"}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "", "list": false, "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_input": true, "type": "dict", "value": {}}, "openai_api_base": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 基础 URL", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "", "input_types": ["Message"], "load_from_db": true, "name": "openai_api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "openai_api_type": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 类型", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_type", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_api_version": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI API 版本", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_api_version", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_organization": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 组织", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_organization", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "openai_proxy": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "OpenAI 代理", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "openai_proxy", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "request_timeout": {"_input_type": "FloatInput", "advanced": true, "display_name": "请求超时时间", "dynamic": false, "info": "", "list": false, "name": "request_timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "float", "value": ""}, "show_progress_bar": {"_input_type": "BoolInput", "advanced": true, "display_name": "显示进度条", "dynamic": false, "info": "", "list": false, "name": "show_progress_bar", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "skip_empty": {"_input_type": "BoolInput", "advanced": true, "display_name": "跳过空值", "dynamic": false, "info": "", "list": false, "name": "skip_empty", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "tiktoken_enable": {"_input_type": "BoolInput", "advanced": true, "display_name": "启用 TikToken", "dynamic": false, "info": "如果为 False，则必须安装 transformers。", "list": false, "name": "tiktoken_enable", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "tiktoken_model_name": {"_input_type": "MessageTextInput", "advanced": true, "display_name": "TikToken 模型名称", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "load_from_db": false, "name": "tiktoken_model_name", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}}, "tool_mode": false}, "type": "OpenAIEmbeddings"}, "dragging": false, "height": 320, "id": "OpenAIEmbeddings-fftff", "measured": {"height": 320, "width": 320}, "position": {"x": 1690.9220896443658, "y": 1866.483269483266}, "positionAbsolute": {"x": 1690.9220896443658, "y": 1866.483269483266}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "File-IwyQk", "node": {"base_classes": ["Data"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "加载一个文件以在您的项目中使用。", "display_name": "文件", "documentation": "", "edited": false, "field_order": ["path", "silent_errors", "use_multithreading", "concurrency_multithreading"], "frozen": false, "icon": "file-text", "legacy": false, "lf_version": "1.1.1", "metadata": {}, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "数据", "method": "load_files", "name": "data", "required_inputs": [], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据表", "method": "load_dataframe", "name": "dataframe", "required_inputs": [], "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "消息", "method": "load_message", "name": "message", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langflow.base.data import BaseFileComponent\nfrom langflow.base.data.utils import TEXT_FILE_TYPES, parallel_load_data, parse_text_file_to_data\nfrom langflow.io import BoolInput, IntInput\nfrom langflow.schema import Data\n\n\nclass FileComponent(BaseFileComponent):\n    \"\"\"Handles loading and processing of individual or zipped text files.\n\n    This component supports processing multiple valid files within a zip archive,\n    resolving paths, validating file types, and optionally using multithreading for processing.\n    \"\"\"\n\n    display_name = \"文件\"  # \"File\"\n    description = \"加载一个文件以在您的项目中使用。\"  # \"Load a file to be used in your project.\"\n    icon = \"file-text\"\n    name = \"File\"\n\n    VALID_EXTENSIONS = TEXT_FILE_TYPES\n\n    inputs = [\n        *BaseFileComponent._base_inputs,\n        BoolInput(\n            name=\"use_multithreading\",\n            display_name=\"[已弃用] 使用多线程\",  # \"[Deprecated] Use Multithreading\"\n            advanced=True,\n            value=True,\n            info=\"将“处理并发数”设置为大于 1 以启用多线程。\",  # \"Set 'Processing Concurrency' greater than 1 to enable multithreading.\"\n        ),\n        IntInput(\n            name=\"concurrency_multithreading\",\n            display_name=\"处理并发数\",  # \"Processing Concurrency\"\n            advanced=True,\n            info=\"当处理多个文件时，同时处理的文件数量。\",  # \"When multiple files are being processed, the number of files to process concurrently.\"\n            value=1,\n        ),\n    ]\n\n    outputs = [\n        *BaseFileComponent._base_outputs,\n    ]\n\n    def process_files(self, file_list: list[BaseFileComponent.BaseFile]) -> list[BaseFileComponent.BaseFile]:\n        \"\"\"Processes files either sequentially or in parallel, depending on concurrency settings.\n\n        Args:\n            file_list (list[BaseFileComponent.BaseFile]): List of files to process.\n\n        Returns:\n            list[BaseFileComponent.BaseFile]: Updated list of files with merged data.\n        \"\"\"\n\n        def process_file(file_path: str, *, silent_errors: bool = False) -> Data | None:\n            \"\"\"Processes a single file and returns its Data object.\"\"\"\n            try:\n                return parse_text_file_to_data(file_path, silent_errors=silent_errors)\n            except FileNotFoundError as e:\n                msg = f\"文件未找到: {file_path}。错误: {e}\"  # \"File not found: {file_path}. Error: {e}\"\n                self.log(msg)\n                if not silent_errors:\n                    raise\n                return None\n            except Exception as e:\n                msg = f\"处理 {file_path} 时发生意外错误: {e}\"  # \"Unexpected error processing {file_path}: {e}\"\n                self.log(msg)\n                if not silent_errors:\n                    raise\n                return None\n\n        if not file_list:\n            msg = \"没有要处理的文件。\"  # \"No files to process.\"\n            raise ValueError(msg)\n\n        concurrency = 1 if not self.use_multithreading else max(1, self.concurrency_multithreading)\n        file_count = len(file_list)\n\n        parallel_processing_threshold = 2\n        if concurrency < parallel_processing_threshold or file_count < parallel_processing_threshold:\n            if file_count > 1:\n                self.log(f\"顺序处理 {file_count} 个文件。\")  # \"Processing {file_count} files sequentially.\"\n            processed_data = [process_file(str(file.path), silent_errors=self.silent_errors) for file in file_list]\n        else:\n            self.log(f\"开始并行处理 {file_count} 个文件，并发数: {concurrency}。\")  # \"Starting parallel processing of {file_count} files with concurrency: {concurrency}.\"\n            file_paths = [str(file.path) for file in file_list]\n            processed_data = parallel_load_data(\n                file_paths,\n                silent_errors=self.silent_errors,\n                load_function=process_file,\n                max_concurrency=concurrency,\n            )\n\n        # Use rollup_basefile_data to merge processed data with BaseFile objects\n        return self.rollup_data(file_list, processed_data)\n"}, "concurrency_multithreading": {"_input_type": "IntInput", "advanced": true, "display_name": "处理并发数", "dynamic": false, "info": "当处理多个文件时，同时处理的文件数量。", "list": false, "name": "concurrency_multithreading", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "int", "value": 4}, "delete_server_file_after_processing": {"_input_type": "BoolInput", "advanced": true, "display_name": "处理后删除服务器文件", "dynamic": false, "info": "如果为 true，处理后将删除服务器文件路径。", "list": false, "name": "delete_server_file_after_processing", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "file_path": {"_input_type": "HandleInput", "advanced": true, "display_name": "服务器文件路径", "dynamic": false, "info": "包含 'file_path' 属性指向服务器文件的 Data 对象 或包含文件路径的 Message 对象。优先于 'Path'，但支持相同的文件类型。", "input_types": ["Data", "Message"], "list": true, "name": "file_path", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "ignore_unspecified_files": {"_input_type": "BoolInput", "advanced": true, "display_name": "忽略未指定的文件", "dynamic": false, "info": "如果为 true，将忽略没有 'file_path' 属性的 Data。", "list": false, "name": "ignore_unspecified_files", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "ignore_unsupported_extensions": {"_input_type": "BoolInput", "advanced": true, "display_name": "忽略不支持的扩展名", "dynamic": false, "info": "如果为 true，将不会处理具有不支持扩展名的文件。", "list": false, "name": "ignore_unsupported_extensions", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": true}, "path": {"_input_type": "FileInput", "advanced": false, "display_name": "文件", "dynamic": false, "fileTypes": ["txt", "md", "mdx", "csv", "json", "yaml", "yml", "xml", "html", "htm", "pdf", "docx", "py", "sh", "sql", "js", "ts", "tsx", "xlsx", "xls", "zip", "tar", "tgz", "bz2", "gz"], "file_path": [], "info": "支持的文件扩展名: txt, md, mdx, csv, json, yaml, yml, xml, html, htm, pdf, docx, py, sh, sql, js, ts, tsx, xlsx, xls; 可选的打包文件扩展名: zip, tar, tgz, bz2, gz", "list": true, "name": "path", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "file", "value": ""}, "separator": {"_input_type": "StrInput", "advanced": true, "display_name": "分隔符", "dynamic": false, "info": "指定在 Message 格式中多个输出之间使用的分隔符。", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "separator", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "\n\n"}, "silent_errors": {"_input_type": "BoolInput", "advanced": true, "display_name": "静默错误", "dynamic": false, "info": "如果为 true，错误将不会引发异常。", "list": false, "name": "silent_errors", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}, "use_multithreading": {"_input_type": "BoolInput", "advanced": true, "display_name": "[已弃用] 使用多线程", "dynamic": false, "info": "将“处理并发数”设置为大于 1 以启用多线程。", "list": false, "name": "use_multithreading", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "bool", "value": false}}, "tool_mode": false}, "type": "File"}, "dragging": false, "height": 367, "id": "File-IwyQk", "measured": {"height": 367, "width": 320}, "position": {"x": 1314.0643184619548, "y": 1508.2155181023356}, "positionAbsolute": {"x": 1318.9043936921921, "y": 1484.0151419511485}, "selected": false, "type": "genericNode", "width": 320}, {"data": {"id": "note-F2ZXQ", "node": {"description": "### 💡 Add your OpenAI API key here 👇", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-F2ZXQ", "measured": {"height": 324, "width": 324}, "position": {"x": 1692.2322233423606, "y": 1821.9077961087607}, "positionAbsolute": {"x": 1692.2322233423606, "y": 1821.9077961087607}, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "note-ocedp", "node": {"description": "### 💡 Add your OpenAI API key here 👇", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-ocedp", "measured": {"height": 324, "width": 324}, "position": {"x": 824.1003268813427, "y": 698.6951695764802}, "positionAbsolute": {"x": 824.1003268813427, "y": 698.6951695764802}, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "note-rhNU3", "node": {"description": "### 💡 Add your OpenAI API key here 👇", "display_name": "", "documentation": "", "template": {"backgroundColor": "transparent"}}, "type": "note"}, "dragging": false, "height": 324, "id": "note-rhNU3", "measured": {"height": 324, "width": 324}, "position": {"x": 2350.297636215281, "y": 525.0687902842766}, "positionAbsolute": {"x": 2350.297636215281, "y": 525.0687902842766}, "selected": false, "type": "noteNode", "width": 324}, {"data": {"id": "OpenAIModel-e49kI", "node": {"base_classes": ["LanguageModel", "Message"], "beta": false, "category": "models", "conditional_paths": [], "custom_fields": {}, "description": "使用 OpenAI 模型生成文本", "display_name": "OpenAI", "documentation": "", "edited": false, "field_order": ["input_value", "system_message", "stream", "max_tokens", "model_kwargs", "json_mode", "model_name", "openai_api_base", "api_key", "temperature", "seed"], "frozen": false, "icon": "OpenAI", "key": "OpenAIModel", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "消息", "method": "text_response", "name": "text_output", "required_inputs": [], "selected": "Message", "tool_mode": true, "types": ["Message"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "语言模型", "method": "build_model", "name": "model_output", "required_inputs": ["api_key", "model_name"], "selected": "LanguageModel", "tool_mode": true, "types": ["LanguageModel"], "value": "__UNDEFINED__"}], "pinned": false, "score": 0.14285714285714285, "template": {"_type": "Component", "api_key": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "OpenAI API 密钥", "dynamic": false, "info": "用于 OpenAI 模型的 OpenAI API 密钥。", "input_types": ["Message"], "load_from_db": true, "name": "api_key", "password": true, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "str", "value": "OPENAI_API_KEY"}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "from langchain_openai import ChatOpenAI\nfrom pydantic.v1 import SecretStr\n\nfrom langflow.base.models.model import LCModelComponent\nfrom langflow.base.models.openai_constants import OPENAI_MODEL_NAMES\nfrom langflow.field_typing import LanguageModel\nfrom langflow.field_typing.range_spec import RangeSpec\nfrom langflow.inputs import BoolInput, DictInput, DropdownInput, IntInput, SecretStrInput, SliderInput, StrInput\n\n# 替换硬编码的字符串为中文，并保留英文注释\nclass OpenAIModelComponent(LCModelComponent):\n    display_name = \"OpenAI\"  # OpenAI\n    description = \"使用 OpenAI LLMs 生成文本。\"  # Generates text using OpenAI LLMs.\n    icon = \"OpenAI\"  # OpenAI\n    name = \"OpenAIModel\"  # OpenAIModel\n\n    inputs = [\n        *LCModelComponent._base_inputs,\n        IntInput(\n            name=\"max_tokens\",\n            display_name=\"最大 Token 数\",  # Max Tokens\n            advanced=True,\n            info=\"生成的最大 token 数。设置为 0 表示无限制。\"  # The maximum number of tokens to generate. Set to 0 for unlimited tokens.\n            ,\n            range_spec=RangeSpec(min=0, max=128000),\n        ),\n        DictInput(\n            name=\"model_kwargs\",\n            display_name=\"模型参数\",  # Model Kwargs\n            advanced=True,\n            info=\"传递给模型的其他关键字参数。\"  # Additional keyword arguments to pass to the model.\n            ,\n        ),\n        BoolInput(\n            name=\"json_mode\",\n            display_name=\"JSON 模式\",  # JSON Mode\n            advanced=True,\n            info=\"如果为 True，则无论是否传递 schema，都会输出 JSON。\"  # If True, it will output JSON regardless of passing a schema.\n            ,\n        ),\n        StrInput(\n            name=\"model_name\",\n            display_name=\"模型名称\",  # Model Name\n            advanced=False,\n            info=\"所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、\",\n            required=True\n        ),\n        # DropdownInput(\n        #     name=\"model_name\",\n        #     display_name=\"模型名称\",  # Model Name\n        #     advanced=False,\n        #     options=OPENAI_MODEL_NAMES,\n        #     value=OPENAI_MODEL_NAMES[1],\n        #     combobox=True,\n        # ),\n        StrInput(\n            name=\"openai_api_base\",\n            display_name=\"OpenAI API 基础地址\",  # OpenAI API Base\n            advanced=True,\n            info=\"OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。\"\n            \"您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。\"  # The base URL of the OpenAI API. Defaults to https://api.openai.com/v1. You can change this to use other APIs like JinaChat, LocalAI and Prem.\n            ,\n        ),\n        SecretStrInput(\n            name=\"api_key\",\n            display_name=\"OpenAI API 密钥\",  # OpenAI API Key\n            info=\"用于 OpenAI 模型的 OpenAI API 密钥。\"  # The OpenAI API Key to use for the OpenAI model.\n            ,\n            advanced=False,\n            value=\"OPENAI_API_KEY\",\n            required=True,\n        ),\n        SliderInput(\n            name=\"temperature\",\n            display_name=\"温度\",  # Temperature\n            value=0.1,\n            range_spec=RangeSpec(min=0, max=1, step=0.01),\n            advanced=True,\n        ),\n        IntInput(\n            name=\"seed\",\n            display_name=\"随机种子\",  # Seed\n            info=\"随机种子控制任务的可重复性。\"  # The seed controls the reproducibility of the job.\n            ,\n            advanced=True,\n            value=1,\n        ),\n        IntInput(\n            name=\"max_retries\",\n            display_name=\"最大重试次数\",  # Max Retries\n            info=\"生成时的最大重试次数。\"  # The maximum number of retries to make when generating.\n            ,\n            advanced=True,\n            value=5,\n        ),\n        IntInput(\n            name=\"timeout\",\n            display_name=\"超时时间\",  # Timeout\n            info=\"请求 OpenAI 完成 API 的超时时间。\"  # The timeout for requests to OpenAI completion API.\n            ,\n            advanced=True,\n            value=700,\n        ),\n    ]\n\n    def build_model(self) -> LanguageModel:  # type: ignore[type-var]\n        openai_api_key = self.api_key\n        temperature = self.temperature\n        model_name: str = self.model_name\n        max_tokens = self.max_tokens\n        model_kwargs = self.model_kwargs or {}\n        openai_api_base = self.openai_api_base or \"https://api.openai.com/v1\"\n        json_mode = self.json_mode\n        seed = self.seed\n        max_retries = self.max_retries\n        timeout = self.timeout\n\n        api_key = SecretStr(openai_api_key).get_secret_value() if openai_api_key else None\n        output = ChatOpenAI(\n            max_tokens=max_tokens or None,\n            model_kwargs=model_kwargs,\n            model=model_name,\n            base_url=openai_api_base,\n            api_key=api_key,\n            temperature=temperature if temperature is not None else 0.1,\n            seed=seed,\n            max_retries=max_retries,\n            request_timeout=timeout,\n        )\n        if json_mode:\n            output = output.bind(response_format={\"type\": \"json_object\"})\n\n        return output\n\n    def _get_exception_message(self, e: Exception):\n        \"\"\"Get a message from an OpenAI exception.\n\n        Args:\n            e (Exception): The exception to get the message from.\n\n        Returns:\n            str: The message from the exception.\n        \"\"\"\n        try:\n            from openai import BadRequestError\n        except ImportError:\n            return None\n        if isinstance(e, BadRequestError):\n            message = e.body.get(\"message\")\n            if message:\n                return message\n        return None\n"}, "input_value": {"_input_type": "MessageInput", "advanced": false, "display_name": "输入", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "input_value", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "json_mode": {"_input_type": "BoolInput", "advanced": true, "display_name": "JSON 模式", "dynamic": false, "info": "如果为 True，则无论是否传递 schema，都会输出 JSON。", "list": false, "list_add_label": "Add More", "name": "json_mode", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "max_retries": {"_input_type": "IntInput", "advanced": true, "display_name": "最大重试次数", "dynamic": false, "info": "生成时的最大重试次数。", "list": false, "list_add_label": "Add More", "name": "max_retries", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 5}, "max_tokens": {"_input_type": "IntInput", "advanced": true, "display_name": "最大 Token 数", "dynamic": false, "info": "生成的最大 token 数。设置为 0 表示无限制。", "list": false, "list_add_label": "Add More", "name": "max_tokens", "placeholder": "", "range_spec": {"max": 128000, "min": 0, "step": 0.1, "step_type": "float"}, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": ""}, "model_kwargs": {"_input_type": "DictInput", "advanced": true, "display_name": "模型参数", "dynamic": false, "info": "传递给模型的其他关键字参数。", "list": false, "list_add_label": "Add More", "name": "model_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "type": "dict", "value": {}}, "model_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {}, "display_name": "模型名称", "dynamic": false, "info": "所有支持openai调用格式的模型均可。例：gpt-4o、deepseek-v3、qwen2-max、", "name": "model_name", "options": ["gpt-4o-mini", "gpt-4o", "gpt-4.5-preview", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "gpt-4o"}, "openai_api_base": {"_input_type": "StrInput", "advanced": true, "display_name": "OpenAI API 基础地址", "dynamic": false, "info": "OpenAI API 的基础 URL。默认为 https://api.openai.com/v1。您可以更改此地址以使用其他 API，例如 JinaChat、LocalAI 和 Prem。", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "openai_api_base", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "seed": {"_input_type": "IntInput", "advanced": true, "display_name": "随机种子", "dynamic": false, "info": "随机种子控制任务的可重复性。", "list": false, "list_add_label": "Add More", "name": "seed", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1}, "stream": {"_input_type": "BoolInput", "advanced": true, "display_name": "流式输出", "dynamic": false, "info": "Stream the response from the model. Streaming works only in Chat.", "list": false, "list_add_label": "Add More", "name": "stream", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "system_message": {"_input_type": "MultilineInput", "advanced": false, "display_name": "系统消息", "dynamic": false, "info": "传递给模型的系统消息。", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "system_message", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "temperature": {"_input_type": "SliderInput", "advanced": true, "display_name": "温度", "dynamic": false, "info": "", "max_label": "", "max_label_icon": "", "min_label": "", "min_label_icon": "", "name": "temperature", "placeholder": "", "range_spec": {"max": 1, "min": 0, "step": 0.01, "step_type": "float"}, "required": false, "show": true, "slider_buttons": false, "slider_buttons_options": [], "slider_input": false, "title_case": false, "tool_mode": false, "type": "slider", "value": 0.1}, "timeout": {"_input_type": "IntInput", "advanced": true, "display_name": "超时时间", "dynamic": false, "info": "请求 OpenAI 完成 API 的超时时间。", "list": false, "list_add_label": "Add More", "name": "timeout", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 700}}, "tool_mode": false}, "showNode": true, "type": "OpenAIModel"}, "dragging": false, "id": "OpenAIModel-e49kI", "measured": {"height": 525, "width": 320}, "position": {"x": 2365.714820732046, "y": 600.6979286268308}, "selected": false, "type": "genericNode"}, {"data": {"id": "AstraDB-8CuoQ", "node": {"base_classes": ["Data", "DataFrame"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Ingest and search documents in Astra DB", "display_name": "Astra DB", "documentation": "https://docs.datastax.com/en/langflow/astra-components.html", "edited": false, "field_order": ["token", "environment", "database_name", "api_endpoint", "collection_name", "keyspace", "embedding_choice", "embedding_model", "ingest_data", "search_query", "should_cache_vector_store", "number_of_results", "search_type", "search_score_threshold", "advanced_search_filter", "autodetect_collection", "content_field", "deletion_field", "ignore_invalid_documents", "astradb_vectorstore_kwargs"], "frozen": false, "icon": "AstraDB", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "搜索结果", "method": "search_documents", "name": "search_results", "required_inputs": ["collection_name", "database_name", "token"], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据框", "method": "as_dataframe", "name": "dataframe", "required_inputs": [], "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Vector Store Connection", "hidden": true, "method": "as_vector_store", "name": "vectorstoreconnection", "selected": "VectorStore", "tool_mode": true, "types": ["VectorStore"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "advanced_search_filter": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "Search Metadata Filter", "dynamic": false, "info": "Optional dictionary of filters to apply to the search query.", "list": false, "list_add_label": "Add More", "name": "advanced_search_filter", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "api_endpoint": {"_input_type": "StrInput", "advanced": true, "display_name": "Astra DB API Endpoint", "dynamic": false, "info": "The API Endpoint for the Astra DB instance. Supercedes database selection.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "api_endpoint", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "astradb_vectorstore_kwargs": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "AstraDBVectorStore Parameters", "dynamic": false, "info": "Optional dictionary of additional parameters for the AstraDBVectorStore.", "list": false, "list_add_label": "Add More", "name": "astradb_vectorstore_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "autodetect_collection": {"_input_type": "BoolInput", "advanced": true, "display_name": "Autodetect Collection", "dynamic": false, "info": "Boolean flag to determine whether to autodetect the collection.", "list": false, "list_add_label": "Add More", "name": "autodetect_collection", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import re\nfrom collections import defaultdict\nfrom dataclasses import asdict, dataclass, field\n\nfrom astrapy import AstraDBAdmin, DataAPIClient, Database\nfrom astrapy.info import CollectionDescriptor\nfrom langchain_astradb import AstraDBVectorStore, CollectionVectorServiceOptions\n\nfrom langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store\nfrom langflow.base.vectorstores.vector_store_connection_decorator import vector_store_connection\nfrom langflow.helpers import docs_to_data\nfrom langflow.inputs import FloatInput, NestedDictInput\nfrom langflow.io import (\n    BoolInput,\n    DropdownInput,\n    HandleInput,\n    IntInput,\n    SecretStrInput,\n    StrInput,\n)\nfrom langflow.schema import Data\nfrom langflow.utils.version import get_version_info\n\n\n@vector_store_connection\nclass AstraDBVectorStoreComponent(LCVectorStoreComponent):\n    display_name: str = \"Astra DB\"\n    description: str = \"Ingest and search documents in Astra DB\"\n    documentation: str = \"https://docs.datastax.com/en/langflow/astra-components.html\"\n    name = \"AstraDB\"\n    icon: str = \"AstraDB\"\n\n    _cached_vector_store: AstraDBVectorStore | None = None\n\n    @dataclass\n    class NewDatabaseInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_database\",\n                        \"description\": \"Please allow several minutes for creation to complete.\",\n                        \"display_name\": \"Create new database\",\n                        \"field_order\": [\"01_new_database_name\", \"02_cloud_provider\", \"03_region\"],\n                        \"template\": {\n                            \"01_new_database_name\": StrInput(\n                                name=\"new_database_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new database to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_cloud_provider\": DropdownInput(\n                                name=\"cloud_provider\",\n                                display_name=\"Cloud provider\",\n                                info=\"Cloud provider for the new database.\",\n                                options=[],\n                                required=True,\n                                real_time_refresh=True,\n                            ),\n                            \"03_region\": DropdownInput(\n                                name=\"region\",\n                                display_name=\"Region\",\n                                info=\"Region for the new database.\",\n                                options=[],\n                                required=True,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    @dataclass\n    class NewCollectionInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_collection\",\n                        \"description\": \"Please allow several seconds for creation to complete.\",\n                        \"display_name\": \"Create new collection\",\n                        \"field_order\": [\n                            \"01_new_collection_name\",\n                            \"02_embedding_generation_provider\",\n                            \"03_embedding_generation_model\",\n                            \"04_dimension\",\n                        ],\n                        \"template\": {\n                            \"01_new_collection_name\": StrInput(\n                                name=\"new_collection_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new collection to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_embedding_generation_provider\": DropdownInput(\n                                name=\"embedding_generation_provider\",\n                                display_name=\"Embedding generation method\",\n                                info=\"Provider to use for generating embeddings.\",\n                                helper_text=(\n                                    \"To create collections with more embedding provider options, go to \"\n                                    '<a class=\"underline\" href=\"https://astra.datastax.com/\" target=\" _blank\" '\n                                    'rel=\"noopener noreferrer\">your database in Astra DB</a>'\n                                ),\n                                real_time_refresh=True,\n                                required=True,\n                                options=[],\n                            ),\n                            \"03_embedding_generation_model\": DropdownInput(\n                                name=\"embedding_generation_model\",\n                                display_name=\"Embedding model\",\n                                info=\"Model to use for generating embeddings.\",\n                                real_time_refresh=True,\n                                options=[],\n                            ),\n                            \"04_dimension\": IntInput(\n                                name=\"dimension\",\n                                display_name=\"Dimensions\",\n                                info=\"Dimensions of the embeddings to generate.\",\n                                value=None,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    inputs = [\n        SecretStrInput(\n            name=\"token\",\n            display_name=\"Astra DB Application Token\",\n            info=\"Authentication token for accessing Astra DB.\",\n            value=\"ASTRA_DB_APPLICATION_TOKEN\",\n            required=True,\n            real_time_refresh=True,\n            input_types=[],\n        ),\n        StrInput(\n            name=\"environment\",\n            display_name=\"Environment\",\n            info=\"The environment for the Astra DB API Endpoint.\",\n            advanced=True,\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"database_name\",\n            display_name=\"Database\",\n            info=\"The Database name for the Astra DB instance.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewDatabaseInput()),\n            combobox=True,\n        ),\n        StrInput(\n            name=\"api_endpoint\",\n            display_name=\"Astra DB API Endpoint\",\n            info=\"The API Endpoint for the Astra DB instance. Supercedes database selection.\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"collection_name\",\n            display_name=\"Collection\",\n            info=\"The name of the collection within Astra DB where the vectors will be stored.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewCollectionInput()),\n            combobox=True,\n            advanced=True,\n        ),\n        StrInput(\n            name=\"keyspace\",\n            display_name=\"Keyspace\",\n            info=\"Optional keyspace within Astra DB to use for the collection.\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"embedding_choice\",\n            display_name=\"Embedding Model or Astra Vectorize\",\n            info=\"Choose an embedding model or use Astra Vectorize.\",\n            options=[\"Embedding Model\", \"Astra Vectorize\"],\n            value=\"Embedding Model\",\n            advanced=True,\n            real_time_refresh=True,\n        ),\n        HandleInput(\n            name=\"embedding_model\",\n            display_name=\"Embedding Model\",\n            input_types=[\"Embeddings\"],\n            info=\"Specify the Embedding Model. Not required for Astra Vectorize collections.\",\n            required=False,\n        ),\n        *LCVectorStoreComponent.inputs,\n        IntInput(\n            name=\"number_of_results\",\n            display_name=\"Number of Search Results\",\n            info=\"Number of search results to return.\",\n            advanced=True,\n            value=4,\n        ),\n        DropdownInput(\n            name=\"search_type\",\n            display_name=\"Search Type\",\n            info=\"Search type to use\",\n            options=[\"Similarity\", \"Similarity with score threshold\", \"MMR (Max Marginal Relevance)\"],\n            value=\"Similarity\",\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"search_score_threshold\",\n            display_name=\"Search Score Threshold\",\n            info=\"Minimum similarity score threshold for search results. \"\n            \"(when using 'Similarity with score threshold')\",\n            value=0,\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"advanced_search_filter\",\n            display_name=\"Search Metadata Filter\",\n            info=\"Optional dictionary of filters to apply to the search query.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"autodetect_collection\",\n            display_name=\"Autodetect Collection\",\n            info=\"Boolean flag to determine whether to autodetect the collection.\",\n            advanced=True,\n            value=True,\n        ),\n        StrInput(\n            name=\"content_field\",\n            display_name=\"Content Field\",\n            info=\"Field to use as the text content field for the vector store.\",\n            advanced=True,\n        ),\n        StrInput(\n            name=\"deletion_field\",\n            display_name=\"Deletion Based On Field\",\n            info=\"When this parameter is provided, documents in the target collection with \"\n            \"metadata field values matching the input metadata field value will be deleted \"\n            \"before new data is loaded.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"ignore_invalid_documents\",\n            display_name=\"Ignore Invalid Documents\",\n            info=\"Boolean flag to determine whether to ignore invalid documents at runtime.\",\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"astradb_vectorstore_kwargs\",\n            display_name=\"AstraDBVectorStore Parameters\",\n            info=\"Optional dictionary of additional parameters for the AstraDBVectorStore.\",\n            advanced=True,\n        ),\n    ]\n\n    @classmethod\n    def map_cloud_providers(cls):\n        # TODO: Programmatically fetch the regions for each cloud provider\n        return {\n            \"dev\": {\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\"],\n                },\n            },\n            # TODO: Check test regions\n            \"test\": {\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\"],\n                },\n            },\n            \"prod\": {\n                \"Amazon Web Services\": {\n                    \"id\": \"aws\",\n                    \"regions\": [\"us-east-2\", \"ap-south-1\", \"eu-west-1\"],\n                },\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-east1\"],\n                },\n                \"Microsoft Azure\": {\n                    \"id\": \"azure\",\n                    \"regions\": [\"westus3\"],\n                },\n            },\n        }\n\n    @classmethod\n    def get_vectorize_providers(cls, token: str, environment: str | None = None, api_endpoint: str | None = None):\n        try:\n            # Get the admin object\n            admin = AstraDBAdmin(token=token, environment=environment)\n            db_admin = admin.get_database_admin(api_endpoint=api_endpoint)\n\n            # Get the list of embedding providers\n            embedding_providers = db_admin.find_embedding_providers().as_dict()\n\n            vectorize_providers_mapping = {}\n            # Map the provider display name to the provider key and models\n            for provider_key, provider_data in embedding_providers[\"embeddingProviders\"].items():\n                # Get the provider display name and models\n                display_name = provider_data[\"displayName\"]\n                models = [model[\"name\"] for model in provider_data[\"models\"]]\n\n                # Build our mapping\n                vectorize_providers_mapping[display_name] = [provider_key, models]\n\n            # Sort the resulting dictionary\n            return defaultdict(list, dict(sorted(vectorize_providers_mapping.items())))\n        except Exception as _:  # noqa: BLE001\n            return {}\n\n    @classmethod\n    async def create_database_api(\n        cls,\n        new_database_name: str,\n        cloud_provider: str,\n        region: str,\n        token: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n    ):\n        client = DataAPIClient(token=token, environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the environment, set to prod if null like\n        my_env = environment or \"prod\"\n\n        # Raise a value error if name isn't provided\n        if not new_database_name:\n            msg = \"Database name is required to create a new database.\"\n            raise ValueError(msg)\n\n        # Call the create database function\n        return await admin_client.async_create_database(\n            name=new_database_name,\n            cloud_provider=cls.map_cloud_providers()[my_env][cloud_provider][\"id\"],\n            region=region,\n            keyspace=keyspace,\n            wait_until_active=False,\n        )\n\n    @classmethod\n    async def create_collection_api(\n        cls,\n        new_collection_name: str,\n        token: str,\n        api_endpoint: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n        dimension: int | None = None,\n        embedding_generation_provider: str | None = None,\n        embedding_generation_model: str | None = None,\n    ):\n        # Create the data API client\n        client = DataAPIClient(token=token, environment=environment)\n\n        # Get the database object\n        database = client.get_async_database(api_endpoint=api_endpoint, token=token)\n\n        # Build vectorize options, if needed\n        vectorize_options = None\n        if not dimension:\n            vectorize_options = CollectionVectorServiceOptions(\n                provider=cls.get_vectorize_providers(\n                    token=token, environment=environment, api_endpoint=api_endpoint\n                ).get(embedding_generation_provider, [None, []])[0],\n                model_name=embedding_generation_model,\n            )\n\n        # Raise a value error if name isn't provided\n        if not new_collection_name:\n            msg = \"Collection name is required to create a new collection.\"\n            raise ValueError(msg)\n\n        # Create the collection\n        return await database.create_collection(\n            name=new_collection_name,\n            keyspace=keyspace,\n            dimension=dimension,\n            service=vectorize_options,\n        )\n\n    @classmethod\n    def get_database_list_static(cls, token: str, environment: str | None = None):\n        client = DataAPIClient(token=token, environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the list of databases\n        db_list = list(admin_client.list_databases())\n\n        # Set the environment properly\n        env_string = \"\"\n        if environment and environment != \"prod\":\n            env_string = f\"-{environment}\"\n\n        # Generate the api endpoint for each database\n        db_info_dict = {}\n        for db in db_list:\n            try:\n                # Get the API endpoint for the database\n                api_endpoint = f\"https://{db.info.id}-{db.info.region}.apps.astra{env_string}.datastax.com\"\n\n                # Get the number of collections\n                try:\n                    num_collections = len(\n                        list(\n                            client.get_database(\n                                api_endpoint=api_endpoint, token=token, keyspace=db.info.keyspace\n                            ).list_collection_names(keyspace=db.info.keyspace)\n                        )\n                    )\n                except Exception:  # noqa: BLE001\n                    if db.status != \"PENDING\":\n                        continue\n                    num_collections = 0\n\n                # Add the database to the dictionary\n                db_info_dict[db.info.name] = {\n                    \"api_endpoint\": api_endpoint,\n                    \"collections\": num_collections,\n                    \"status\": db.status if db.status != \"ACTIVE\" else None,\n                    \"org_id\": db.org_id if db.org_id else None,\n                }\n            except Exception:  # noqa: BLE001, S110\n                pass\n\n        return db_info_dict\n\n    def get_database_list(self):\n        return self.get_database_list_static(token=self.token, environment=self.environment)\n\n    @classmethod\n    def get_api_endpoint_static(\n        cls,\n        token: str,\n        environment: str | None = None,\n        api_endpoint: str | None = None,\n        database_name: str | None = None,\n    ):\n        # If the api_endpoint is set, return it\n        if api_endpoint:\n            return api_endpoint\n\n        # Check if the database_name is like a url\n        if database_name and database_name.startswith(\"https://\"):\n            return database_name\n\n        # If the database is not set, nothing we can do.\n        if not database_name:\n            return None\n\n        # Grab the database object\n        db = cls.get_database_list_static(token=token, environment=environment).get(database_name)\n        if not db:\n            return None\n\n        # Otherwise, get the URL from the database list\n        return db.get(\"api_endpoint\")\n\n    def get_api_endpoint(self):\n        return self.get_api_endpoint_static(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=self.api_endpoint,\n            database_name=self.database_name,\n        )\n\n    @classmethod\n    def get_database_id_static(cls, api_endpoint: str) -> str | None:\n        # Pattern matches standard UUID format: 8-4-4-4-12 hexadecimal characters\n        uuid_pattern = r\"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\"\n        match = re.search(uuid_pattern, api_endpoint)\n\n        return match.group(0) if match else None\n\n    def get_database_id(self):\n        return self.get_database_id_static(api_endpoint=self.get_api_endpoint())\n\n    def get_keyspace(self):\n        keyspace = self.keyspace\n\n        if keyspace:\n            return keyspace.strip()\n\n        return None\n\n    def get_database_object(self, api_endpoint: str | None = None):\n        try:\n            client = DataAPIClient(token=self.token, environment=self.environment)\n\n            return client.get_database(\n                api_endpoint=api_endpoint or self.get_api_endpoint(),\n                token=self.token,\n                keyspace=self.get_keyspace(),\n            )\n        except Exception as e:\n            msg = f\"Error fetching database object: {e}\"\n            raise ValueError(msg) from e\n\n    def collection_data(self, collection_name: str, database: Database | None = None):\n        try:\n            if not database:\n                client = DataAPIClient(token=self.token, environment=self.environment)\n\n                database = client.get_database(\n                    api_endpoint=self.get_api_endpoint(),\n                    token=self.token,\n                    keyspace=self.get_keyspace(),\n                )\n\n            collection = database.get_collection(collection_name, keyspace=self.get_keyspace())\n\n            return collection.estimated_document_count()\n        except Exception as e:  # noqa: BLE001\n            self.log(f\"Error checking collection data: {e}\")\n\n            return None\n\n    def _initialize_database_options(self):\n        try:\n            return [\n                {\n                    \"name\": name,\n                    \"status\": info[\"status\"],\n                    \"collections\": info[\"collections\"],\n                    \"api_endpoint\": info[\"api_endpoint\"],\n                    \"org_id\": info[\"org_id\"],\n                }\n                for name, info in self.get_database_list().items()\n            ]\n        except Exception as e:\n            msg = f\"Error fetching database options: {e}\"\n            raise ValueError(msg) from e\n\n    @classmethod\n    def get_provider_icon(cls, collection: CollectionDescriptor | None = None, provider_name: str | None = None) -> str:\n        # Get the provider name from the collection\n        provider_name = provider_name or (\n            collection.options.vector.service.provider\n            if collection and collection.options and collection.options.vector and collection.options.vector.service\n            else None\n        )\n\n        # If there is no provider, use the vector store icon\n        if not provider_name or provider_name == \"Bring your own\":\n            return \"vectorstores\"\n\n        # Map provider casings\n        case_map = {\n            \"nvidia\": \"NVIDIA\",\n            \"openai\": \"OpenAI\",\n            \"amazon bedrock\": \"AmazonBedrockEmbeddings\",\n            \"azure openai\": \"AzureOpenAiEmbeddings\",\n            \"cohere\": \"Cohere\",\n            \"jina ai\": \"JinaAI\",\n            \"mistral ai\": \"MistralAI\",\n            \"upstage\": \"Upstage\",\n            \"voyage ai\": \"VoyageAI\",\n        }\n\n        # Adjust the casing on some like nvidia\n        return case_map[provider_name.lower()] if provider_name.lower() in case_map else provider_name.title()\n\n    def _initialize_collection_options(self, api_endpoint: str | None = None):\n        # Nothing to generate if we don't have an API endpoint yet\n        api_endpoint = api_endpoint or self.get_api_endpoint()\n        if not api_endpoint:\n            return []\n\n        # Retrieve the database object\n        database = self.get_database_object(api_endpoint=api_endpoint)\n\n        # Get the list of collections\n        collection_list = list(database.list_collections(keyspace=self.get_keyspace()))\n\n        # Return the list of collections and metadata associated\n        return [\n            {\n                \"name\": col.name,\n                \"records\": self.collection_data(collection_name=col.name, database=database),\n                \"provider\": (\n                    col.options.vector.service.provider if col.options.vector and col.options.vector.service else None\n                ),\n                \"icon\": self.get_provider_icon(collection=col),\n                \"model\": (\n                    col.options.vector.service.model_name if col.options.vector and col.options.vector.service else None\n                ),\n            }\n            for col in collection_list\n        ]\n\n    def reset_provider_options(self, build_config: dict) -> dict:\n        \"\"\"Reset provider options and related configurations in the build_config dictionary.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get vectorize providers\n        vectorize_providers_api = self.get_vectorize_providers(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n        )\n\n        # Create a new dictionary with \"Bring your own\" first\n        vectorize_providers: dict[str, list[list[str]]] = {\"Bring your own\": [[], []]}\n\n        # Add the remaining items (only Nvidia) from the original dictionary\n        vectorize_providers.update(\n            {\n                k: v\n                for k, v in vectorize_providers_api.items()\n                if k.lower() in [\"nvidia\"]  # TODO: Eventually support more\n            }\n        )\n\n        # Set provider options\n        provider_field = \"02_embedding_generation_provider\"\n        template[provider_field][\"options\"] = list(vectorize_providers.keys())\n\n        # Add metadata for each provider option\n        template[provider_field][\"options_metadata\"] = [\n            {\"icon\": self.get_provider_icon(provider_name=provider)} for provider in template[provider_field][\"options\"]\n        ]\n\n        # Get selected embedding provider\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure embedding model field\n        model_field = \"03_embedding_generation_model\"\n        template[model_field].update(\n            {\n                \"options\": vectorize_providers.get(embedding_provider, [[], []])[1],\n                \"placeholder\": \"Bring your own\" if is_bring_your_own else None,\n                \"readonly\": is_bring_your_own,\n                \"required\": not is_bring_your_own,\n                \"value\": None,\n            }\n        )\n\n        # If this is a bring your own, set dimensions to 0\n        return self.reset_dimension_field(build_config)\n\n    def reset_dimension_field(self, build_config: dict) -> dict:\n        \"\"\"Reset dimension field options based on provided configuration.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get selected embedding model\n        provider_field = \"02_embedding_generation_provider\"\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure dimension field\n        dimension_field = \"04_dimension\"\n        dimension_value = 1024 if not is_bring_your_own else None  # TODO: Dynamically figure this out\n        template[dimension_field].update(\n            {\n                \"placeholder\": dimension_value,\n                \"value\": dimension_value,\n                \"readonly\": not is_bring_your_own,\n                \"required\": is_bring_your_own,\n            }\n        )\n\n        return build_config\n\n    def reset_collection_list(self, build_config: dict) -> dict:\n        \"\"\"Reset collection list options based on provided configuration.\"\"\"\n        # Get collection options\n        collection_options = self._initialize_collection_options(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n\n        # Update collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update(\n            {\n                \"options\": [col[\"name\"] for col in collection_options],\n                \"options_metadata\": [{k: v for k, v in col.items() if k != \"name\"} for col in collection_options],\n            }\n        )\n\n        # Reset selected collection if not in options\n        if collection_config[\"value\"] not in collection_config[\"options\"]:\n            collection_config[\"value\"] = \"\"\n\n        # Set advanced status based on database selection\n        collection_config[\"advanced\"] = not build_config[\"database_name\"][\"value\"]\n\n        return build_config\n\n    def reset_database_list(self, build_config: dict) -> dict:\n        \"\"\"Reset database list options and related configurations.\"\"\"\n        # Get database options\n        database_options = self._initialize_database_options()\n\n        # Update cloud provider options\n        env = self.environment or \"prod\"\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_cloud_provider\"][\"options\"] = list(self.map_cloud_providers()[env].keys())\n\n        # Update database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update(\n            {\n                \"options\": [db[\"name\"] for db in database_options],\n                \"options_metadata\": [{k: v for k, v in db.items() if k != \"name\"} for db in database_options],\n            }\n        )\n\n        # Reset selections if value not in options\n        if database_config[\"value\"] not in database_config[\"options\"]:\n            database_config[\"value\"] = \"\"\n            build_config[\"api_endpoint\"][\"value\"] = \"\"\n            build_config[\"collection_name\"][\"advanced\"] = True\n\n        # Set advanced status based on token presence\n        database_config[\"advanced\"] = not build_config[\"token\"][\"value\"]\n\n        return build_config\n\n    def reset_build_config(self, build_config: dict) -> dict:\n        \"\"\"Reset all build configuration options to default empty state.\"\"\"\n        # Reset database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"advanced\": True})\n        build_config[\"api_endpoint\"][\"value\"] = \"\"\n\n        # Reset collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"advanced\": True})\n\n        return build_config\n\n    async def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:\n        \"\"\"Update build configuration based on field name and value.\"\"\"\n        # Early return if no token provided\n        if not self.token:\n            return self.reset_build_config(build_config)\n\n        # Database creation callback\n        if field_name == \"database_name\" and isinstance(field_value, dict):\n            if \"01_new_database_name\" in field_value:\n                await self._create_new_database(build_config, field_value)\n                return self.reset_collection_list(build_config)\n            return self._update_cloud_regions(build_config, field_value)\n\n        # Collection creation callback\n        if field_name == \"collection_name\" and isinstance(field_value, dict):\n            # Case 1: New collection creation\n            if \"01_new_collection_name\" in field_value:\n                await self._create_new_collection(build_config, field_value)\n                return build_config\n\n            # Case 2: Update embedding provider options\n            if \"02_embedding_generation_provider\" in field_value:\n                return self.reset_provider_options(build_config)\n\n            # Case 3: Update dimension field\n            if \"03_embedding_generation_model\" in field_value:\n                return self.reset_dimension_field(build_config)\n\n        # Initial execution or token/environment change\n        first_run = field_name == \"collection_name\" and not field_value and not build_config[\"database_name\"][\"options\"]\n        if first_run or field_name in {\"token\", \"environment\"}:\n            return self.reset_database_list(build_config)\n\n        # Database selection change\n        if field_name == \"database_name\" and not isinstance(field_value, dict):\n            return self._handle_database_selection(build_config, field_value)\n\n        # Collection selection change\n        if field_name == \"collection_name\" and not isinstance(field_value, dict):\n            return self._handle_collection_selection(build_config, field_value)\n\n        return build_config\n\n    async def _create_new_database(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new database and update build config options.\"\"\"\n        try:\n            await self.create_database_api(\n                new_database_name=field_value[\"01_new_database_name\"],\n                token=self.token,\n                keyspace=self.get_keyspace(),\n                environment=self.environment,\n                cloud_provider=field_value[\"02_cloud_provider\"],\n                region=field_value[\"03_region\"],\n            )\n        except Exception as e:\n            msg = f\"Error creating database: {e}\"\n            raise ValueError(msg) from e\n\n        build_config[\"database_name\"][\"options\"].append(field_value[\"01_new_database_name\"])\n        build_config[\"database_name\"][\"options_metadata\"].append(\n            {\n                \"status\": \"PENDING\",\n                \"collections\": 0,\n                \"api_endpoint\": None,\n                \"org_id\": None,\n            }\n        )\n\n    def _update_cloud_regions(self, build_config: dict, field_value: dict) -> dict:\n        \"\"\"Update cloud provider regions in build config.\"\"\"\n        env = self.environment or \"prod\"\n        cloud_provider = field_value[\"02_cloud_provider\"]\n\n        # Update the region options based on the selected cloud provider\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"03_region\"][\"options\"] = self.map_cloud_providers()[env][cloud_provider][\"regions\"]\n\n        # Reset the the 03_region value if it's not in the new options\n        if template[\"03_region\"][\"value\"] not in template[\"03_region\"][\"options\"]:\n            template[\"03_region\"][\"value\"] = None\n\n        return build_config\n\n    async def _create_new_collection(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new collection and update build config options.\"\"\"\n        embedding_provider = field_value.get(\"02_embedding_generation_provider\")\n        try:\n            await self.create_collection_api(\n                new_collection_name=field_value[\"01_new_collection_name\"],\n                token=self.token,\n                api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n                environment=self.environment,\n                keyspace=self.get_keyspace(),\n                dimension=field_value.get(\"04_dimension\") if embedding_provider == \"Bring your own\" else None,\n                embedding_generation_provider=embedding_provider,\n                embedding_generation_model=field_value.get(\"03_embedding_generation_model\"),\n            )\n        except Exception as e:\n            msg = f\"Error creating collection: {e}\"\n            raise ValueError(msg) from e\n\n        provider = embedding_provider.lower() if embedding_provider and embedding_provider != \"Bring your own\" else None\n        build_config[\"collection_name\"].update(\n            {\n                \"value\": field_value[\"01_new_collection_name\"],\n                \"options\": build_config[\"collection_name\"][\"options\"] + [field_value[\"01_new_collection_name\"]],\n            }\n        )\n        build_config[\"embedding_choice\"][\"value\"] = \"Astra Vectorize\" if provider else \"Embedding Model\"\n        build_config[\"embedding_model\"][\"advanced\"] = bool(provider)\n        build_config[\"collection_name\"][\"options_metadata\"].append(\n            {\n                \"records\": 0,\n                \"provider\": provider,\n                \"icon\": self.get_provider_icon(provider_name=embedding_provider),\n                \"model\": field_value.get(\"03_embedding_generation_model\"),\n            }\n        )\n\n    def _handle_database_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle database selection and update related configurations.\"\"\"\n        build_config = self.reset_database_list(build_config)\n\n        # Reset collection list if database selection changes\n        if field_value not in build_config[\"database_name\"][\"options\"]:\n            build_config[\"database_name\"][\"value\"] = \"\"\n            return build_config\n\n        # Get the api endpoint for the selected database\n        index = build_config[\"database_name\"][\"options\"].index(field_value)\n        build_config[\"api_endpoint\"][\"value\"] = build_config[\"database_name\"][\"options_metadata\"][index][\"api_endpoint\"]\n\n        # Get the org_id for the selected database\n        org_id = build_config[\"database_name\"][\"options_metadata\"][index][\"org_id\"]\n        if not org_id:\n            return build_config\n\n        # Get the database id for the selected database\n        db_id = self.get_database_id_static(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        keyspace = self.get_keyspace() or \"default_keyspace\"\n\n        # Update the helper text for the embedding provider field\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_embedding_generation_provider\"][\"helper_text\"] = (\n            \"To create collections with more embedding provider options, go to \"\n            f'<a class=\"underline\" target=\"_blank\" rel=\"noopener noreferrer\" '\n            f'href=\"https://astra.datastax.com/org/{org_id}/database/{db_id}/data-explorer?createCollection=1&namespace={keyspace}\">'\n            \"your database in Astra DB</a>.\"\n        )\n\n        # Reset provider options\n        build_config = self.reset_provider_options(build_config)\n\n        return self.reset_collection_list(build_config)\n\n    def _handle_collection_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle collection selection and update embedding options.\"\"\"\n        build_config[\"autodetect_collection\"][\"value\"] = True\n        build_config = self.reset_collection_list(build_config)\n\n        if field_value and field_value not in build_config[\"collection_name\"][\"options\"]:\n            build_config[\"collection_name\"][\"options\"].append(field_value)\n            build_config[\"collection_name\"][\"options_metadata\"].append(\n                {\n                    \"records\": 0,\n                    \"provider\": None,\n                    \"icon\": \"vectorstores\",\n                    \"model\": None,\n                }\n            )\n            build_config[\"autodetect_collection\"][\"value\"] = False\n\n        if not field_value:\n            return build_config\n\n        index = build_config[\"collection_name\"][\"options\"].index(field_value)\n        provider = build_config[\"collection_name\"][\"options_metadata\"][index][\"provider\"]\n        build_config[\"embedding_model\"][\"advanced\"] = bool(provider)\n        build_config[\"embedding_choice\"][\"value\"] = \"Astra Vectorize\" if provider else \"Embedding Model\"\n        return build_config\n\n    @check_cached_vector_store\n    def build_vector_store(self):\n        try:\n            from langchain_astradb import AstraDBVectorStore\n        except ImportError as e:\n            msg = (\n                \"Could not import langchain Astra DB integration package. \"\n                \"Please install it with `pip install langchain-astradb`.\"\n            )\n            raise ImportError(msg) from e\n\n        # Get the embedding model and additional params\n        embedding_params = (\n            {\"embedding\": self.embedding_model}\n            if self.embedding_model and self.embedding_choice == \"Embedding Model\"\n            else {}\n        )\n\n        # Get the additional parameters\n        additional_params = self.astradb_vectorstore_kwargs or {}\n\n        # Get Langflow version and platform information\n        __version__ = get_version_info()[\"version\"]\n        langflow_prefix = \"\"\n        # if os.getenv(\"AWS_EXECUTION_ENV\") == \"AWS_ECS_FARGATE\":  # TODO: More precise way of detecting\n        #     langflow_prefix = \"ds-\"\n\n        # Get the database object\n        database = self.get_database_object()\n        autodetect = self.collection_name in database.list_collection_names() and self.autodetect_collection\n\n        # Bundle up the auto-detect parameters\n        autodetect_params = {\n            \"autodetect_collection\": autodetect,\n            \"content_field\": (\n                self.content_field\n                if self.content_field and embedding_params\n                else (\n                    \"page_content\"\n                    if embedding_params\n                    and self.collection_data(collection_name=self.collection_name, database=database) == 0\n                    else None\n                )\n            ),\n            \"ignore_invalid_documents\": self.ignore_invalid_documents,\n        }\n\n        # Attempt to build the Vector Store object\n        try:\n            vector_store = AstraDBVectorStore(\n                # Astra DB Authentication Parameters\n                token=self.token,\n                api_endpoint=database.api_endpoint,\n                namespace=database.keyspace,\n                collection_name=self.collection_name,\n                environment=self.environment,\n                # Astra DB Usage Tracking Parameters\n                ext_callers=[(f\"{langflow_prefix}langflow\", __version__)],\n                # Astra DB Vector Store Parameters\n                **autodetect_params,\n                **embedding_params,\n                **additional_params,\n            )\n        except Exception as e:\n            msg = f\"Error initializing AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        # Add documents to the vector store\n        self._add_documents_to_vector_store(vector_store)\n\n        return vector_store\n\n    def _add_documents_to_vector_store(self, vector_store) -> None:\n        self.ingest_data = self._prepare_ingest_data()\n\n        documents = []\n        for _input in self.ingest_data or []:\n            if isinstance(_input, Data):\n                documents.append(_input.to_lc_document())\n            else:\n                msg = \"Vector Store Inputs must be Data objects.\"\n                raise TypeError(msg)\n\n        if documents and self.deletion_field:\n            self.log(f\"Deleting documents where {self.deletion_field}\")\n            try:\n                database = self.get_database_object()\n                collection = database.get_collection(self.collection_name, keyspace=database.keyspace)\n                delete_values = list({doc.metadata[self.deletion_field] for doc in documents})\n                self.log(f\"Deleting documents where {self.deletion_field} matches {delete_values}.\")\n                collection.delete_many({f\"metadata.{self.deletion_field}\": {\"$in\": delete_values}})\n            except Exception as e:\n                msg = f\"Error deleting documents from AstraDBVectorStore based on '{self.deletion_field}': {e}\"\n                raise ValueError(msg) from e\n\n        if documents:\n            self.log(f\"Adding {len(documents)} documents to the Vector Store.\")\n            try:\n                vector_store.add_documents(documents)\n            except Exception as e:\n                msg = f\"Error adding documents to AstraDBVectorStore: {e}\"\n                raise ValueError(msg) from e\n        else:\n            self.log(\"No documents to add to the Vector Store.\")\n\n    def _map_search_type(self) -> str:\n        search_type_mapping = {\n            \"Similarity with score threshold\": \"similarity_score_threshold\",\n            \"MMR (Max Marginal Relevance)\": \"mmr\",\n        }\n\n        return search_type_mapping.get(self.search_type, \"similarity\")\n\n    def _build_search_args(self):\n        query = self.search_query if isinstance(self.search_query, str) and self.search_query.strip() else None\n\n        if query:\n            args = {\n                \"query\": query,\n                \"search_type\": self._map_search_type(),\n                \"k\": self.number_of_results,\n                \"score_threshold\": self.search_score_threshold,\n            }\n        elif self.advanced_search_filter:\n            args = {\n                \"n\": self.number_of_results,\n            }\n        else:\n            return {}\n\n        filter_arg = self.advanced_search_filter or {}\n        if filter_arg:\n            args[\"filter\"] = filter_arg\n\n        return args\n\n    def search_documents(self, vector_store=None) -> list[Data]:\n        vector_store = vector_store or self.build_vector_store()\n\n        self.log(f\"Search input: {self.search_query}\")\n        self.log(f\"Search type: {self.search_type}\")\n        self.log(f\"Number of results: {self.number_of_results}\")\n\n        try:\n            search_args = self._build_search_args()\n        except Exception as e:\n            msg = f\"Error in AstraDBVectorStore._build_search_args: {e}\"\n            raise ValueError(msg) from e\n\n        if not search_args:\n            self.log(\"No search input or filters provided. Skipping search.\")\n            return []\n\n        docs = []\n        search_method = \"search\" if \"query\" in search_args else \"metadata_search\"\n\n        try:\n            self.log(f\"Calling vector_store.{search_method} with args: {search_args}\")\n            docs = getattr(vector_store, search_method)(**search_args)\n        except Exception as e:\n            msg = f\"Error performing {search_method} in AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        self.log(f\"Retrieved documents: {len(docs)}\")\n\n        data = docs_to_data(docs)\n        self.log(f\"Converted documents to data: {len(data)}\")\n        self.status = data\n\n        return data\n\n    def get_retriever_kwargs(self):\n        search_args = self._build_search_args()\n\n        return {\n            \"search_type\": self._map_search_type(),\n            \"search_kwargs\": search_args,\n        }\n"}, "collection_name": {"_input_type": "DropdownInput", "advanced": true, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several seconds for creation to complete.", "display_name": "Create new collection", "field_order": ["01_new_collection_name", "02_embedding_generation_provider", "03_embedding_generation_model", "04_dimension"], "name": "create_collection", "template": {"01_new_collection_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new collection to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_collection_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_embedding_generation_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding generation method", "dynamic": false, "info": "Provider to use for generating embeddings.", "name": "embedding_generation_provider", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_embedding_generation_model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding model", "dynamic": false, "info": "Model to use for generating embeddings.", "name": "embedding_generation_model", "options": [], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "04_dimension": {"_input_type": "IntInput", "advanced": false, "display_name": "Dimensions (Required only for `Bring your own`)", "dynamic": false, "info": "Dimensions of the embeddings to generate.", "list": false, "list_add_label": "Add More", "name": "dimension", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1024}}}}}, "functionality": "create"}, "display_name": "Collection", "dynamic": false, "info": "The name of the collection within Astra DB where the vectors will be stored.", "name": "collection_name", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "content_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Content Field", "dynamic": false, "info": "Field to use as the text content field for the vector store.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "content_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "database_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several minutes for creation to complete.", "display_name": "Create new database", "field_order": ["01_new_database_name", "02_cloud_provider", "03_region"], "name": "create_database", "template": {"01_new_database_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new database to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_database_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_cloud_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Cloud provider", "dynamic": false, "info": "Cloud provider for the new database.", "name": "cloud_provider", "options": ["Amazon Web Services", "Google Cloud Platform", "Microsoft Azure"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_region": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Region", "dynamic": false, "info": "Region for the new database.", "name": "region", "options": [], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}}}}}, "functionality": "create"}, "display_name": "Database", "dynamic": false, "info": "The Database name for the Astra DB instance.", "name": "database_name", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "deletion_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Deletion Based On Field", "dynamic": false, "info": "When this parameter is provided, documents in the target collection with metadata field values matching the input metadata field value will be deleted before new data is loaded.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "deletion_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "embedding_choice": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding Model or Astra Vectorize", "dynamic": false, "info": "Choose an embedding model or use Astra Vectorize.", "name": "embedding_choice", "options": ["Embedding Model", "Astra Vectorize"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Embedding Model"}, "embedding_model": {"_input_type": "HandleInput", "advanced": false, "display_name": "Embedding Model", "dynamic": false, "info": "Specify the Embedding Model. Not required for Astra Vectorize collections.", "input_types": ["Embeddings"], "list": false, "list_add_label": "Add More", "name": "embedding_model", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "environment": {"_input_type": "StrInput", "advanced": true, "display_name": "Environment", "dynamic": false, "info": "The environment for the Astra DB API Endpoint.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "environment", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "ignore_invalid_documents": {"_input_type": "BoolInput", "advanced": true, "display_name": "Ignore Invalid Documents", "dynamic": false, "info": "Boolean flag to determine whether to ignore invalid documents at runtime.", "list": false, "list_add_label": "Add More", "name": "ignore_invalid_documents", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "ingest_data": {"_input_type": "DataInput", "advanced": false, "display_name": "导入数据", "dynamic": false, "info": "", "input_types": ["Data", "DataFrame"], "list": true, "list_add_label": "Add More", "name": "ingest_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "keyspace": {"_input_type": "StrInput", "advanced": true, "display_name": "Keyspace", "dynamic": false, "info": "Optional keyspace within Astra DB to use for the collection.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "keyspace", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "number_of_results": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Search Results", "dynamic": false, "info": "Number of search results to return.", "list": false, "list_add_label": "Add More", "name": "number_of_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 4}, "search_query": {"_input_type": "MultilineInput", "advanced": false, "display_name": "搜索查询", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "search_query", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "search_score_threshold": {"_input_type": "FloatInput", "advanced": true, "display_name": "Search Score Threshold", "dynamic": false, "info": "Minimum similarity score threshold for search results. (when using 'Similarity with score threshold')", "list": false, "list_add_label": "Add More", "name": "search_score_threshold", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": 0}, "search_type": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Search Type", "dynamic": false, "info": "Search type to use", "name": "search_type", "options": ["Similarity", "Similarity with score threshold", "MMR (Max Marginal Relevance)"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Similarity"}, "should_cache_vector_store": {"_input_type": "BoolInput", "advanced": true, "display_name": "缓存向量存储", "dynamic": false, "info": "如果为 True，则向量存储将在组件的当前构建中被缓存。这对于具有多个输出方法并希望共享相同向量存储的组件非常有用。", "list": false, "list_add_label": "Add More", "name": "should_cache_vector_store", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "token": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Astra DB Application Token", "dynamic": false, "info": "Authentication token for accessing Astra DB.", "input_types": [], "load_from_db": true, "name": "token", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_APPLICATION_TOKEN"}}, "tool_mode": false}, "showNode": true, "type": "AstraDB"}, "dragging": false, "id": "AstraDB-8CuoQ", "measured": {"height": 532, "width": 320}, "position": {"x": 1215.9887897864403, "y": 620.425551225735}, "selected": false, "type": "genericNode"}, {"data": {"id": "AstraDB-JF2lS", "node": {"base_classes": ["Data", "DataFrame"], "beta": false, "conditional_paths": [], "custom_fields": {}, "description": "Ingest and search documents in Astra DB", "display_name": "Astra DB", "documentation": "https://docs.datastax.com/en/langflow/astra-components.html", "edited": false, "field_order": ["token", "environment", "database_name", "api_endpoint", "collection_name", "keyspace", "embedding_choice", "embedding_model", "ingest_data", "search_query", "should_cache_vector_store", "number_of_results", "search_type", "search_score_threshold", "advanced_search_filter", "autodetect_collection", "content_field", "deletion_field", "ignore_invalid_documents", "astradb_vectorstore_kwargs"], "frozen": false, "icon": "AstraDB", "legacy": false, "metadata": {}, "minimized": false, "output_types": [], "outputs": [{"allows_loop": false, "cache": true, "display_name": "搜索结果", "method": "search_documents", "name": "search_results", "required_inputs": ["collection_name", "database_name", "token"], "selected": "Data", "tool_mode": true, "types": ["Data"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "数据框", "method": "as_dataframe", "name": "dataframe", "required_inputs": [], "selected": "DataFrame", "tool_mode": true, "types": ["DataFrame"], "value": "__UNDEFINED__"}, {"allows_loop": false, "cache": true, "display_name": "Vector Store Connection", "hidden": true, "method": "as_vector_store", "name": "vectorstoreconnection", "selected": "VectorStore", "tool_mode": true, "types": ["VectorStore"], "value": "__UNDEFINED__"}], "pinned": false, "template": {"_type": "Component", "advanced_search_filter": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "Search Metadata Filter", "dynamic": false, "info": "Optional dictionary of filters to apply to the search query.", "list": false, "list_add_label": "Add More", "name": "advanced_search_filter", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "api_endpoint": {"_input_type": "StrInput", "advanced": true, "display_name": "Astra DB API Endpoint", "dynamic": false, "info": "The API Endpoint for the Astra DB instance. Supercedes database selection.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "api_endpoint", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "astradb_vectorstore_kwargs": {"_input_type": "NestedDictInput", "advanced": true, "display_name": "AstraDBVectorStore Parameters", "dynamic": false, "info": "Optional dictionary of additional parameters for the AstraDBVectorStore.", "list": false, "list_add_label": "Add More", "name": "astradb_vectorstore_kwargs", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "NestedDict", "value": {}}, "autodetect_collection": {"_input_type": "BoolInput", "advanced": true, "display_name": "Autodetect Collection", "dynamic": false, "info": "Boolean flag to determine whether to autodetect the collection.", "list": false, "list_add_label": "Add More", "name": "autodetect_collection", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "code": {"advanced": true, "dynamic": true, "fileTypes": [], "file_path": "", "info": "", "list": false, "load_from_db": false, "multiline": true, "name": "code", "password": false, "placeholder": "", "required": true, "show": true, "title_case": false, "type": "code", "value": "import re\nfrom collections import defaultdict\nfrom dataclasses import asdict, dataclass, field\n\nfrom astrapy import AstraDBAdmin, DataAPIClient, Database\nfrom astrapy.info import CollectionDescriptor\nfrom langchain_astradb import AstraDBVectorStore, CollectionVectorServiceOptions\n\nfrom langflow.base.vectorstores.model import LCVectorStoreComponent, check_cached_vector_store\nfrom langflow.base.vectorstores.vector_store_connection_decorator import vector_store_connection\nfrom langflow.helpers import docs_to_data\nfrom langflow.inputs import FloatInput, NestedDictInput\nfrom langflow.io import (\n    BoolInput,\n    DropdownInput,\n    HandleInput,\n    IntInput,\n    SecretStrInput,\n    StrInput,\n)\nfrom langflow.schema import Data\nfrom langflow.utils.version import get_version_info\n\n\n@vector_store_connection\nclass AstraDBVectorStoreComponent(LCVectorStoreComponent):\n    display_name: str = \"Astra DB\"\n    description: str = \"Ingest and search documents in Astra DB\"\n    documentation: str = \"https://docs.datastax.com/en/langflow/astra-components.html\"\n    name = \"AstraDB\"\n    icon: str = \"AstraDB\"\n\n    _cached_vector_store: AstraDBVectorStore | None = None\n\n    @dataclass\n    class NewDatabaseInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_database\",\n                        \"description\": \"Please allow several minutes for creation to complete.\",\n                        \"display_name\": \"Create new database\",\n                        \"field_order\": [\"01_new_database_name\", \"02_cloud_provider\", \"03_region\"],\n                        \"template\": {\n                            \"01_new_database_name\": StrInput(\n                                name=\"new_database_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new database to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_cloud_provider\": DropdownInput(\n                                name=\"cloud_provider\",\n                                display_name=\"Cloud provider\",\n                                info=\"Cloud provider for the new database.\",\n                                options=[],\n                                required=True,\n                                real_time_refresh=True,\n                            ),\n                            \"03_region\": DropdownInput(\n                                name=\"region\",\n                                display_name=\"Region\",\n                                info=\"Region for the new database.\",\n                                options=[],\n                                required=True,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    @dataclass\n    class NewCollectionInput:\n        functionality: str = \"create\"\n        fields: dict[str, dict] = field(\n            default_factory=lambda: {\n                \"data\": {\n                    \"node\": {\n                        \"name\": \"create_collection\",\n                        \"description\": \"Please allow several seconds for creation to complete.\",\n                        \"display_name\": \"Create new collection\",\n                        \"field_order\": [\n                            \"01_new_collection_name\",\n                            \"02_embedding_generation_provider\",\n                            \"03_embedding_generation_model\",\n                            \"04_dimension\",\n                        ],\n                        \"template\": {\n                            \"01_new_collection_name\": StrInput(\n                                name=\"new_collection_name\",\n                                display_name=\"Name\",\n                                info=\"Name of the new collection to create in Astra DB.\",\n                                required=True,\n                            ),\n                            \"02_embedding_generation_provider\": DropdownInput(\n                                name=\"embedding_generation_provider\",\n                                display_name=\"Embedding generation method\",\n                                info=\"Provider to use for generating embeddings.\",\n                                helper_text=(\n                                    \"To create collections with more embedding provider options, go to \"\n                                    '<a class=\"underline\" href=\"https://astra.datastax.com/\" target=\" _blank\" '\n                                    'rel=\"noopener noreferrer\">your database in Astra DB</a>'\n                                ),\n                                real_time_refresh=True,\n                                required=True,\n                                options=[],\n                            ),\n                            \"03_embedding_generation_model\": DropdownInput(\n                                name=\"embedding_generation_model\",\n                                display_name=\"Embedding model\",\n                                info=\"Model to use for generating embeddings.\",\n                                real_time_refresh=True,\n                                options=[],\n                            ),\n                            \"04_dimension\": IntInput(\n                                name=\"dimension\",\n                                display_name=\"Dimensions\",\n                                info=\"Dimensions of the embeddings to generate.\",\n                                value=None,\n                            ),\n                        },\n                    },\n                }\n            }\n        )\n\n    inputs = [\n        SecretStrInput(\n            name=\"token\",\n            display_name=\"Astra DB Application Token\",\n            info=\"Authentication token for accessing Astra DB.\",\n            value=\"ASTRA_DB_APPLICATION_TOKEN\",\n            required=True,\n            real_time_refresh=True,\n            input_types=[],\n        ),\n        StrInput(\n            name=\"environment\",\n            display_name=\"Environment\",\n            info=\"The environment for the Astra DB API Endpoint.\",\n            advanced=True,\n            real_time_refresh=True,\n        ),\n        DropdownInput(\n            name=\"database_name\",\n            display_name=\"Database\",\n            info=\"The Database name for the Astra DB instance.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewDatabaseInput()),\n            combobox=True,\n        ),\n        StrInput(\n            name=\"api_endpoint\",\n            display_name=\"Astra DB API Endpoint\",\n            info=\"The API Endpoint for the Astra DB instance. Supercedes database selection.\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"collection_name\",\n            display_name=\"Collection\",\n            info=\"The name of the collection within Astra DB where the vectors will be stored.\",\n            required=True,\n            refresh_button=True,\n            real_time_refresh=True,\n            dialog_inputs=asdict(NewCollectionInput()),\n            combobox=True,\n            advanced=True,\n        ),\n        StrInput(\n            name=\"keyspace\",\n            display_name=\"Keyspace\",\n            info=\"Optional keyspace within Astra DB to use for the collection.\",\n            advanced=True,\n        ),\n        DropdownInput(\n            name=\"embedding_choice\",\n            display_name=\"Embedding Model or Astra Vectorize\",\n            info=\"Choose an embedding model or use Astra Vectorize.\",\n            options=[\"Embedding Model\", \"Astra Vectorize\"],\n            value=\"Embedding Model\",\n            advanced=True,\n            real_time_refresh=True,\n        ),\n        HandleInput(\n            name=\"embedding_model\",\n            display_name=\"Embedding Model\",\n            input_types=[\"Embeddings\"],\n            info=\"Specify the Embedding Model. Not required for Astra Vectorize collections.\",\n            required=False,\n        ),\n        *LCVectorStoreComponent.inputs,\n        IntInput(\n            name=\"number_of_results\",\n            display_name=\"Number of Search Results\",\n            info=\"Number of search results to return.\",\n            advanced=True,\n            value=4,\n        ),\n        DropdownInput(\n            name=\"search_type\",\n            display_name=\"Search Type\",\n            info=\"Search type to use\",\n            options=[\"Similarity\", \"Similarity with score threshold\", \"MMR (Max Marginal Relevance)\"],\n            value=\"Similarity\",\n            advanced=True,\n        ),\n        FloatInput(\n            name=\"search_score_threshold\",\n            display_name=\"Search Score Threshold\",\n            info=\"Minimum similarity score threshold for search results. \"\n            \"(when using 'Similarity with score threshold')\",\n            value=0,\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"advanced_search_filter\",\n            display_name=\"Search Metadata Filter\",\n            info=\"Optional dictionary of filters to apply to the search query.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"autodetect_collection\",\n            display_name=\"Autodetect Collection\",\n            info=\"Boolean flag to determine whether to autodetect the collection.\",\n            advanced=True,\n            value=True,\n        ),\n        StrInput(\n            name=\"content_field\",\n            display_name=\"Content Field\",\n            info=\"Field to use as the text content field for the vector store.\",\n            advanced=True,\n        ),\n        StrInput(\n            name=\"deletion_field\",\n            display_name=\"Deletion Based On Field\",\n            info=\"When this parameter is provided, documents in the target collection with \"\n            \"metadata field values matching the input metadata field value will be deleted \"\n            \"before new data is loaded.\",\n            advanced=True,\n        ),\n        BoolInput(\n            name=\"ignore_invalid_documents\",\n            display_name=\"Ignore Invalid Documents\",\n            info=\"Boolean flag to determine whether to ignore invalid documents at runtime.\",\n            advanced=True,\n        ),\n        NestedDictInput(\n            name=\"astradb_vectorstore_kwargs\",\n            display_name=\"AstraDBVectorStore Parameters\",\n            info=\"Optional dictionary of additional parameters for the AstraDBVectorStore.\",\n            advanced=True,\n        ),\n    ]\n\n    @classmethod\n    def map_cloud_providers(cls):\n        # TODO: Programmatically fetch the regions for each cloud provider\n        return {\n            \"dev\": {\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\"],\n                },\n            },\n            # TODO: Check test regions\n            \"test\": {\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-central1\"],\n                },\n            },\n            \"prod\": {\n                \"Amazon Web Services\": {\n                    \"id\": \"aws\",\n                    \"regions\": [\"us-east-2\", \"ap-south-1\", \"eu-west-1\"],\n                },\n                \"Google Cloud Platform\": {\n                    \"id\": \"gcp\",\n                    \"regions\": [\"us-east1\"],\n                },\n                \"Microsoft Azure\": {\n                    \"id\": \"azure\",\n                    \"regions\": [\"westus3\"],\n                },\n            },\n        }\n\n    @classmethod\n    def get_vectorize_providers(cls, token: str, environment: str | None = None, api_endpoint: str | None = None):\n        try:\n            # Get the admin object\n            admin = AstraDBAdmin(token=token, environment=environment)\n            db_admin = admin.get_database_admin(api_endpoint=api_endpoint)\n\n            # Get the list of embedding providers\n            embedding_providers = db_admin.find_embedding_providers().as_dict()\n\n            vectorize_providers_mapping = {}\n            # Map the provider display name to the provider key and models\n            for provider_key, provider_data in embedding_providers[\"embeddingProviders\"].items():\n                # Get the provider display name and models\n                display_name = provider_data[\"displayName\"]\n                models = [model[\"name\"] for model in provider_data[\"models\"]]\n\n                # Build our mapping\n                vectorize_providers_mapping[display_name] = [provider_key, models]\n\n            # Sort the resulting dictionary\n            return defaultdict(list, dict(sorted(vectorize_providers_mapping.items())))\n        except Exception as _:  # noqa: BLE001\n            return {}\n\n    @classmethod\n    async def create_database_api(\n        cls,\n        new_database_name: str,\n        cloud_provider: str,\n        region: str,\n        token: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n    ):\n        client = DataAPIClient(token=token, environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the environment, set to prod if null like\n        my_env = environment or \"prod\"\n\n        # Raise a value error if name isn't provided\n        if not new_database_name:\n            msg = \"Database name is required to create a new database.\"\n            raise ValueError(msg)\n\n        # Call the create database function\n        return await admin_client.async_create_database(\n            name=new_database_name,\n            cloud_provider=cls.map_cloud_providers()[my_env][cloud_provider][\"id\"],\n            region=region,\n            keyspace=keyspace,\n            wait_until_active=False,\n        )\n\n    @classmethod\n    async def create_collection_api(\n        cls,\n        new_collection_name: str,\n        token: str,\n        api_endpoint: str,\n        environment: str | None = None,\n        keyspace: str | None = None,\n        dimension: int | None = None,\n        embedding_generation_provider: str | None = None,\n        embedding_generation_model: str | None = None,\n    ):\n        # Create the data API client\n        client = DataAPIClient(token=token, environment=environment)\n\n        # Get the database object\n        database = client.get_async_database(api_endpoint=api_endpoint, token=token)\n\n        # Build vectorize options, if needed\n        vectorize_options = None\n        if not dimension:\n            vectorize_options = CollectionVectorServiceOptions(\n                provider=cls.get_vectorize_providers(\n                    token=token, environment=environment, api_endpoint=api_endpoint\n                ).get(embedding_generation_provider, [None, []])[0],\n                model_name=embedding_generation_model,\n            )\n\n        # Raise a value error if name isn't provided\n        if not new_collection_name:\n            msg = \"Collection name is required to create a new collection.\"\n            raise ValueError(msg)\n\n        # Create the collection\n        return await database.create_collection(\n            name=new_collection_name,\n            keyspace=keyspace,\n            dimension=dimension,\n            service=vectorize_options,\n        )\n\n    @classmethod\n    def get_database_list_static(cls, token: str, environment: str | None = None):\n        client = DataAPIClient(token=token, environment=environment)\n\n        # Get the admin object\n        admin_client = client.get_admin(token=token)\n\n        # Get the list of databases\n        db_list = list(admin_client.list_databases())\n\n        # Set the environment properly\n        env_string = \"\"\n        if environment and environment != \"prod\":\n            env_string = f\"-{environment}\"\n\n        # Generate the api endpoint for each database\n        db_info_dict = {}\n        for db in db_list:\n            try:\n                # Get the API endpoint for the database\n                api_endpoint = f\"https://{db.info.id}-{db.info.region}.apps.astra{env_string}.datastax.com\"\n\n                # Get the number of collections\n                try:\n                    num_collections = len(\n                        list(\n                            client.get_database(\n                                api_endpoint=api_endpoint, token=token, keyspace=db.info.keyspace\n                            ).list_collection_names(keyspace=db.info.keyspace)\n                        )\n                    )\n                except Exception:  # noqa: BLE001\n                    if db.status != \"PENDING\":\n                        continue\n                    num_collections = 0\n\n                # Add the database to the dictionary\n                db_info_dict[db.info.name] = {\n                    \"api_endpoint\": api_endpoint,\n                    \"collections\": num_collections,\n                    \"status\": db.status if db.status != \"ACTIVE\" else None,\n                    \"org_id\": db.org_id if db.org_id else None,\n                }\n            except Exception:  # noqa: BLE001, S110\n                pass\n\n        return db_info_dict\n\n    def get_database_list(self):\n        return self.get_database_list_static(token=self.token, environment=self.environment)\n\n    @classmethod\n    def get_api_endpoint_static(\n        cls,\n        token: str,\n        environment: str | None = None,\n        api_endpoint: str | None = None,\n        database_name: str | None = None,\n    ):\n        # If the api_endpoint is set, return it\n        if api_endpoint:\n            return api_endpoint\n\n        # Check if the database_name is like a url\n        if database_name and database_name.startswith(\"https://\"):\n            return database_name\n\n        # If the database is not set, nothing we can do.\n        if not database_name:\n            return None\n\n        # Grab the database object\n        db = cls.get_database_list_static(token=token, environment=environment).get(database_name)\n        if not db:\n            return None\n\n        # Otherwise, get the URL from the database list\n        return db.get(\"api_endpoint\")\n\n    def get_api_endpoint(self):\n        return self.get_api_endpoint_static(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=self.api_endpoint,\n            database_name=self.database_name,\n        )\n\n    @classmethod\n    def get_database_id_static(cls, api_endpoint: str) -> str | None:\n        # Pattern matches standard UUID format: 8-4-4-4-12 hexadecimal characters\n        uuid_pattern = r\"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\"\n        match = re.search(uuid_pattern, api_endpoint)\n\n        return match.group(0) if match else None\n\n    def get_database_id(self):\n        return self.get_database_id_static(api_endpoint=self.get_api_endpoint())\n\n    def get_keyspace(self):\n        keyspace = self.keyspace\n\n        if keyspace:\n            return keyspace.strip()\n\n        return None\n\n    def get_database_object(self, api_endpoint: str | None = None):\n        try:\n            client = DataAPIClient(token=self.token, environment=self.environment)\n\n            return client.get_database(\n                api_endpoint=api_endpoint or self.get_api_endpoint(),\n                token=self.token,\n                keyspace=self.get_keyspace(),\n            )\n        except Exception as e:\n            msg = f\"Error fetching database object: {e}\"\n            raise ValueError(msg) from e\n\n    def collection_data(self, collection_name: str, database: Database | None = None):\n        try:\n            if not database:\n                client = DataAPIClient(token=self.token, environment=self.environment)\n\n                database = client.get_database(\n                    api_endpoint=self.get_api_endpoint(),\n                    token=self.token,\n                    keyspace=self.get_keyspace(),\n                )\n\n            collection = database.get_collection(collection_name, keyspace=self.get_keyspace())\n\n            return collection.estimated_document_count()\n        except Exception as e:  # noqa: BLE001\n            self.log(f\"Error checking collection data: {e}\")\n\n            return None\n\n    def _initialize_database_options(self):\n        try:\n            return [\n                {\n                    \"name\": name,\n                    \"status\": info[\"status\"],\n                    \"collections\": info[\"collections\"],\n                    \"api_endpoint\": info[\"api_endpoint\"],\n                    \"org_id\": info[\"org_id\"],\n                }\n                for name, info in self.get_database_list().items()\n            ]\n        except Exception as e:\n            msg = f\"Error fetching database options: {e}\"\n            raise ValueError(msg) from e\n\n    @classmethod\n    def get_provider_icon(cls, collection: CollectionDescriptor | None = None, provider_name: str | None = None) -> str:\n        # Get the provider name from the collection\n        provider_name = provider_name or (\n            collection.options.vector.service.provider\n            if collection and collection.options and collection.options.vector and collection.options.vector.service\n            else None\n        )\n\n        # If there is no provider, use the vector store icon\n        if not provider_name or provider_name == \"Bring your own\":\n            return \"vectorstores\"\n\n        # Map provider casings\n        case_map = {\n            \"nvidia\": \"NVIDIA\",\n            \"openai\": \"OpenAI\",\n            \"amazon bedrock\": \"AmazonBedrockEmbeddings\",\n            \"azure openai\": \"AzureOpenAiEmbeddings\",\n            \"cohere\": \"Cohere\",\n            \"jina ai\": \"JinaAI\",\n            \"mistral ai\": \"MistralAI\",\n            \"upstage\": \"Upstage\",\n            \"voyage ai\": \"VoyageAI\",\n        }\n\n        # Adjust the casing on some like nvidia\n        return case_map[provider_name.lower()] if provider_name.lower() in case_map else provider_name.title()\n\n    def _initialize_collection_options(self, api_endpoint: str | None = None):\n        # Nothing to generate if we don't have an API endpoint yet\n        api_endpoint = api_endpoint or self.get_api_endpoint()\n        if not api_endpoint:\n            return []\n\n        # Retrieve the database object\n        database = self.get_database_object(api_endpoint=api_endpoint)\n\n        # Get the list of collections\n        collection_list = list(database.list_collections(keyspace=self.get_keyspace()))\n\n        # Return the list of collections and metadata associated\n        return [\n            {\n                \"name\": col.name,\n                \"records\": self.collection_data(collection_name=col.name, database=database),\n                \"provider\": (\n                    col.options.vector.service.provider if col.options.vector and col.options.vector.service else None\n                ),\n                \"icon\": self.get_provider_icon(collection=col),\n                \"model\": (\n                    col.options.vector.service.model_name if col.options.vector and col.options.vector.service else None\n                ),\n            }\n            for col in collection_list\n        ]\n\n    def reset_provider_options(self, build_config: dict) -> dict:\n        \"\"\"Reset provider options and related configurations in the build_config dictionary.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get vectorize providers\n        vectorize_providers_api = self.get_vectorize_providers(\n            token=self.token,\n            environment=self.environment,\n            api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n        )\n\n        # Create a new dictionary with \"Bring your own\" first\n        vectorize_providers: dict[str, list[list[str]]] = {\"Bring your own\": [[], []]}\n\n        # Add the remaining items (only Nvidia) from the original dictionary\n        vectorize_providers.update(\n            {\n                k: v\n                for k, v in vectorize_providers_api.items()\n                if k.lower() in [\"nvidia\"]  # TODO: Eventually support more\n            }\n        )\n\n        # Set provider options\n        provider_field = \"02_embedding_generation_provider\"\n        template[provider_field][\"options\"] = list(vectorize_providers.keys())\n\n        # Add metadata for each provider option\n        template[provider_field][\"options_metadata\"] = [\n            {\"icon\": self.get_provider_icon(provider_name=provider)} for provider in template[provider_field][\"options\"]\n        ]\n\n        # Get selected embedding provider\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure embedding model field\n        model_field = \"03_embedding_generation_model\"\n        template[model_field].update(\n            {\n                \"options\": vectorize_providers.get(embedding_provider, [[], []])[1],\n                \"placeholder\": \"Bring your own\" if is_bring_your_own else None,\n                \"readonly\": is_bring_your_own,\n                \"required\": not is_bring_your_own,\n                \"value\": None,\n            }\n        )\n\n        # If this is a bring your own, set dimensions to 0\n        return self.reset_dimension_field(build_config)\n\n    def reset_dimension_field(self, build_config: dict) -> dict:\n        \"\"\"Reset dimension field options based on provided configuration.\"\"\"\n        # Extract template path for cleaner access\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n\n        # Get selected embedding model\n        provider_field = \"02_embedding_generation_provider\"\n        embedding_provider = template[provider_field][\"value\"]\n        is_bring_your_own = embedding_provider and embedding_provider == \"Bring your own\"\n\n        # Configure dimension field\n        dimension_field = \"04_dimension\"\n        dimension_value = 1024 if not is_bring_your_own else None  # TODO: Dynamically figure this out\n        template[dimension_field].update(\n            {\n                \"placeholder\": dimension_value,\n                \"value\": dimension_value,\n                \"readonly\": not is_bring_your_own,\n                \"required\": is_bring_your_own,\n            }\n        )\n\n        return build_config\n\n    def reset_collection_list(self, build_config: dict) -> dict:\n        \"\"\"Reset collection list options based on provided configuration.\"\"\"\n        # Get collection options\n        collection_options = self._initialize_collection_options(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n\n        # Update collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update(\n            {\n                \"options\": [col[\"name\"] for col in collection_options],\n                \"options_metadata\": [{k: v for k, v in col.items() if k != \"name\"} for col in collection_options],\n            }\n        )\n\n        # Reset selected collection if not in options\n        if collection_config[\"value\"] not in collection_config[\"options\"]:\n            collection_config[\"value\"] = \"\"\n\n        # Set advanced status based on database selection\n        collection_config[\"advanced\"] = not build_config[\"database_name\"][\"value\"]\n\n        return build_config\n\n    def reset_database_list(self, build_config: dict) -> dict:\n        \"\"\"Reset database list options and related configurations.\"\"\"\n        # Get database options\n        database_options = self._initialize_database_options()\n\n        # Update cloud provider options\n        env = self.environment or \"prod\"\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_cloud_provider\"][\"options\"] = list(self.map_cloud_providers()[env].keys())\n\n        # Update database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update(\n            {\n                \"options\": [db[\"name\"] for db in database_options],\n                \"options_metadata\": [{k: v for k, v in db.items() if k != \"name\"} for db in database_options],\n            }\n        )\n\n        # Reset selections if value not in options\n        if database_config[\"value\"] not in database_config[\"options\"]:\n            database_config[\"value\"] = \"\"\n            build_config[\"api_endpoint\"][\"value\"] = \"\"\n            build_config[\"collection_name\"][\"advanced\"] = True\n\n        # Set advanced status based on token presence\n        database_config[\"advanced\"] = not build_config[\"token\"][\"value\"]\n\n        return build_config\n\n    def reset_build_config(self, build_config: dict) -> dict:\n        \"\"\"Reset all build configuration options to default empty state.\"\"\"\n        # Reset database configuration\n        database_config = build_config[\"database_name\"]\n        database_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"advanced\": True})\n        build_config[\"api_endpoint\"][\"value\"] = \"\"\n\n        # Reset collection configuration\n        collection_config = build_config[\"collection_name\"]\n        collection_config.update({\"options\": [], \"options_metadata\": [], \"value\": \"\", \"advanced\": True})\n\n        return build_config\n\n    async def update_build_config(self, build_config: dict, field_value: str, field_name: str | None = None) -> dict:\n        \"\"\"Update build configuration based on field name and value.\"\"\"\n        # Early return if no token provided\n        if not self.token:\n            return self.reset_build_config(build_config)\n\n        # Database creation callback\n        if field_name == \"database_name\" and isinstance(field_value, dict):\n            if \"01_new_database_name\" in field_value:\n                await self._create_new_database(build_config, field_value)\n                return self.reset_collection_list(build_config)\n            return self._update_cloud_regions(build_config, field_value)\n\n        # Collection creation callback\n        if field_name == \"collection_name\" and isinstance(field_value, dict):\n            # Case 1: New collection creation\n            if \"01_new_collection_name\" in field_value:\n                await self._create_new_collection(build_config, field_value)\n                return build_config\n\n            # Case 2: Update embedding provider options\n            if \"02_embedding_generation_provider\" in field_value:\n                return self.reset_provider_options(build_config)\n\n            # Case 3: Update dimension field\n            if \"03_embedding_generation_model\" in field_value:\n                return self.reset_dimension_field(build_config)\n\n        # Initial execution or token/environment change\n        first_run = field_name == \"collection_name\" and not field_value and not build_config[\"database_name\"][\"options\"]\n        if first_run or field_name in {\"token\", \"environment\"}:\n            return self.reset_database_list(build_config)\n\n        # Database selection change\n        if field_name == \"database_name\" and not isinstance(field_value, dict):\n            return self._handle_database_selection(build_config, field_value)\n\n        # Collection selection change\n        if field_name == \"collection_name\" and not isinstance(field_value, dict):\n            return self._handle_collection_selection(build_config, field_value)\n\n        return build_config\n\n    async def _create_new_database(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new database and update build config options.\"\"\"\n        try:\n            await self.create_database_api(\n                new_database_name=field_value[\"01_new_database_name\"],\n                token=self.token,\n                keyspace=self.get_keyspace(),\n                environment=self.environment,\n                cloud_provider=field_value[\"02_cloud_provider\"],\n                region=field_value[\"03_region\"],\n            )\n        except Exception as e:\n            msg = f\"Error creating database: {e}\"\n            raise ValueError(msg) from e\n\n        build_config[\"database_name\"][\"options\"].append(field_value[\"01_new_database_name\"])\n        build_config[\"database_name\"][\"options_metadata\"].append(\n            {\n                \"status\": \"PENDING\",\n                \"collections\": 0,\n                \"api_endpoint\": None,\n                \"org_id\": None,\n            }\n        )\n\n    def _update_cloud_regions(self, build_config: dict, field_value: dict) -> dict:\n        \"\"\"Update cloud provider regions in build config.\"\"\"\n        env = self.environment or \"prod\"\n        cloud_provider = field_value[\"02_cloud_provider\"]\n\n        # Update the region options based on the selected cloud provider\n        template = build_config[\"database_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"03_region\"][\"options\"] = self.map_cloud_providers()[env][cloud_provider][\"regions\"]\n\n        # Reset the the 03_region value if it's not in the new options\n        if template[\"03_region\"][\"value\"] not in template[\"03_region\"][\"options\"]:\n            template[\"03_region\"][\"value\"] = None\n\n        return build_config\n\n    async def _create_new_collection(self, build_config: dict, field_value: dict) -> None:\n        \"\"\"Create a new collection and update build config options.\"\"\"\n        embedding_provider = field_value.get(\"02_embedding_generation_provider\")\n        try:\n            await self.create_collection_api(\n                new_collection_name=field_value[\"01_new_collection_name\"],\n                token=self.token,\n                api_endpoint=build_config[\"api_endpoint\"][\"value\"],\n                environment=self.environment,\n                keyspace=self.get_keyspace(),\n                dimension=field_value.get(\"04_dimension\") if embedding_provider == \"Bring your own\" else None,\n                embedding_generation_provider=embedding_provider,\n                embedding_generation_model=field_value.get(\"03_embedding_generation_model\"),\n            )\n        except Exception as e:\n            msg = f\"Error creating collection: {e}\"\n            raise ValueError(msg) from e\n\n        provider = embedding_provider.lower() if embedding_provider and embedding_provider != \"Bring your own\" else None\n        build_config[\"collection_name\"].update(\n            {\n                \"value\": field_value[\"01_new_collection_name\"],\n                \"options\": build_config[\"collection_name\"][\"options\"] + [field_value[\"01_new_collection_name\"]],\n            }\n        )\n        build_config[\"embedding_choice\"][\"value\"] = \"Astra Vectorize\" if provider else \"Embedding Model\"\n        build_config[\"embedding_model\"][\"advanced\"] = bool(provider)\n        build_config[\"collection_name\"][\"options_metadata\"].append(\n            {\n                \"records\": 0,\n                \"provider\": provider,\n                \"icon\": self.get_provider_icon(provider_name=embedding_provider),\n                \"model\": field_value.get(\"03_embedding_generation_model\"),\n            }\n        )\n\n    def _handle_database_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle database selection and update related configurations.\"\"\"\n        build_config = self.reset_database_list(build_config)\n\n        # Reset collection list if database selection changes\n        if field_value not in build_config[\"database_name\"][\"options\"]:\n            build_config[\"database_name\"][\"value\"] = \"\"\n            return build_config\n\n        # Get the api endpoint for the selected database\n        index = build_config[\"database_name\"][\"options\"].index(field_value)\n        build_config[\"api_endpoint\"][\"value\"] = build_config[\"database_name\"][\"options_metadata\"][index][\"api_endpoint\"]\n\n        # Get the org_id for the selected database\n        org_id = build_config[\"database_name\"][\"options_metadata\"][index][\"org_id\"]\n        if not org_id:\n            return build_config\n\n        # Get the database id for the selected database\n        db_id = self.get_database_id_static(api_endpoint=build_config[\"api_endpoint\"][\"value\"])\n        keyspace = self.get_keyspace() or \"default_keyspace\"\n\n        # Update the helper text for the embedding provider field\n        template = build_config[\"collection_name\"][\"dialog_inputs\"][\"fields\"][\"data\"][\"node\"][\"template\"]\n        template[\"02_embedding_generation_provider\"][\"helper_text\"] = (\n            \"To create collections with more embedding provider options, go to \"\n            f'<a class=\"underline\" target=\"_blank\" rel=\"noopener noreferrer\" '\n            f'href=\"https://astra.datastax.com/org/{org_id}/database/{db_id}/data-explorer?createCollection=1&namespace={keyspace}\">'\n            \"your database in Astra DB</a>.\"\n        )\n\n        # Reset provider options\n        build_config = self.reset_provider_options(build_config)\n\n        return self.reset_collection_list(build_config)\n\n    def _handle_collection_selection(self, build_config: dict, field_value: str) -> dict:\n        \"\"\"Handle collection selection and update embedding options.\"\"\"\n        build_config[\"autodetect_collection\"][\"value\"] = True\n        build_config = self.reset_collection_list(build_config)\n\n        if field_value and field_value not in build_config[\"collection_name\"][\"options\"]:\n            build_config[\"collection_name\"][\"options\"].append(field_value)\n            build_config[\"collection_name\"][\"options_metadata\"].append(\n                {\n                    \"records\": 0,\n                    \"provider\": None,\n                    \"icon\": \"vectorstores\",\n                    \"model\": None,\n                }\n            )\n            build_config[\"autodetect_collection\"][\"value\"] = False\n\n        if not field_value:\n            return build_config\n\n        index = build_config[\"collection_name\"][\"options\"].index(field_value)\n        provider = build_config[\"collection_name\"][\"options_metadata\"][index][\"provider\"]\n        build_config[\"embedding_model\"][\"advanced\"] = bool(provider)\n        build_config[\"embedding_choice\"][\"value\"] = \"Astra Vectorize\" if provider else \"Embedding Model\"\n        return build_config\n\n    @check_cached_vector_store\n    def build_vector_store(self):\n        try:\n            from langchain_astradb import AstraDBVectorStore\n        except ImportError as e:\n            msg = (\n                \"Could not import langchain Astra DB integration package. \"\n                \"Please install it with `pip install langchain-astradb`.\"\n            )\n            raise ImportError(msg) from e\n\n        # Get the embedding model and additional params\n        embedding_params = (\n            {\"embedding\": self.embedding_model}\n            if self.embedding_model and self.embedding_choice == \"Embedding Model\"\n            else {}\n        )\n\n        # Get the additional parameters\n        additional_params = self.astradb_vectorstore_kwargs or {}\n\n        # Get Langflow version and platform information\n        __version__ = get_version_info()[\"version\"]\n        langflow_prefix = \"\"\n        # if os.getenv(\"AWS_EXECUTION_ENV\") == \"AWS_ECS_FARGATE\":  # TODO: More precise way of detecting\n        #     langflow_prefix = \"ds-\"\n\n        # Get the database object\n        database = self.get_database_object()\n        autodetect = self.collection_name in database.list_collection_names() and self.autodetect_collection\n\n        # Bundle up the auto-detect parameters\n        autodetect_params = {\n            \"autodetect_collection\": autodetect,\n            \"content_field\": (\n                self.content_field\n                if self.content_field and embedding_params\n                else (\n                    \"page_content\"\n                    if embedding_params\n                    and self.collection_data(collection_name=self.collection_name, database=database) == 0\n                    else None\n                )\n            ),\n            \"ignore_invalid_documents\": self.ignore_invalid_documents,\n        }\n\n        # Attempt to build the Vector Store object\n        try:\n            vector_store = AstraDBVectorStore(\n                # Astra DB Authentication Parameters\n                token=self.token,\n                api_endpoint=database.api_endpoint,\n                namespace=database.keyspace,\n                collection_name=self.collection_name,\n                environment=self.environment,\n                # Astra DB Usage Tracking Parameters\n                ext_callers=[(f\"{langflow_prefix}langflow\", __version__)],\n                # Astra DB Vector Store Parameters\n                **autodetect_params,\n                **embedding_params,\n                **additional_params,\n            )\n        except Exception as e:\n            msg = f\"Error initializing AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        # Add documents to the vector store\n        self._add_documents_to_vector_store(vector_store)\n\n        return vector_store\n\n    def _add_documents_to_vector_store(self, vector_store) -> None:\n        self.ingest_data = self._prepare_ingest_data()\n\n        documents = []\n        for _input in self.ingest_data or []:\n            if isinstance(_input, Data):\n                documents.append(_input.to_lc_document())\n            else:\n                msg = \"Vector Store Inputs must be Data objects.\"\n                raise TypeError(msg)\n\n        if documents and self.deletion_field:\n            self.log(f\"Deleting documents where {self.deletion_field}\")\n            try:\n                database = self.get_database_object()\n                collection = database.get_collection(self.collection_name, keyspace=database.keyspace)\n                delete_values = list({doc.metadata[self.deletion_field] for doc in documents})\n                self.log(f\"Deleting documents where {self.deletion_field} matches {delete_values}.\")\n                collection.delete_many({f\"metadata.{self.deletion_field}\": {\"$in\": delete_values}})\n            except Exception as e:\n                msg = f\"Error deleting documents from AstraDBVectorStore based on '{self.deletion_field}': {e}\"\n                raise ValueError(msg) from e\n\n        if documents:\n            self.log(f\"Adding {len(documents)} documents to the Vector Store.\")\n            try:\n                vector_store.add_documents(documents)\n            except Exception as e:\n                msg = f\"Error adding documents to AstraDBVectorStore: {e}\"\n                raise ValueError(msg) from e\n        else:\n            self.log(\"No documents to add to the Vector Store.\")\n\n    def _map_search_type(self) -> str:\n        search_type_mapping = {\n            \"Similarity with score threshold\": \"similarity_score_threshold\",\n            \"MMR (Max Marginal Relevance)\": \"mmr\",\n        }\n\n        return search_type_mapping.get(self.search_type, \"similarity\")\n\n    def _build_search_args(self):\n        query = self.search_query if isinstance(self.search_query, str) and self.search_query.strip() else None\n\n        if query:\n            args = {\n                \"query\": query,\n                \"search_type\": self._map_search_type(),\n                \"k\": self.number_of_results,\n                \"score_threshold\": self.search_score_threshold,\n            }\n        elif self.advanced_search_filter:\n            args = {\n                \"n\": self.number_of_results,\n            }\n        else:\n            return {}\n\n        filter_arg = self.advanced_search_filter or {}\n        if filter_arg:\n            args[\"filter\"] = filter_arg\n\n        return args\n\n    def search_documents(self, vector_store=None) -> list[Data]:\n        vector_store = vector_store or self.build_vector_store()\n\n        self.log(f\"Search input: {self.search_query}\")\n        self.log(f\"Search type: {self.search_type}\")\n        self.log(f\"Number of results: {self.number_of_results}\")\n\n        try:\n            search_args = self._build_search_args()\n        except Exception as e:\n            msg = f\"Error in AstraDBVectorStore._build_search_args: {e}\"\n            raise ValueError(msg) from e\n\n        if not search_args:\n            self.log(\"No search input or filters provided. Skipping search.\")\n            return []\n\n        docs = []\n        search_method = \"search\" if \"query\" in search_args else \"metadata_search\"\n\n        try:\n            self.log(f\"Calling vector_store.{search_method} with args: {search_args}\")\n            docs = getattr(vector_store, search_method)(**search_args)\n        except Exception as e:\n            msg = f\"Error performing {search_method} in AstraDBVectorStore: {e}\"\n            raise ValueError(msg) from e\n\n        self.log(f\"Retrieved documents: {len(docs)}\")\n\n        data = docs_to_data(docs)\n        self.log(f\"Converted documents to data: {len(data)}\")\n        self.status = data\n\n        return data\n\n    def get_retriever_kwargs(self):\n        search_args = self._build_search_args()\n\n        return {\n            \"search_type\": self._map_search_type(),\n            \"search_kwargs\": search_args,\n        }\n"}, "collection_name": {"_input_type": "DropdownInput", "advanced": true, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several seconds for creation to complete.", "display_name": "Create new collection", "field_order": ["01_new_collection_name", "02_embedding_generation_provider", "03_embedding_generation_model", "04_dimension"], "name": "create_collection", "template": {"01_new_collection_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new collection to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_collection_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_embedding_generation_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding generation method", "dynamic": false, "info": "Provider to use for generating embeddings.", "name": "embedding_generation_provider", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_embedding_generation_model": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding model", "dynamic": false, "info": "Model to use for generating embeddings.", "name": "embedding_generation_model", "options": [], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "04_dimension": {"_input_type": "IntInput", "advanced": false, "display_name": "Dimensions (Required only for `Bring your own`)", "dynamic": false, "info": "Dimensions of the embeddings to generate.", "list": false, "list_add_label": "Add More", "name": "dimension", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 1024}}}}}, "functionality": "create"}, "display_name": "Collection", "dynamic": false, "info": "The name of the collection within Astra DB where the vectors will be stored.", "name": "collection_name", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "content_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Content Field", "dynamic": false, "info": "Field to use as the text content field for the vector store.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "content_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "database_name": {"_input_type": "DropdownInput", "advanced": false, "combobox": true, "dialog_inputs": {"fields": {"data": {"node": {"description": "Please allow several minutes for creation to complete.", "display_name": "Create new database", "field_order": ["01_new_database_name", "02_cloud_provider", "03_region"], "name": "create_database", "template": {"01_new_database_name": {"_input_type": "StrInput", "advanced": false, "display_name": "Name", "dynamic": false, "info": "Name of the new database to create in Astra DB.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "new_database_name", "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "02_cloud_provider": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Cloud provider", "dynamic": false, "info": "Cloud provider for the new database.", "name": "cloud_provider", "options": ["Amazon Web Services", "Google Cloud Platform", "Microsoft Azure"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "03_region": {"_input_type": "DropdownInput", "advanced": false, "combobox": false, "dialog_inputs": {}, "display_name": "Region", "dynamic": false, "info": "Region for the new database.", "name": "region", "options": [], "options_metadata": [], "placeholder": "", "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}}}}}, "functionality": "create"}, "display_name": "Database", "dynamic": false, "info": "The Database name for the Astra DB instance.", "name": "database_name", "options": [], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "refresh_button": true, "required": true, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "deletion_field": {"_input_type": "StrInput", "advanced": true, "display_name": "Deletion Based On Field", "dynamic": false, "info": "When this parameter is provided, documents in the target collection with metadata field values matching the input metadata field value will be deleted before new data is loaded.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "deletion_field", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "embedding_choice": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Embedding Model or Astra Vectorize", "dynamic": false, "info": "Choose an embedding model or use Astra Vectorize.", "name": "embedding_choice", "options": ["Embedding Model", "Astra Vectorize"], "options_metadata": [], "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Embedding Model"}, "embedding_model": {"_input_type": "HandleInput", "advanced": false, "display_name": "Embedding Model", "dynamic": false, "info": "Specify the Embedding Model. Not required for Astra Vectorize collections.", "input_types": ["Embeddings"], "list": false, "list_add_label": "Add More", "name": "embedding_model", "placeholder": "", "required": false, "show": true, "title_case": false, "trace_as_metadata": true, "type": "other", "value": ""}, "environment": {"_input_type": "StrInput", "advanced": true, "display_name": "Environment", "dynamic": false, "info": "The environment for the Astra DB API Endpoint.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "environment", "placeholder": "", "real_time_refresh": true, "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "ignore_invalid_documents": {"_input_type": "BoolInput", "advanced": true, "display_name": "Ignore Invalid Documents", "dynamic": false, "info": "Boolean flag to determine whether to ignore invalid documents at runtime.", "list": false, "list_add_label": "Add More", "name": "ignore_invalid_documents", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": false}, "ingest_data": {"_input_type": "DataInput", "advanced": false, "display_name": "导入数据", "dynamic": false, "info": "", "input_types": ["Data", "DataFrame"], "list": true, "list_add_label": "Add More", "name": "ingest_data", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_input": true, "trace_as_metadata": true, "type": "other", "value": ""}, "keyspace": {"_input_type": "StrInput", "advanced": true, "display_name": "Keyspace", "dynamic": false, "info": "Optional keyspace within Astra DB to use for the collection.", "list": false, "list_add_label": "Add More", "load_from_db": false, "name": "keyspace", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": ""}, "number_of_results": {"_input_type": "IntInput", "advanced": true, "display_name": "Number of Search Results", "dynamic": false, "info": "Number of search results to return.", "list": false, "list_add_label": "Add More", "name": "number_of_results", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "int", "value": 4}, "search_query": {"_input_type": "MultilineInput", "advanced": false, "display_name": "搜索查询", "dynamic": false, "info": "", "input_types": ["Message"], "list": false, "list_add_label": "Add More", "load_from_db": false, "multiline": true, "name": "search_query", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": true, "trace_as_input": true, "trace_as_metadata": true, "type": "str", "value": ""}, "search_score_threshold": {"_input_type": "FloatInput", "advanced": true, "display_name": "Search Score Threshold", "dynamic": false, "info": "Minimum similarity score threshold for search results. (when using 'Similarity with score threshold')", "list": false, "list_add_label": "Add More", "name": "search_score_threshold", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "float", "value": 0}, "search_type": {"_input_type": "DropdownInput", "advanced": true, "combobox": false, "dialog_inputs": {}, "display_name": "Search Type", "dynamic": false, "info": "Search type to use", "name": "search_type", "options": ["Similarity", "Similarity with score threshold", "MMR (Max Marginal Relevance)"], "options_metadata": [], "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "str", "value": "Similarity"}, "should_cache_vector_store": {"_input_type": "BoolInput", "advanced": true, "display_name": "缓存向量存储", "dynamic": false, "info": "如果为 True，则向量存储将在组件的当前构建中被缓存。这对于具有多个输出方法并希望共享相同向量存储的组件非常有用。", "list": false, "list_add_label": "Add More", "name": "should_cache_vector_store", "placeholder": "", "required": false, "show": true, "title_case": false, "tool_mode": false, "trace_as_metadata": true, "type": "bool", "value": true}, "token": {"_input_type": "SecretStrInput", "advanced": false, "display_name": "Astra DB Application Token", "dynamic": false, "info": "Authentication token for accessing Astra DB.", "input_types": [], "load_from_db": true, "name": "token", "password": true, "placeholder": "", "real_time_refresh": true, "required": true, "show": true, "title_case": false, "type": "str", "value": "ASTRA_DB_APPLICATION_TOKEN"}}, "tool_mode": false}, "showNode": true, "type": "AstraDB"}, "dragging": false, "id": "AstraDB-JF2lS", "measured": {"height": 532, "width": 320}, "position": {"x": 2051.8997493133134, "y": 1513.9120105159382}, "selected": false, "type": "genericNode"}], "viewport": {"x": 23.94695899838632, "y": -121.14595988693713, "zoom": 0.44406917240373706}}, "description": "使用向量存储和检索增强生成（RAG）技术的项目。", "endpoint_name": null, "id": "b9f87508-ce73-441d-ba7b-66144513c04e", "is_component": false, "last_tested_version": "1.2.0", "name": "向量存储检索增强生成（RAG）", "tags": ["openai", "astradb", "rag", "q-a"]}