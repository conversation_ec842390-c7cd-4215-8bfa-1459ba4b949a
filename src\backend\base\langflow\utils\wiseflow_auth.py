import hmac
import hashlib
import json
from typing import Dict, Any

class AuthUtil:
    def __init__(self, secret_key: str):
        self.secret_key = secret_key.encode('utf-8')

    def generate_signature(self, params: Dict[str, Any]) -> str:
        """生成HMAC-SHA256签名"""
        # 1. 参数按key排序后转为JSON字符串
        # sorted_params = json.dumps(params, sort_keys=True)
        # 2. 计算签名
        signature = hmac.new(
            key=self.secret_key,
            msg=params.encode('utf-8'),
            digestmod=hashlib.sha256
        ).hexdigest()
        return signature

    def verify_signature(self, params: Dict[str, Any], client_signature: str) -> bool:
        """验证签名是否合法"""
        server_signature = self.generate_signature(params)
        return hmac.compare_digest(server_signature, client_signature)
    
auth = AuthUtil(secret_key="8d9f62e621f0b22d18db0a71b68ea09d21818566d40712758b8c0c4d476b8bdd")