[project]
name = "langflow-base"
version = "0.2.0"
description = "A Python package with a built-in web application"
requires-python = ">=3.10,<3.14"
license = "MIT"
keywords = ["nlp", "langchain", "openai", "gpt", "gui"]
readme = "README.md"
maintainers = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "Cristhian Zanforlin", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "Italo dos Anjos", email = "<EMAIL>" },
]

dependencies = [
    "fastapi>=0.115.2,<1.0.0",
    "httpx[http2]>=0.27,<1.0.0",
    "aiofile>=3.9.0,<4.0.0",
    "uvicorn>=0.30.0,<1.0.0",
    "gunicorn>=22.0.0,<24.0.0",
    "langchain~=0.3.10",
    "langchain-community~=0.3.10",
    "langchain-core~=0.3.15",
    "langchainhub~=0.1.15",
    "loguru>=0.7.1,<1.0.0",
    "rich>=13.7.0,<14.0.0",
    "langchain-experimental>=0.0.61,<1.0.0",
    "sqlmodel==0.0.22",
    "pydantic~=2.10.1",
    "pydantic-settings>=2.2.0,<3.0.0",
    "typer>=0.13.0,<1.0.0",
    "cachetools>=5.5.0,<6.0.0",
    "platformdirs>=4.2.0,<5.0.0",
    "python-multipart>=0.0.12,<1.0.0",
    "orjson==3.10.15",
    "alembic>=1.13.0,<2.0.0",
    "passlib>=1.7.4,<2.0.0",
    "bcrypt==4.0.1",
    "pillow>=11.1.0,<12.0.0",
    "docstring-parser>=0.16,<1.0.0",
    "python-jose>=3.3.0,<4.0.0",
    "pandas==2.2.3",
    "multiprocess>=0.70.14,<1.0.0",
    "duckdb>=1.0.0,<2.0.0",
    "python-docx>=1.1.0,<2.0.0",
    "jq>=1.7.0,<2.0.0; sys_platform != 'win32'",
    "nest-asyncio>=1.6.0,<2.0.0",
    "emoji>=2.12.0,<3.0.0",
    "cryptography>=42.0.5,<44.0.0",
    "asyncer>=0.0.5,<1.0.0",
    "pyperclip>=1.8.2,<2.0.0",
    "uncurl>=0.0.11,<1.0.0",
    "sentry-sdk[fastapi,loguru]>=2.5.1,<3.0.0",
    "chardet>=5.2.0,<6.0.0",
    "firecrawl-py>=1.0.16,<2.0.0",
    "opentelemetry-api>=1.25.0,<2.0.0",
    "opentelemetry-sdk>=1.25.0,<2.0.0",
    "opentelemetry-exporter-prometheus>=0.46b0,<1.0.0",
    "opentelemetry-instrumentation-fastapi>=0.46b0,<1.0.0",
    "prometheus-client>=0.20.0,<1.0.0",
    "aiofiles>=24.1.0,<25.0.0",
    "setuptools>=70,<76.0.0",
    "nanoid>=2.0.0,<3.0.0",
    "filelock>=3.15.4,<4.0.0",
    "grandalf>=0.8.0,<1.0.0",
    "spider-client>=0.0.27,<1.0.0",
    "diskcache>=5.6.3,<6.0.0",
    "clickhouse-connect==0.7.19",
    "assemblyai>=0.33.0,<1.0.0",
    "fastapi-pagination>=0.12.29,<1.0.0",
    "defusedxml>=0.7.1,<1.0.0",
    "pypdf~=5.1.0",
    "validators>=0.34.0",
    "networkx>=3.4.2",
    "json-repair>=0.30.3",
    "mcp>=1.1.2",
    "aiosqlite>=0.20.0",
    "greenlet>=3.1.1",
    "jsonquerylang>=1.1.1",
    "sqlalchemy[aiosqlite]>=2.0.38,<3.0.0",
    "elevenlabs>=1.54.0",
    "webrtcvad>=2.0.10",
    "scipy>=1.15.2",
]

[dependency-groups]
dev = [
    "asgi-lifespan>=2.1.0",
    "pytest-codspeed>=3.0.0",
    "pytest-github-actions-annotate-failures>=0.2.0",
    "types-aiofiles>=24.1.0.20240626",
    "types-redis>=*******",
    "ipykernel>=6.29.0",
    "mypy>=1.11.0",
    "ruff>=0.6.2",
    "httpx[http2]>=0.27",
    "pytest>=8.2.0",
    "types-requests>=2.32.0",
    "requests>=2.32.0",
    "pytest-cov>=5.0.0",
    "pandas-stubs>=2.1.4.231227",
    "types-pillow>=10.2.0.20240213",
    "types-pyyaml>=********",
    "types-python-jose>=*******",
    "types-passlib>=********",
    "pytest-mock>=3.14.0",
    "pytest-xdist>=3.6.0",
    "types-pywin32>=306.0.0.4",
    "types-google-cloud-ndb>=*******",
    "pytest-sugar>=1.0.0",
    "respx>=0.21.1",
    "pytest-instafail>=0.5.0",
    "pytest-asyncio>=0.23.0",
    "pytest-profiling>=1.7.0",
    "pre-commit>=3.7.0",
    "vulture>=2.11",
    "dictdiffer>=0.9.0",
    "pytest-split>=0.9.0",
    "devtools>=0.12.2",
    "pytest-flakefinder>=1.1.0",
    "types-markdown>=3.7.0.20240822",
    "codeflash>=0.8.4"

]


[tool.hatch.build.targets.wheel]
packages = ["langflow"]


[tool.pytest.ini_options]
minversion = "6.0"
#addopts = "-ra"
testpaths = ["tests", "integration"]
console_output_style = "progress"
filterwarnings = ["ignore::DeprecationWarning"]
log_cli = true
markers = ["async_test"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.mypy]
plugins = ["pydantic.mypy"]
follow_imports = "skip"
disable_error_code = ["type-var"]
namespace_packages = true
mypy_path = "langflow"
ignore_missing_imports = true

[tool.ruff]
target-version = "py310"
exclude = ["langflow/alembic/versions"]
line-length = 120

[tool.ruff.lint]
flake8-annotations.mypy-init-return = true
flake8-bugbear.extend-immutable-calls = [
    "fastapi.Depends",
    "fastapi.File",
    "fastapi.Query",
    "typer.Option",
]
flake8-type-checking.runtime-evaluated-base-classes = [
    "pydantic.BaseModel",
    "typing.TypedDict", # Needed by fastapi
    "typing_extensions.TypedDict", # Needed by fastapi
]
pydocstyle.convention = "google"
select = ["ALL"]
ignore = [
    "C90", # McCabe complexity
    "CPY", # Missing copyright
    "COM812", # Messes with the formatter
    "ERA", # Eradicate commented-out code
    "FIX002", # Line contains TODO
    "ISC001", # Messes with the formatter
    "PERF203", # Rarely useful
    "PLR09", # Too many something (arg, statements, etc)
    "RUF012", # Pydantic models are currently not well detected. See https://github.com/astral-sh/ruff/issues/13630
    "TD002", # Missing author in TODO
    "TD003", # Missing issue link in TODO
    "TRY301", # A bit too harsh (Abstract `raise` to an inner function)

    # Rules that are TODOs
    "ANN", # Missing type annotations
    "D1", # Missing docstrings
    "SLF001", # Using private attributes outside of class
]

[tool.ruff.lint.per-file-ignores]
"langflow/api/v1/*" = [
    "TCH", # FastAPI needs to evaluate types at runtime
]
"langflow/api/v2/*" = [
    "TCH", # FastAPI needs to evaluate types at runtime
]
"langflow/{components/tools/python_code_structured_tool.py,custom/code_parser/code_parser.py,utils/validate.py}" = [
    "S102", # Use of exec
]
"langflow/services/cache/*" = [
    "S301", # Use of pickle
]

[tool.codeflash]
# All paths are relative to this pyproject.toml's directory.
module-root = "langflow"
tests-root = "../tests/unit"
test-framework = "pytest"
ignore-paths = ["langflow/components/"]
formatter-cmds = ["ruff check --exit-zero --fix $file", "ruff format $file"]
#disable plugins that might interfere with runtime measurement
pytest-cmd = "pytest -p no:profiling -p no:sugar -p no:xdist -p no:cov -p no:split"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"





[project.urls]
Repository = "https://github.com/langflow-ai/langflow"
Documentation = "https://docs.langflow.org"

# Optional dependencies for uv
[project.optional-dependencies]

postgresql = [
  "sqlalchemy[postgresql_psycopg2binary]",
    "sqlalchemy[postgresql_psycopg]",

]
deploy = [
    "celery>=5.3.1",
    "redis>=4.6.0",
    "flower>=1.0.0"
]
local = [
    "llama-cpp-python>=0.2.0",
    "sentence-transformers>=2.0.0",
    "ctransformers>=0.2"
]
all = [
    "celery>=5.3.1",
    "redis>=4.6.0",
    "flower>=1.0.0",
    "llama-cpp-python>=0.2.0",
    "sentence-transformers>=2.0.0",
    "ctransformers>=0.2"
]

[project.scripts]
langflow-base = "langflow.__main__:main"
