{"name": "langflow", "version": "1.2.0", "private": true, "dependencies": {"@chakra-ui/number-input": "^2.1.2", "@chakra-ui/system": "^2.6.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.0.4", "@hookform/resolvers": "^3.6.0", "@million/lint": "^1.0.0-rc.26", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "@tabler/icons-react": "^3.6.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.49.2", "@testing-library/dom": "^10.4.0", "@types/axios": "^0.14.0", "@xyflow/react": "^12.3.6", "ace-builds": "^1.35.0", "ag-grid-community": "^32.0.2", "ag-grid-react": "^32.0.2", "ansi-to-html": "^0.7.2", "axios": "^1.7.4", "base64-js": "^1.5.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "core-js": "^3.45.0", "dompurify": "^3.2.4", "dotenv": "^16.4.5", "elkjs": "^0.9.3", "emoji-regex": "^10.3.0", "esbuild": "^0.21.5", "fetch-intercept": "^2.4.0", "file-saver": "^2.0.5", "framer-motion": "^11.2.10", "fuse.js": "^7.0.0", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "i18next-parser": "^9.3.0", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "million": "^3.1.11", "moment": "^2.30.1", "node": "^20.19.0", "node-gyp": "^11.1.0", "openseadragon": "^4.1.1", "p-debounce": "^4.0.0", "pako": "^2.1.0", "playwright": "^1.44.1", "pretty-ms": "^9.1.0", "react": "^18.3.1", "react-ace": "^11.0.1", "react-cookie": "^7.1.4", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.52.0", "react-hotkeys-hook": "^4.5.0", "react-i18next": "^15.4.1", "react-icons": "^5.2.1", "react-laag": "^2.0.5", "react-markdown": "^8.0.7", "react-pdf": "^9.0.0", "react-router-dom": "^6.23.1", "react-syntax-highlighter": "^15.6.1", "reactflow": "^11.11.3", "rehype-mathjax": "^4.0.3", "rehype-raw": "^6.1.1", "remark-gfm": "3.0.1", "remark-math": "^6.0.0", "shadcn-ui": "^0.9.4", "short-unique-id": "^5.2.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "vanilla-jsoneditor": "^2.3.3", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "web-vitals": "^4.1.1", "zod": "^3.23.8", "zustand": "^4.5.2"}, "scripts": {"dev:docker": "vite --host 0.0.0.0", "start": "vite", "build": "node --max-old-space-size=12288 ./node_modules/vite/bin/vite.js build", "serve": "vite preview", "format": "npx prettier --write \"{tests,src}/**/*.{js,jsx,ts,tsx,json,md}\" --ignore-path .prettierignore", "check-format": "npx prettier --check \"{tests,src}/**/*.{js,jsx,ts,tsx,json,md}\" --ignore-path .prettierignore", "type-check": "tsc --noEmit --pretty --project tsconfig.json && vite", "i18n:extract": "npx i18next-parser src/**/*.{js,jsx,ts,tsx} -o src/i18n/locales/zh/translation.json"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://127.0.0.1:7860", "devDependencies": {"@playwright/test": "^1.44.1", "@swc/cli": "^0.5.2", "@swc/core": "^1.6.1", "@tailwindcss/typography": "^0.5.13", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/lodash": "4.17.5", "@types/node": "^20.14.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.8", "@vitejs/plugin-legacy": "^5.3.0", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.19", "eslint": "^9.5.0", "postcss": "^8.4.38", "prettier": "^3.3.2", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.6.5", "simple-git-hooks": "^2.11.1", "tailwindcss": "^3.4.4", "tailwindcss-dotted-background": "^1.1.0", "typescript": "^5.4.5", "ua-parser-js": "^1.0.38", "vite": "^5.4.14"}}