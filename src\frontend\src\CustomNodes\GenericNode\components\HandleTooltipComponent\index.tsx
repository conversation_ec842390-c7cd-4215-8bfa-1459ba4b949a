import { convertTestName } from "@/components/common/storeCardComponent/utils/convert-test-name";
import { Badge } from "@/components/ui/badge";
import { nodeColorsName } from "@/utils/styleUtils";
import { useTranslation } from "react-i18next";

export default function HandleTooltipComponent({
  isInput,
  tooltipTitle,
  isConnecting,
  isCompatible,
  isSameNode,
  left,
}: {
  isInput: boolean;
  tooltipTitle: string;
  isConnecting: boolean;
  isCompatible: boolean;
  isSameNode: boolean;
  left: boolean;
}) {
  const { t } = useTranslation();
  const tooltips = tooltipTitle.split("\n");
  const plural = tooltips.length > 1 ? "s" : "";
  

  return (
    <div className="font-medium">
      {isSameNode ? (
        t('cant-connect-to-the-same-node')
      ) : (
        <div className="flex items-center gap-1.5">
          {isConnecting ? (
            isCompatible ? (
              <span>
                <span className="font-semibold">{t('connect')}</span> to
              </span>
            ) : (
              <span>{t('incompatible-with')}</span>
            )
          ) : (
            <span className="text-xs">
              {isInput
                ? t('input-plural-type-plural',{plural : plural})
                : t('output-plural-type-plural',{plural : plural})}
              :{" "}
            </span>
          )}
          {tooltips.map((word, index) => (
            <Badge
              className="h-6 rounded-md p-1"
              key={`${index}-${word.toLowerCase()}`}
              style={{
                backgroundColor: left
                  ? `hsl(var(--datatype-${nodeColorsName[word]}))`
                  : `hsl(var(--datatype-${nodeColorsName[word]}-foreground))`,
                color: left
                  ? `hsl(var(--datatype-${nodeColorsName[word]}-foreground))`
                  : `hsl(var(--datatype-${nodeColorsName[word]}))`,
              }}
              data-testid={`${isInput ? "input" : "output"}-tooltip-${convertTestName(word)}`}
            >
              {word}
            </Badge>
          ))}
          {isConnecting && <span>{isInput ? t('input') : t('output')}</span>}
        </div>
      )}
      {!isConnecting && (
        <div className="mt-2 flex flex-col gap-0.5 text-xs leading-6">
          <div>
            <b>{t('drag')}</b> {t('to-connect-compatible',{input: (!isInput ? "inputs" : "outputs")})} 
          </div>
          <div>
            <b>{t('click')}</b> {t('to-filter-compatible',{ input: (!isInput ? "inputs" : "outputs")})} {" "}
            {t('and-components')}
          </div>
        </div>
      )}
    </div>
  );
}
