import { Badge } from "@/components/ui/badge";
import { ICON_STROKE_WIDTH } from "@/constants/constants";
import { useShortcutsStore } from "@/stores/shortcuts";
import { targetHandleType } from "@/types/flow";
import { useUpdateNodeInternals } from "@xyflow/react";
import { cloneDeep } from "lodash";
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";
import ForwardedIconComponent, {
  default as IconComponent,
} from "../../../../components/common/genericIconComponent";
import ShadTooltip from "../../../../components/common/shadTooltipComponent";
import { Button } from "../../../../components/ui/button";
import useFlowStore from "../../../../stores/flowStore";
import { useTypesStore } from "../../../../stores/typesStore";
import { NodeOutputFieldComponentType } from "../../../../types/components";
import {
  getGroupOutputNodeId,
  scapedJSONStringfy,
  scapeJSONParse,
} from "../../../../utils/reactflowUtils";
import {
  cn,
  logFirstMessage,
  logHasMessage,
  logTypeIsError,
  logTypeIsUnknown,
} from "../../../../utils/utils";
import OutputComponent from "../OutputComponent";
import HandleRenderComponent from "../handleRenderComponent";
import OutputModal from "../outputModal";
import { useTranslation } from "react-i18next";

// Memoize IconComponent instances
const EyeIcon = memo(
  ({ hidden, className }: { hidden: boolean; className: string }) => (
    <IconComponent
      className={className}
      strokeWidth={ICON_STROKE_WIDTH}
      name={hidden ? "EyeOff" : "Eye"}
    />
  ),
);

const SnowflakeIcon = memo(() => (
  <IconComponent className="h-5 w-5 text-ice" name="Snowflake" />
));

// Memoize Button components
const HideShowButton = memo(
  ({
    disabled,
    onClick,
    hidden,
    isToolMode,
    title,
  }: {
    disabled: boolean;
    onClick: () => void;
    hidden: boolean;
    isToolMode: boolean;
    title: string;
  }) => {
    const { t } = useTranslation();
    return (
      <Button
        disabled={disabled}
        unstyled
        onClick={onClick}
        data-testid={`input-inspection-${title.toLowerCase()}`}
        className="cursor-pointer"
      >
        <ShadTooltip
          content={disabled ? null : hidden ? t('show-output') : t('hide-output')}
        >
          <div>
            <EyeIcon
              hidden={hidden}
              className={cn(
                "icon-size",
                disabled
                  ? isToolMode
                    ? "text-placeholder-foreground opacity-60"
                    : "text-placeholder-foreground hover:text-foreground"
                  : isToolMode
                    ? "text-background hover:text-secondary-hover"
                    : "text-placeholder-foreground hover:text-primary-hover",
              )}
            />
          </div>
        </ShadTooltip>
      </Button>
    );
  }
);

const InspectButton = memo(
  forwardRef(
    (
      {
        disabled,
        displayOutputPreview,
        unknownOutput,
        errorOutput,
        isToolMode,
        title,
        onClick,
        id,
      }: {
        disabled: boolean | undefined;
        displayOutputPreview: boolean;
        unknownOutput: boolean | undefined;
        errorOutput: boolean;
        isToolMode: boolean;
        title: string;
        onClick: () => void;
        id: string;
      },
      ref: React.ForwardedRef<HTMLButtonElement>,
    ) => (
      <Button
        ref={ref}
        disabled={disabled}
        data-testid={`output-inspection-${title.toLowerCase()}-${id.toLowerCase()}`}
        unstyled
        onClick={onClick}
      >
        <IconComponent
          name="TextSearchIcon"
          strokeWidth={ICON_STROKE_WIDTH}
          className={cn(
            "icon-size",
            isToolMode
              ? displayOutputPreview && !unknownOutput
                ? "text-background hover:text-secondary-hover"
                : "cursor-not-allowed text-placeholder-foreground opacity-80"
              : displayOutputPreview && !unknownOutput
                ? "text-foreground hover:text-primary-hover"
                : "cursor-not-allowed text-placeholder-foreground opacity-60",
            errorOutput ? "text-destructive" : "",
          )}
        />
      </Button>
    ),
  ),
);
InspectButton.displayName = "InspectButton";

const MemoizedOutputComponent = memo(OutputComponent);

function NodeOutputField({
  selected,
  data,
  title,
  id,
  colors,
  tooltipTitle,
  showNode,
  index,
  type,
  outputName,
  outputProxy,
  lastOutput,
  colorName,
  isToolMode = false,
}: NodeOutputFieldComponentType): JSX.Element {
  const ref = useRef<HTMLDivElement>(null);
  const updateNodeInternals = useUpdateNodeInternals();

  // Use selective store subscriptions
  const nodes = useFlowStore((state) => state.nodes);
  const edges = useFlowStore((state) => state.edges);
  const setNode = useFlowStore((state) => state.setNode);
  const setFilterEdge = useFlowStore((state) => state.setFilterEdge);
  const flowPool = useFlowStore((state) => state.flowPool);
  const myData = useTypesStore((state) => state.data);

  // Memoize computed values
  const { flowPoolId, internalOutputName } = useMemo(() => {
    if (data.node?.flow && outputProxy) {
      const realOutput = getGroupOutputNodeId(
        data.node.flow,
        outputProxy.name,
        outputProxy.id,
      );
      if (realOutput) {
        return {
          flowPoolId: realOutput.id,
          internalOutputName: realOutput.outputName,
        };
      }
    }
    return { flowPoolId: data.id, internalOutputName: outputName };
  }, [data.id, data.node?.flow, outputProxy, outputName]);

  const flowPoolNode = useMemo(() => {
    const pool = flowPool[flowPoolId] ?? [];
    return pool[pool.length - 1];
  }, [flowPool, flowPoolId]);

  const { displayOutputPreview, unknownOutput, errorOutput } = useMemo(
    () => ({
      displayOutputPreview:
        !!flowPool[flowPoolId] &&
        logHasMessage(flowPoolNode?.data, internalOutputName),
      unknownOutput: logTypeIsUnknown(flowPoolNode?.data, internalOutputName),
      errorOutput: logTypeIsError(flowPoolNode?.data, internalOutputName),
    }),
    [flowPool, flowPoolId, flowPoolNode?.data, internalOutputName],
  );

  const disabledOutput = useMemo(
    () => edges.some((edge) => edge.sourceHandle === scapedJSONStringfy(id)),
    [edges, id],
  );

  const looping = useMemo(() => {
    return edges.some((edge) => {
      const targetHandleObject: targetHandleType = scapeJSONParse(
        edge.targetHandle!,
      );
      return (
        targetHandleObject.output_types &&
        edge.sourceHandle === scapedJSONStringfy(id)
      );
    });
  }, [edges, id]);

  const handleUpdateOutputHide = useCallback(
    (value?: boolean) => {
      setNode(data.id, (oldNode) => {
        if (oldNode.type !== "genericNode") return oldNode;
        let newNode = cloneDeep(oldNode);
        newNode.data = {
          ...newNode.data,
          node: {
            ...newNode.data.node,
            outputs: newNode.data.node.outputs?.map((output, i) => {
              if (i === index) {
                output.hidden = value ?? !output.hidden;
              }
              return output;
            }),
          },
        };
        return newNode;
      });
      updateNodeInternals(data.id);
    },
    [data.id, index, setNode, updateNodeInternals],
  );

  useEffect(() => {
    if (disabledOutput && data.node?.outputs![index].hidden) {
      handleUpdateOutputHide(false);
    }
  }, [disabledOutput, data.node?.outputs, handleUpdateOutputHide, index]);

  const [openOutputModal, setOpenOutputModal] = useState(false);

  const outputShortcutOpenable = useMemo(() => {
    if (!displayOutputPreview || !selected) return;

    const sortedEdges = edges
      .filter((edge) => edge.source === data.id)
      .slice() // 创建副本
      .sort((a, b) => {
        const indexA =
          data?.node?.outputs?.findIndex(
            (output) => output.name === a.data?.sourceHandle?.name,
          ) ?? 0;
        const indexB =
          data?.node?.outputs?.findIndex(
            (output) => output.name === b.data?.sourceHandle?.name,
          ) ?? 0;
        return indexA - indexB;
      });

    const isFirstOutput =
      sortedEdges[0]?.sourceHandle === scapedJSONStringfy(id);
    const hasNoEdges = !edges.some((edge) => edge.source === data.id);
    const isValidFirstMessage =
      hasNoEdges && logFirstMessage(flowPoolNode?.data, internalOutputName);

    if (isFirstOutput || isValidFirstMessage) {
      return true;
    }
    return false;
  }, [displayOutputPreview, edges, data.id, data?.node?.outputs, selected]);

  const handleOpenOutputModal = () => {
    if (outputShortcutOpenable) {
      setOpenOutputModal(true);
    }
  };

  const outputInspection = useShortcutsStore((state) => state.outputInspection);
  useHotkeys(outputInspection, handleOpenOutputModal, { preventDefault: true });

  const LoopHandle = useMemo(() => {
    if (data.node?.outputs![index].allows_loop) {
      return (
        <HandleRenderComponent
          left={true}
          nodes={nodes}
          tooltipTitle={tooltipTitle}
          id={id}
          title={title}
          edges={edges}
          nodeId={data.id}
          myData={myData}
          colors={colors}
          setFilterEdge={setFilterEdge}
          showNode={showNode}
          testIdComplement={`${data?.type?.toLowerCase()}-${showNode ? "shownode" : "noshownode"}`}
          colorName={colorName}
        />
      );
    }
  }, [
    nodes,
    tooltipTitle,
    id,
    title,
    edges,
    data.id,
    myData,
    colors,
    setFilterEdge,
    showNode,
    data?.type,
    colorName,
  ]);

  const Handle = useMemo(
    () => (
      <HandleRenderComponent
        left={false}
        nodes={nodes}
        tooltipTitle={tooltipTitle}
        id={id}
        title={title}
        edges={edges}
        nodeId={data.id}
        myData={myData}
        colors={colors}
        setFilterEdge={setFilterEdge}
        showNode={showNode}
        testIdComplement={`${data?.type?.toLowerCase()}-${showNode ? "shownode" : "noshownode"}`}
        colorName={colorName}
      />
    ),
    [
      nodes,
      tooltipTitle,
      id,
      title,
      edges,
      data.id,
      myData,
      colors,
      setFilterEdge,
      showNode,
      data?.type,
      colorName,
    ],
  );

  if (!showNode) return <>{Handle}</>;
  const { t } = useTranslation();
  return (
    <div
      ref={ref}
      className={cn(
        "relative mt-1 flex h-11 w-full flex-wrap items-center justify-between bg-muted px-5 py-2",
        lastOutput ? "rounded-b-[0.69rem]" : "",
        isToolMode && "bg-primary",
      )}
    >
      {LoopHandle}
      <div className="flex w-full items-center justify-end truncate text-sm">
        <div className="flex flex-1">
          {data.node?.outputs![index].allows_loop && (
            <Badge variant="pinkStatic" size="xq" className="mr-2 px-1">
              <ForwardedIconComponent name="Infinity" className="h-4 w-4" />
            </Badge>
          )}
          <HideShowButton
            disabled={disabledOutput}
            onClick={() => handleUpdateOutputHide()}
            hidden={!!data.node?.outputs![index].hidden}
            isToolMode={isToolMode}
            title={title}
          />
        </div>

        {data.node?.frozen && (
          <div className="pr-1">
            <SnowflakeIcon />
          </div>
        )}

        <div className="flex items-center gap-2">
          <span className={data.node?.frozen ? "text-ice" : ""}>
            <MemoizedOutputComponent
              proxy={outputProxy}
              idx={index}
              types={type?.split("|") ?? []}
              selected={
                data.node?.outputs![index].selected ??
                data.node?.outputs![index].types[0] ??
                title
              }
              nodeId={data.id}
              frozen={data.node?.frozen}
              name={title ?? type}
              isToolMode={isToolMode}
            />
          </span>

          <ShadTooltip
            content={
              displayOutputPreview
                ? unknownOutput
                  ? t('output-cant-be-displayed')
                  : t('inspect-output')
                : t('please-build-the-component-first')
            }
            styleClasses="z-40"
          >
            <div className="flex items-center gap-2">
              <OutputModal
                open={openOutputModal}
                setOpen={setOpenOutputModal}
                disabled={!displayOutputPreview || unknownOutput}
                nodeId={flowPoolId}
                outputName={internalOutputName}
              >
                <InspectButton
                  disabled={!displayOutputPreview || unknownOutput}
                  displayOutputPreview={displayOutputPreview}
                  unknownOutput={unknownOutput ?? false}
                  errorOutput={errorOutput ?? false}
                  isToolMode={isToolMode}
                  title={title}
                  onClick={() => {
                    //just to trigger the memoization
                  }}
                  id={data?.type}
                />
              </OutputModal>
              {looping && (
                <Badge variant="pinkStatic" size="xq" className="px-1">
                  {t('looping')}
                </Badge>
              )}
            </div>
          </ShadTooltip>
        </div>
      </div>
      {Handle}
    </div>
  );
}

export default memo(NodeOutputField);
