import useAddFlow from "@/hooks/flows/use-add-flow";
import { getComponent } from "../../../../controllers/API";
import { storeComponent } from "../../../../types/store";
import cloneFlowWithParent from "../../../../utils/storeUtils";
import { useTranslation } from 'react-i18next';

const useInstallComponent = (
  data: storeComponent,
  name: string,
  isStore: boolean,
  downloadsCount: number,
  setDownloadsCount: (value: any) => void,
  setLoading: (value: boolean) => void,
  setSuccessData: (value: { title: string }) => void,
  setErrorData: (value: { title: string; list: string[] }) => void,
) => {
  const addFlow = useAddFlow();
  const { t } = useTranslation();
  
  const handleInstall = () => {
    const temp = downloadsCount;
    setDownloadsCount((old) => Number(old) + 1);
    setLoading(true);

    getComponent(data.id)
      .then((res) => {
        const newFlow = cloneFlowWithParent(res, res.id, data.is_component);
        addFlow({ flow: newFlow })
          .then((id) => {
            setSuccessData({              
              title: t('name-isstore-downloaded-installed-successfully', {name:`${name}`,action: (isStore ?  t('downloaded') : t('installed'))}),
            });
            setLoading(false);
          })
          .catch((error) => {
            setLoading(false);
            setErrorData({
              title: t('error-isstore-downloading-installing-the-name', {action: (isStore ?  t('downloaded') : t('installed')), name:`${name}`}),
              list: [error.response.data.detail],
            });
          });
      })
      .catch((err) => {
        setLoading(false);
        setErrorData({
          title: t('error-isstore-downloading-installing-the-name', {action: (isStore ?  t('downloaded') : t('installed')), name:`${name}`}),
          list: [err.response.data.detail],
        });
        setDownloadsCount(temp);
      });
  };

  return { handleInstall };
};

export default useInstallComponent;
