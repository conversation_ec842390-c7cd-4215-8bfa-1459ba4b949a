import { CustomCellEditorProps } from "ag-grid-react";
import InputComponent from "../../../inputComponent";
import { useTranslation } from "react-i18next";

export default function TableDropdownCellEditor({
  value,
  values,
  onValueChange,
  colDef,
}: CustomCellEditorProps & { values: string[] }) {
  const { t } = useTranslation();
  return (
    <div className="flex h-full items-center px-2">
      <InputComponent
        setSelectedOption={(value) => onValueChange(value)}
        value={value}
        options={values}
        password={false}
        placeholder={t('select-an-option')}
        id="apply-to-fields"
      />
    </div>
  );
}
