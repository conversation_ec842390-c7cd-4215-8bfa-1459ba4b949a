// ERROR
import i18n from "@/i18n";


export const MISSED_ERROR_ALERT = i18n.t('oops-looks-like-you-missed-something');
export const INCOMPLETE_LOOP_ERROR_ALERT =
  i18n.t('the-flow-has-an-incomplete-loop-check-your-connections-and-try-again');
export const INVALID_FILE_ALERT =
  i18n.t('please-select-a-valid-file-only-these-file-types-are-allowed');
export const CONSOLE_ERROR_MSG = i18n.t('error-occurred-while-uploading-file');
export const CONSOLE_SUCCESS_MSG = i18n.t('file-uploaded-successfully');
export const INFO_MISSING_ALERT =
  i18n.t('oops-looks-like-you-missed-some-required-information');
export const FUNC_ERROR_ALERT = i18n.t('there-is-an-error-in-your-function');
export const IMPORT_ERROR_ALERT = i18n.t('there-is-an-error-in-your-imports');
export const BUG_ALERT = i18n.t('something-went-wrong-please-try-again');
export const CODE_ERROR_ALERT =
  i18n.t('there-is-something-wrong-with-this-code-please-review-it');
export const CHAT_ERROR_ALERT =
  i18n.t('please-build-the-flow-again-before-using-the-chat');
export const MSG_ERROR_ALERT = i18n.t('there-was-an-error-sending-the-message');
export const PROMPT_ERROR_ALERT =
  i18n.t('there-is-something-wrong-with-this-prompt-please-review-it');
export const API_ERROR_ALERT =
  i18n.t('there-was-an-error-saving-the-api-key-please-try-again');
export const USER_DEL_ERROR_ALERT = i18n.t('error-on-delete-user');
export const USER_EDIT_ERROR_ALERT = i18n.t('error-on-edit-user');
export const USER_ADD_ERROR_ALERT = i18n.t('error-when-adding-new-user');
export const SIGNIN_ERROR_ALERT = i18n.t('error-signing-in');
export const DEL_KEY_ERROR_ALERT = i18n.t('error-on-delete-key');
export const DEL_KEY_ERROR_ALERT_PLURAL = i18n.t('error-on-delete-keys');
export const UPLOAD_ERROR_ALERT = i18n.t('error-uploading-file');
export const WRONG_FILE_ERROR_ALERT = i18n.t('invalid-file-type');
export const UPLOAD_ALERT_LIST = i18n.t('please-upload-a-json-file');
export const INVALID_SELECTION_ERROR_ALERT = i18n.t('invalid-selection');
export const EDIT_PASSWORD_ERROR_ALERT = i18n.t('error-changing-password');
export const EDIT_PASSWORD_ALERT_LIST = i18n.t('passwords-do-not-match');
export const SAVE_ERROR_ALERT = i18n.t('error-saving-changes');
export const PROFILE_PICTURES_GET_ERROR_ALERT =
  i18n.t('error-retrieving-profile-pictures');
export const SIGNUP_ERROR_ALERT = i18n.t('error-signing-up');
export const APIKEY_ERROR_ALERT = i18n.t('api-key-error');
export const NOAPI_ERROR_ALERT =
  i18n.t('you-dont-have-an-api-key-please-add-one-to-use-the-langflow-store');
export const INVALID_API_ERROR_ALERT =
  i18n.t('your-api-key-is-not-valid-please-add-a-valid-api-key-to-use-the-langflow-store');
export const COMPONENTS_ERROR_ALERT = i18n.t('error-getting-components');

// NOTICE
export const NOCHATOUTPUT_NOTICE_ALERT =
  i18n.t('there-is-no-chatoutput-component-in-the-flow');
export const API_WARNING_NOTICE_ALERT =
  i18n.t('warning-critical-data-json-file-may-include-api-keys');
export const COPIED_NOTICE_ALERT = i18n.t('api-key-copied');
export const TEMP_NOTICE_ALERT = i18n.t('your-template-does-not-have-any-variables');

// SUCCESS
export const CODE_SUCCESS_ALERT = i18n.t('code-is-ready-to-run');
export const PROMPT_SUCCESS_ALERT = i18n.t('prompt-is-ready');
export const API_SUCCESS_ALERT = i18n.t('success-your-api-key-has-been-saved');
export const USER_DEL_SUCCESS_ALERT = i18n.t('success-user-deleted');
export const USER_EDIT_SUCCESS_ALERT = i18n.t('success-user-edited');
export const USER_ADD_SUCCESS_ALERT = i18n.t('success-new-user-added');
export const DEL_KEY_SUCCESS_ALERT = i18n.t('success-key-deleted');
export const DEL_KEY_SUCCESS_ALERT_PLURAL = i18n.t('success-keys-deleted');
export const FLOW_BUILD_SUCCESS_ALERT = i18n.t('flow-built-successfully');
export const SAVE_SUCCESS_ALERT = i18n.t('changes-saved-successfully');
export const INVALID_FILE_SIZE_ALERT = (maxSizeMB) => {
  return i18n.t('the-file-size-is-too-large-please-select-a-file-smaller-than-maxsizemb',{maxSizeMB:maxSizeMB});
};
