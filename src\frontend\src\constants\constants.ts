// src/constants/constants.ts

import custom from "../customization/config-constants";
import { languageMap } from "../types/components";

import i18n from '../i18n';


/**
 * invalid characters for flow name
 * @constant
 */
export const INVALID_CHARACTERS = [
  " ",
  ",",
  ".",
  ":",
  ";",
  "!",
  "?",
  "/",
  "\\",
  "(",
  ")",
  "[",
  "]",
  "\n",
];

/**
 * regex to highlight the variables in the text
 * @constant regexHighlight
 * @type {RegExp}
 * @default
 * @example
 * {{variable}} or {variable}
 * @returns {RegExp}
 * @description
 * This regex is used to highlight the variables in the text.
 * It matches the variables in the text that are between {{}} or {}.
 */

export const regexHighlight = /\{\{(.*?)\}\}|\{([^{}]+)\}/g;
export const specialCharsRegex = /[!@#$%^&*()\-_=+[\]{}|;:'",.<>/?\\`´]/;

export const programmingLanguages: languageMap = {
  javascript: ".js",
  python: ".py",
  java: ".java",
  c: ".c",
  cpp: ".cpp",
  "c++": ".cpp",
  "c#": ".cs",
  ruby: ".rb",
  php: ".php",
  swift: ".swift",
  "objective-c": ".m",
  kotlin: ".kt",
  typescript: ".ts",
  go: ".go",
  perl: ".pl",
  rust: ".rs",
  scala: ".scala",
  haskell: ".hs",
  lua: ".lua",
  shell: ".sh",
  sql: ".sql",
  html: ".html",
  css: ".css",
  // add more file extensions here, make sure the key is same as language prop in CodeBlock.tsx component
};
/**
 * Number maximum of components to scroll on tooltips
 * @constant
 */
export const MAX_LENGTH_TO_SCROLL_TOOLTIP = 200;

export const MESSAGES_TABLE_ORDER = [
  "timestamp",
  "message",
  "text",
  "sender",
  "sender_name",
  "session_id",
  "files",
];

/**
 * Number maximum of components to scroll on tooltips
 * @constant
 */
export const MAX_WORDS_HIGHLIGHT = 79;

/**
 * Limit of items before show scroll on fields modal
 * @constant
 */
export const limitScrollFieldsModal = 10;

/**
 * The base text for subtitle of Export Dialog (Toolbar)
 * @constant
 */
export const EXPORT_DIALOG_SUBTITLE = i18n.t('export-flow-as-json-file');
/**
 * The base text for subtitle of Flow Settings (Menubar)
 * @constant
 */
export const SETTINGS_DIALOG_SUBTITLE =
  i18n.t('customize-your-flow-details-and-settings');

/**
 * The base text for subtitle of Flow Logs (Menubar)
 * @constant
 */
export const LOGS_DIALOG_SUBTITLE =
  i18n.t('explore-detailed-logs-of-events-and-transactions-between-components');

/**
 * The base text for subtitle of Code Dialog (Toolbar)
 * @constant
 */
export const CODE_DIALOG_SUBTITLE =
  i18n.t('export-your-flow-to-integrate-it-using-this-code');

/**
 * The base text for subtitle of Chat Form
 * @constant
 */
export const CHAT_FORM_DIALOG_SUBTITLE =
  i18n.t('interact-with-your-ai-monitor-inputs-outputs-and-memories');

/**
 * The base text for subtitle of Edit Node Dialog
 * @constant
 */
export const EDIT_DIALOG_SUBTITLE =
  i18n.t('adjust-components-settings-and-define-parameter-visibility-remember-to-save-your-changes');

/**
 * The base text for subtitle of Code Dialog
 * @constant
 */
export const CODE_PROMPT_DIALOG_SUBTITLE =
  i18n.t('edit-your-python-code-snippet-refer-to-the-langflow-documentation-for-more-information-on-how-to-write-your-own-component');

export const CODE_DICT_DIALOG_SUBTITLE =
  i18n.t('customize-your-dictionary-adding-or-editing-key-value-pairs-as-needed-supports-adding-new-objects-or-arrays');

/**
 * The base text for subtitle of Prompt Dialog
 * @constant
 */
export const PROMPT_DIALOG_SUBTITLE =
  i18n.t('create-your-prompt-prompts-can-help-guide-the-behavior-of-a-language-model-use-curly-brackets-to-introduce-variables');

export const CHAT_CANNOT_OPEN_TITLE = i18n.t('chat-cannot-open');

export const CHAT_CANNOT_OPEN_DESCRIPTION = i18n.t('this-is-not-a-chat-flow');

export const FLOW_NOT_BUILT_TITLE = i18n.t('flow-not-built');

export const FLOW_NOT_BUILT_DESCRIPTION =
  i18n.t('please-build-the-flow-before-chatting');

/**
 * The base text for subtitle of Text Dialog
 * @constant
 */
export const TEXT_DIALOG_TITLE = i18n.t('edit-text-content');

/**
 * The base text for subtitle of Import Dialog
 * @constant
 */
export const IMPORT_DIALOG_SUBTITLE =
  i18n.t('import-flows-from-a-json-file-or-choose-from-pre-existing-examples');

/**
 * The text that shows when a tooltip is empty
 * @constant
 */
export const TOOLTIP_EMPTY = i18n.t('no-compatible-components-found');

export const CSVViewErrorTitle = i18n.t('csv-output');

export const CSVNoDataError = i18n.t('no-data-available');

export const PDFViewConstant = i18n.t('expand-the-ouptut-to-see-the-pdf');

export const CSVError = i18n.t('error-loading-csv');

export const PDFLoadErrorTitle = i18n.t('error-loading-pdf');

export const PDFCheckFlow = i18n.t('please-check-your-flow-and-try-again');

export const PDFErrorTitle = i18n.t('pdf-output');

export const PDFLoadError = i18n.t('run-the-flow-to-see-the-pdf');

export const IMGViewConstant = i18n.t('expand-the-view-to-see-the-image');

export const IMGViewErrorMSG =
  i18n.t('run-the-flow-or-inform-a-valid-url-to-see-your-image');

export const IMGViewErrorTitle = i18n.t('image-output');

/**
 * The base text for subtitle of code dialog
 * @constant
 */
export const EXPORT_CODE_DIALOG =
  i18n.t('generate-the-code-to-integrate-your-flow-into-an-external-application');

/**
 * The base text for subtitle of code dialog
 * @constant
 */
export const COLUMN_DIV_STYLE =
  " w-full h-full flex overflow-auto flex-col bg-muted px-16 ";

export const NAV_DISPLAY_STYLE =
  " w-full flex justify-between py-12 pb-2 px-6 ";

/**
 * The base text for subtitle of code dialog
 * @constant
 */
export const DESCRIPTIONS: string[] = [
  i18n.t('chain-the-words-master-language'),
  i18n.t('language-architect-at-work'),
  i18n.t('empowering-language-engineering'),
  i18n.t('craft-language-connections-here'),
  i18n.t('create-connect-converse'),
  i18n.t('smart-chains-smarter-conversations'),
  i18n.t('bridging-prompts-for-brilliance'),
  i18n.t('language-models-unleashed'),
  i18n.t('your-hub-for-text-generation'),
  i18n.t('promptly-ingenious'),
  i18n.t('building-linguistic-labyrinths'),
  i18n.t('langflow-create-chain-communicate'),
  i18n.t('connect-the-dots-craft-language'),
  i18n.t('interactive-language-weaving'),
  i18n.t('generate-innovate-communicate'),
  i18n.t('conversation-catalyst-engine'),
  i18n.t('language-chainlink-master'),
  i18n.t('design-dialogues-with-langflow'),
  i18n.t('nurture-nlp-nodes-here'),
  i18n.t('conversational-cartography-unlocked'),
  i18n.t('design-develop-dialogize'),
];
export const BUTTON_DIV_STYLE =
  " flex gap-2 focus:ring-1 focus:ring-offset-1 focus:ring-ring focus:outline-none ";

/**
 * The base text for subtitle of code dialog
 * @constant
 */
export const ADJECTIVES: string[] = [
  "admiring",
  "adoring",
  "agitated",
  "amazing",
  "angry",
  "awesome",
  "backstabbing",
  "berserk",
  "big",
  "boring",
  "clever",
  "cocky",
  "compassionate",
  "condescending",
  "cranky",
  "desperate",
  "determined",
  "distracted",
  "dreamy",
  "drunk",
  "ecstatic",
  "elated",
  "elegant",
  "evil",
  "fervent",
  "focused",
  "furious",
  "gigantic",
  "gloomy",
  "goofy",
  "grave",
  "happy",
  "high",
  "hopeful",
  "hungry",
  "insane",
  "jolly",
  "jovial",
  "kickass",
  "lonely",
  "loving",
  "mad",
  "modest",
  "naughty",
  "nauseous",
  "nostalgic",
  "pedantic",
  "pensive",
  "prickly",
  "reverent",
  "romantic",
  "sad",
  "serene",
  "sharp",
  "sick",
  "silly",
  "sleepy",
  "small",
  "stoic",
  "stupefied",
  "suspicious",
  "tender",
  "thirsty",
  "tiny",
  "trusting",
  "bubbly",
  "charming",
  "cheerful",
  "comical",
  "dazzling",
  "delighted",
  "dynamic",
  "effervescent",
  "enthusiastic",
  "exuberant",
  "fluffy",
  "friendly",
  "funky",
  "giddy",
  "giggly",
  "gleeful",
  "goofy",
  "graceful",
  "grinning",
  "hilarious",
  "inquisitive",
  "joyous",
  "jubilant",
  "lively",
  "mirthful",
  "mischievous",
  "optimistic",
  "peppy",
  "perky",
  "playful",
  "quirky",
  "radiant",
  "sassy",
  "silly",
  "spirited",
  "sprightly",
  "twinkly",
  "upbeat",
  "vibrant",
  "witty",
  "zany",
  "zealous",
];
/**
 * Nouns for the name of the flow
 * @constant
 *
 */
export const NOUNS: string[] = [
  "albattani",
  "allen",
  "almeida",
  "archimedes",
  "ardinghelli",
  "aryabhata",
  "austin",
  "babbage",
  "banach",
  "bardeen",
  "bartik",
  "bassi",
  "bell",
  "bhabha",
  "bhaskara",
  "blackwell",
  "bohr",
  "booth",
  "borg",
  "bose",
  "boyd",
  "brahmagupta",
  "brattain",
  "brown",
  "carson",
  "chandrasekhar",
  "colden",
  "cori",
  "cray",
  "curie",
  "darwin",
  "davinci",
  "dijkstra",
  "dubinsky",
  "easley",
  "einstein",
  "elion",
  "engelbart",
  "euclid",
  "euler",
  "fermat",
  "fermi",
  "feynman",
  "franklin",
  "galileo",
  "gates",
  "goldberg",
  "goldstine",
  "goldwasser",
  "golick",
  "goodall",
  "hamilton",
  "hawking",
  "heisenberg",
  "heyrovsky",
  "hodgkin",
  "hoover",
  "hopper",
  "hugle",
  "hypatia",
  "jang",
  "jennings",
  "jepsen",
  "joliot",
  "jones",
  "kalam",
  "kare",
  "keller",
  "khorana",
  "kilby",
  "kirch",
  "knuth",
  "kowalevski",
  "lalande",
  "lamarr",
  "leakey",
  "leavitt",
  "lichterman",
  "liskov",
  "lovelace",
  "lumiere",
  "mahavira",
  "mayer",
  "mccarthy",
  "mcclintock",
  "mclean",
  "mcnulty",
  "meitner",
  "meninsky",
  "mestorf",
  "minsky",
  "mirzakhani",
  "morse",
  "murdock",
  "newton",
  "nobel",
  "noether",
  "northcutt",
  "noyce",
  "panini",
  "pare",
  "pasteur",
  "payne",
  "perlman",
  "pike",
  "poincare",
  "poitras",
  "ptolemy",
  "raman",
  "ramanujan",
  "ride",
  "ritchie",
  "roentgen",
  "rosalind",
  "saha",
  "sammet",
  "shaw",
  "shirley",
  "shockley",
  "sinoussi",
  "snyder",
  "spence",
  "stallman",
  "stonebraker",
  "swanson",
  "swartz",
  "swirles",
  "tesla",
  "thompson",
  "torvalds",
  "turing",
  "varahamihira",
  "visvesvaraya",
  "volhard",
  "wescoff",
  "williams",
  "wilson",
  "wing",
  "wozniak",
  "wright",
  "yalow",
  "yonath",
  "coulomb",
  "degrasse",
  "dewey",
  "edison",
  "eratosthenes",
  "faraday",
  "galton",
  "gauss",
  "herschel",
  "hubble",
  "joule",
  "kaku",
  "kepler",
  "khayyam",
  "lavoisier",
  "maxwell",
  "mendel",
  "mendeleev",
  "ohm",
  "pascal",
  "planck",
  "riemann",
  "schrodinger",
  "sagan",
  "tesla",
  "tyson",
  "volta",
  "watt",
  "weber",
  "wien",
  "zoBell",
  "zuse",
];

/**
 * Header text for user projects
 * @constant
 *
 */
export const USER_PROJECTS_HEADER = i18n.t('my-collection');

export const DEFAULT_FOLDER = i18n.t('my-projects');

/**
 * Header text for admin page
 * @constant
 *
 */
export const ADMIN_HEADER_TITLE = i18n.t('admin-page');

/**
 * Header description for admin page
 * @constant
 *
 */
export const ADMIN_HEADER_DESCRIPTION =
  i18n.t('navigate-through-this-section-to-efficiently-oversee-all-application-users-from-here-you-can-seamlessly-manage-user-accounts');

export const BASE_URL_API = custom.BASE_URL_API || "/api/v1/";

export const BASE_URL_API_V2 = custom.BASE_URL_API_V2 || "/api/v2/";

/**
 * URLs excluded from error retries.
 * @constant
 *
 */
export const URL_EXCLUDED_FROM_ERROR_RETRIES = [
  `${BASE_URL_API}validate/code`,
  `${BASE_URL_API}custom_component`,
  `${BASE_URL_API}validate/prompt`,
  `${BASE_URL_API}/login`,
  `${BASE_URL_API}api_key/store`,
];

export const skipNodeUpdate = [
  "CustomComponent",
  "PromptTemplate",
  "ChatMessagePromptTemplate",
  "SystemMessagePromptTemplate",
  "HumanMessagePromptTemplate",
];

export const CONTROL_INPUT_STATE = {
  password: "",
  cnfPassword: "",
  username: "",
};

export const CONTROL_PATCH_USER_STATE = {
  password: "",
  cnfPassword: "",
  profilePicture: "",
  apikey: "",
};

export const CONTROL_LOGIN_STATE = {
  username: "",
  password: "",
};

export const CONTROL_NEW_USER = {
  username: "",
  password: "",
  is_active: false,
  is_superuser: false,
};

export const tabsCode = [];

export const FETCH_ERROR_MESSAGE = i18n.t('couldnt-establish-a-connection');
export const FETCH_ERROR_DESCRIPION =
i18n.t('check-if-everything-is-working-properly-and-try-again');

export const TIMEOUT_ERROR_MESSAGE =
i18n.t('please-wait-a-few-moments-while-the-server-processes-your-request');
export const TIMEOUT_ERROR_DESCRIPION = i18n.t('server-is-busy');

export const SIGN_UP_SUCCESS = i18n.t('account-created-await-admin-activation');

export const API_PAGE_PARAGRAPH =
  i18n.t('your-secret-langflow-api-keys-are-listed-below-do-not-share-your-api-key-with-others-or-expose-it-in-the-browser-or-other-client-side-code');

export const API_PAGE_USER_KEYS =
  i18n.t('this-user-does-not-have-any-keys-assigned-at-the-moment');

export const LAST_USED_SPAN_1 = i18n.t('the-last-time-this-key-was-used');

export const LAST_USED_SPAN_2 =
  i18n.t('accurate-to-within-the-hour-from-the-most-recent-usage');

export const LANGFLOW_SUPPORTED_TYPES = new Set([
  "str",
  "bool",
  "float",
  "code",
  "prompt",
  "file",
  "int",
  "dict",
  "NestedDict",
  "table",
  "link",
  "slider",
  "tab",
]);

export const FLEX_VIEW_TYPES = ["bool"];

export const priorityFields = new Set(["code", "template"]);

export const INPUT_TYPES = new Set([
  "ChatInput",
  "ChatOCRInput",
  // "TextInput",
  // "KeyPairInput",
  // "JsonInput",
  // "StringListInput",
]);
export const OUTPUT_TYPES = new Set([
  "ChatOutput",
  // "TextOutput",
  // "PDFOutput",
  // "ImageOutput",
  // "CSVOutput",
  // "JsonOutput",
  // "KeyPairOutput",
  // "StringListOutput",
  // "DataOutput",
  // "TableOutput",
]);

export const CHAT_FIRST_INITIAL_TEXT =
  i18n.t('start-a-conversation-and-click-the-agents-memories');

export const TOOLTIP_OUTDATED_NODE =
  i18n.t('your-component-is-outdated-click-to-update-data-may-be-lost')

export const CHAT_SECOND_INITIAL_TEXT = i18n.t('to-inspect-previous-messages');

export const TOOLTIP_OPEN_HIDDEN_OUTPUTS = i18n.t('expand-hidden-outputs');
export const TOOLTIP_HIDDEN_OUTPUTS = i18n.t('collapse-hidden-outputs');

export const ZERO_NOTIFICATIONS = i18n.t('no-new-notifications');

export const SUCCESS_BUILD = i18n.t('built-sucessfully');

export const ALERT_SAVE_WITH_API =
  i18n.t('caution-unchecking-this-box-only-removes-api-keys-from-fields-specifically-designated-for-api-keys');

export const SAVE_WITH_API_CHECKBOX = i18n.t('save-with-my-api-keys');
export const EDIT_TEXT_MODAL_TITLE = i18n.t('edit-text');
export const EDIT_TEXT_PLACEHOLDER = i18n.t('type-message-here');
export const INPUT_HANDLER_HOVER = i18n.t('avaliable-input-components');
export const OUTPUT_HANDLER_HOVER = i18n.t('avaliable-output-components');
export const TEXT_INPUT_MODAL_TITLE = i18n.t('inputs');
export const OUTPUTS_MODAL_TITLE = i18n.t('outputs');
export const LANGFLOW_CHAT_TITLE = i18n.t('langflow-chat');
export const CHAT_INPUT_PLACEHOLDER =
  i18n.t('no-chat-input-variables-found-click-to-run-your-flow');
export const CHAT_INPUT_PLACEHOLDER_SEND = i18n.t('send-a-message');
export const EDIT_CODE_TITLE = i18n.t('edit-code');
export const MY_COLLECTION_DESC =
  i18n.t('manage-your-projects-download-and-upload-entire-collections');
export const STORE_DESC = i18n.t('explore-community-shared-flows-and-components');
export const STORE_TITLE = i18n.t('langflow-store');
export const NO_API_KEY = i18n.t('you-dont-have-an-api-key');
export const INSERT_API_KEY = i18n.t('insert-your-langflow-api-key');
export const INVALID_API_KEY = i18n.t('your-api-key-is-not-valid');
export const CREATE_API_KEY = i18n.t('dont-have-an-api-key-sign-up-at');
export const STATUS_BUILD = i18n.t('build-to-validate-status');
export const STATUS_INACTIVE = i18n.t('execution-blocked');
export const STATUS_BUILDING = i18n.t('building');
export const SAVED_HOVER = i18n.t('last-saved');
export const RUN_TIMESTAMP_PREFIX = i18n.t('last-run');
export const STARTER_FOLDER_NAME = i18n.t('starter-projects');
export const PRIORITY_SIDEBAR_ORDER = [
  "saved_components",
  "inputs",
  "outputs",
  "prompts",
  "data",
  "prompt",
  "models",
  "helpers",
  "vectorstores",
  "embeddings",
];

export const BUNDLES_SIDEBAR_FOLDER_NAMES = [
  "notion",
  "Notion",
  "AssemblyAI",
  "assemblyai",
  "LangWatch",
  "langwatch",
  "Youtube",
  "youtube",
];

export const AUTHORIZED_DUPLICATE_REQUESTS = [
  "/health",
  "/flows",
  "/logout",
  "/refresh",
  "/login",
  "/auto_login",
];

export const BROKEN_EDGES_WARNING =
  i18n.t('some-connections-were-removed-because-they-were-invalid');

export const SAVE_DEBOUNCE_TIME = 300;

export const IS_MAC = navigator.userAgent.toUpperCase().includes("MAC");

export const defaultShortcuts = [
  {
    display_name: i18n.t('controls'),
    name: "Advanced Settings",
    shortcut: "mod+shift+a",
  },
  {
    display_name: i18n.t('search-components-on-sidebar'),
    name: "Search Components Sidebar",
    shortcut: "/",
  },
  {
    display_name: i18n.t('minimize'),
    name: "Minimize",
    shortcut: "mod+.",
  },
  {
    display_name: i18n.t('code'),
    name: "Code",
    shortcut: "space",
  },
  {
    display_name: i18n.t('copy'),
    name: "Copy",
    shortcut: "mod+c",
  },
  {
    display_name: i18n.t('duplicate'),
    name: "Duplicate",
    shortcut: "mod+d",
  },
  {
    display_name: i18n.t('component-share'),
    name: "Component Share",
    shortcut: "mod+shift+s",
  },
  {
    display_name: i18n.t('docs'),
    name: "Docs",
    shortcut: "mod+shift+d",
  },
  {
    display_name: i18n.t('changes-save'),
    name: "Changes Save",
    shortcut: "mod+s",
  },
  {
    display_name: i18n.t('save-component'),
    name: "Save Component",
    shortcut: "mod+alt+s",
  },
  {
    display_name: i18n.t('delete'),
    name: "Delete",
    shortcut: "backspace",
  },
  {
    display_name: i18n.t('open-playground'),
    name: "Open Playground",
    shortcut: "mod+k",
  },
  {
    display_name: i18n.t('undo'),
    name: "Undo",
    shortcut: "mod+z",
  },
  {
    display_name: i18n.t('redo'),
    name: "Redo",
    shortcut: "mod+y",
  },
  {
    display_name: i18n.t('redo-alternative'),
    name: "Redo Alt",
    shortcut: "mod+shift+z",
  },
  {
    display_name: i18n.t('group'),
    name: "Group",
    shortcut: "mod+g",
  },
  {
    display_name: i18n.t('cut'),
    name: "Cut",
    shortcut: "mod+x",
  },
  {
    display_name: i18n.t('paste'),
    name: "Paste",
    shortcut: "mod+v",
  },
  {
    display_name: "API",
    name: "API",
    shortcut: "r",
  },
  {
    display_name: i18n.t('download'),
    name: "Download",
    shortcut: "mod+j",
  },
  {
    display_name: i18n.t('update'),
    name: "Update",
    shortcut: "mod+u",
  },
  {
    display_name: i18n.t('freeze'),
    name: "Freeze Path",
    shortcut: "mod+shift+f",
  },
  {
    display_name: i18n.t('flow-share'),
    name: "Flow Share",
    shortcut: "mod+shift+b",
  },
  {
    display_name: i18n.t('play'),
    name: "Play",
    shortcut: "p",
  },
  {
    display_name: i18n.t('output-inspection'),
    name: "Output Inspection",
    shortcut: "o",
  },
  {
    display_name: i18n.t('tool-mode'),
    name: "Tool Mode",
    shortcut: "mod+shift+m",
  },
  {
    display_name: i18n.t('toggle-sidebar'),
    name: "Toggle Sidebar",
    shortcut: "mod+b",
  },
];

export const DEFAULT_TABLE_ALERT_MSG = i18n.t('oops-it-seems-theres-no-data-to-display-right-now-please-check-back-later');

export const DEFAULT_TABLE_ALERT_TITLE = i18n.t('no-data-available-0');

export const NO_COLUMN_DEFINITION_ALERT_TITLE = i18n.t('no-column-definitions');

export const NO_COLUMN_DEFINITION_ALERT_DESCRIPTION =
  i18n.t('there-are-no-column-definitions-available-for-this-table');

export const LOCATIONS_TO_RETURN = ["/flow/", "/settings/"];

export const MAX_BATCH_SIZE = 50;

export const MODAL_CLASSES =
  "nopan nodelete nodrag  noflow fixed inset-0 bottom-0 left-0 right-0 top-0 z-50 overflow-auto bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0";

export const ALLOWED_IMAGE_INPUT_EXTENSIONS = ["png", "jpg", "jpeg"];

export const componentsToIgnoreUpdate = ["CustomComponent"];

export const FS_ERROR_TEXT =
  i18n.t('please-ensure-your-file-has-one-of-the-following-extensions');
export const SN_ERROR_TEXT = ALLOWED_IMAGE_INPUT_EXTENSIONS.join(", ");

export const ERROR_UPDATING_COMPONENT =
  i18n.t('an-unexpected-error-occurred-while-updating-the-component-please-try-again');
export const TITLE_ERROR_UPDATING_COMPONENT =
  i18n.t('error-while-updating-the-component');

export const EMPTY_INPUT_SEND_MESSAGE = i18n.t('no-input-message-provided');

export const EMPTY_OUTPUT_SEND_MESSAGE = i18n.t('message-empty');

export const TABS_ORDER = [
  "curl",
  "python api",
  "js api",
  "python code",
  "chat widget html",
];

export const LANGFLOW_ACCESS_TOKEN = "access_token_lf";
export const LANGFLOW_API_TOKEN = "apikey_tkn_lflw";
export const LANGFLOW_AUTO_LOGIN_OPTION = "auto_login_lf";
export const LANGFLOW_REFRESH_TOKEN = "refresh_token_lf";
export const AGENT_AUTH_TOKEN = "agent_auth_token";

export const LANGFLOW_ACCESS_TOKEN_EXPIRE_SECONDS = 60 * 60 - 60 * 60 * 0.1;
export const LANGFLOW_ACCESS_TOKEN_EXPIRE_SECONDS_ENV =
  Number(process.env?.ACCESS_TOKEN_EXPIRE_SECONDS ?? 60) -
  Number(process.env?.ACCESS_TOKEN_EXPIRE_SECONDS ?? 60) * 0.1;
export const TEXT_FIELD_TYPES: string[] = ["str", "SecretStr"];
export const NODE_WIDTH = 384;
export const NODE_HEIGHT = NODE_WIDTH * 3;

export const SHORTCUT_KEYS = ["cmd", "ctrl", "mod", "alt", "shift"];

export const SERVER_HEALTH_INTERVAL = 10000;
export const REFETCH_SERVER_HEALTH_INTERVAL = 20000;
export const DRAG_EVENTS_CUSTOM_TYPESS = {
  genericnode: "genericNode",
  notenode: "noteNode",
};

export const NOTE_NODE_MIN_WIDTH = 324;
export const NOTE_NODE_MIN_HEIGHT = 324;
export const NOTE_NODE_MAX_HEIGHT = 800;
export const NOTE_NODE_MAX_WIDTH = 600;

export const COLOR_OPTIONS = {
  amber: "hsl(var(--note-amber))",
  neutral: "hsl(var(--note-neutral))",
  rose: "hsl(var(--note-rose))",
  blue: "hsl(var(--note-blue))",
  lime: "hsl(var(--note-lime))",
  transparent: null,
};

export const maxSizeFilesInBytes = 10 * 1024 * 1024; // 10MB in bytes
export const MAX_TEXT_LENGTH = 99999;

export const SEARCH_TABS = [i18n.t('all'), i18n.t('flows'), i18n.t('components')];
export const PAGINATION_SIZE = 12;
export const PAGINATION_PAGE = 1;

export const STORE_PAGINATION_SIZE = 12;
export const STORE_PAGINATION_PAGE = 1;

export const PAGINATION_ROWS_COUNT = [12, 24, 48, 96];
export const STORE_PAGINATION_ROWS_COUNT = [12, 24, 48, 96];

export const GRADIENT_CLASS =
  "linear-gradient(to right, hsl(var(--background) / 0.3), hsl(var(--background)))";

export const GRADIENT_CLASS_DISABLED =
  "linear-gradient(to right, hsl(var(--muted) / 0.3), hsl(var(--muted)))";

export const RECEIVING_INPUT_VALUE = i18n.t('receiving-input');

export const ICON_STROKE_WIDTH = 1.5;

export const DEFAULT_PLACEHOLDER = i18n.t('type-something');

export const DEFAULT_TOOLSET_PLACEHOLDER = i18n.t('used-as-a-tool');

export const SAVE_API_KEY_ALERT = i18n.t('api-key-saved-successfully');
export const PLAYGROUND_BUTTON_NAME = i18n.t('playground');
export const POLLING_MESSAGES = {
  ENDPOINT_NOT_AVAILABLE: i18n.t('endpoint-not-available'),
  STREAMING_NOT_SUPPORTED: i18n.t('streaming-not-supported'),
} as const;

export const POLLING_INTERVAL = 100;

export const IS_AUTO_LOGIN =
  !process?.env?.LANGFLOW_AUTO_LOGIN ||
  String(process?.env?.LANGFLOW_AUTO_LOGIN)?.toLowerCase() !== "false";

export const AUTO_LOGIN_RETRY_DELAY = 2000;
export const AUTO_LOGIN_MAX_RETRY_DELAY = 60000;

export const ALL_LANGUAGES = [
  { value: "en-US", name: "English (US)" },
  { value: "en-GB", name: "English (UK)" },
  { value: "it-IT", name: "Italian" },
  { value: "fr-FR", name: "French" },
  { value: "es-ES", name: "Spanish" },
  { value: "de-DE", name: "German" },
  { value: "ja-JP", name: "Japanese" },
  { value: "pt-BR", name: "Portuguese (Brazil)" },
  { value: "zh-CN", name: "Chinese (Simplified)" },
  { value: "ru-RU", name: "Russian" },
  { value: "ar-SA", name: "Arabic" },
  { value: "hi-IN", name: "Hindi" },
];
