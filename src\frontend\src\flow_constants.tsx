import i18n from "@/i18n";
/**
 * Default description for the flow
 * @constant
 */
export const DESCRIPTIONS: string[] = [
  i18n.t('chain-the-words-master-language'),
  i18n.t('language-architect-at-work'),
  i18n.t('empowering-language-engineering'),
  i18n.t('craft-language-connections-here'),
  i18n.t('create-connect-converse'),
  i18n.t('smart-chains-smarter-conversations'),
  i18n.t('bridging-prompts-for-brilliance'),
  i18n.t('language-models-unleashed'),
  i18n.t('your-hub-for-text-generation'),
  i18n.t('promptly-ingenious'),
  i18n.t('building-linguistic-labyrinths'),
  i18n.t('create-chain-communicate'),
  i18n.t('connect-the-dots-craft-language'),
  i18n.t('interactive-language-weaving'),
  i18n.t('generate-innovate-communicate'),
  i18n.t('conversation-catalyst-engine'),
  i18n.t('language-chainlink-master'),
  i18n.t('design-dialogues-with-langflow'),
  i18n.t('nurture-nlp-nodes-here'),
  i18n.t('conversational-cartography-unlocked'),
  i18n.t('design-develop-dialogize'),
  i18n.t('unleashing-linguistic-creativity'),
  i18n.t('graph-your-way-to-great-conversations'),
  i18n.t('the-power-of-language-at-your-fingertips'),
  i18n.t('sculpting-language-with-precision'),
  i18n.t('where-language-meets-logic'),
  i18n.t('building-intelligent-interactions'),
  i18n.t('your-passport-to-linguistic-landscapes'),
  i18n.t('create-curate-communicate-with-langflow'),
  i18n.t('flow-into-the-future-of-language'),
  i18n.t('mapping-meaningful-conversations'),
  i18n.t('unravel-the-art-of-articulation'),
  i18n.t('language-engineering-excellence'),
  i18n.t('navigate-the-networks-of-conversation'),
  i18n.t('crafting-conversations-one-node-at-a-time'),
  i18n.t('the-pinnacle-of-prompt-generation'),
  i18n.t('language-models-mapped-and-mastered'),
  i18n.t('powerful-prompts-perfectly-positioned'),
  i18n.t('innovation-in-interaction-with-langflow'),
  i18n.t('your-toolkit-for-text-generation'),
  i18n.t('unfolding-linguistic-possibilities'),
  i18n.t('building-powerful-solutions-with-language-models'),
  i18n.t('uncover-business-opportunities-with-nlp'),
  i18n.t('harness-the-power-of-conversational-ai'),
  i18n.t('transform-your-business-with-smart-dialogues'),
  i18n.t('craft-meaningful-interactions-generate-value'),
  i18n.t('unleashing-business-potential-through-language-engineering'),
  i18n.t('empowering-enterprises-with-intelligent-interactions'),
  i18n.t('driving-innovation-in-business-communication'),
  i18n.t('catalyzing-business-growth-through-conversational-ai'),
  i18n.t('text-generation-meets-business-transformation'),
  i18n.t('navigate-the-linguistic-landscape-discover-opportunities'),
  i18n.t('create-powerful-connections-boost-business-value'),
  i18n.t('empowering-communication-enabling-opportunities'),
  i18n.t('advanced-nlp-for-groundbreaking-business-solutions'),
  i18n.t('innovation-in-interaction-revolution-in-revenue'),
  i18n.t('maximize-impact-with-intelligent-conversations'),
  i18n.t('beyond-text-generation-unleashing-business-opportunities'),
  i18n.t('unlock-the-power-of-ai-in-your-business-conversations'),
  i18n.t('crafting-dialogues-that-drive-business-success'),
  i18n.t('engineered-for-excellence-built-for-business'),
];

/**
 * Adjectives for the name of the flow
 * @constant
 *
 */
export const ADJECTIVES: string[] = [
  "admiring",
  "adoring",
  "agitated",
  "amazing",
  "angry",
  "awesome",
  "backstabbing",
  "berserk",
  "big",
  "boring",
  "clever",
  "cocky",
  "compassionate",
  "condescending",
  "cranky",
  "desperate",
  "determined",
  "distracted",
  "dreamy",
  "drunk",
  "ecstatic",
  "elated",
  "elegant",
  "evil",
  "fervent",
  "focused",
  "furious",
  "gigantic",
  "gloomy",
  "goofy",
  "grave",
  "happy",
  "high",
  "hopeful",
  "hungry",
  "insane",
  "jolly",
  "jovial",
  "kickass",
  "lonely",
  "loving",
  "mad",
  "modest",
  "naughty",
  "nauseous",
  "nostalgic",
  "pedantic",
  "pensive",
  "prickly",
  "reverent",
  "romantic",
  "sad",
  "serene",
  "sharp",
  "sick",
  "silly",
  "sleepy",
  "small",
  "stoic",
  "stupefied",
  "suspicious",
  "tender",
  "thirsty",
  "tiny",
  "trusting",
  "bubbly",
  "charming",
  "cheerful",
  "comical",
  "dazzling",
  "delighted",
  "dynamic",
  "effervescent",
  "enthusiastic",
  "exuberant",
  "fluffy",
  "friendly",
  "funky",
  "giddy",
  "giggly",
  "gleeful",
  "goofy",
  "graceful",
  "grinning",
  "hilarious",
  "inquisitive",
  "joyous",
  "jubilant",
  "lively",
  "mirthful",
  "mischievous",
  "optimistic",
  "peppy",
  "perky",
  "playful",
  "quirky",
  "radiant",
  "sassy",
  "silly",
  "spirited",
  "sprightly",
  "twinkly",
  "upbeat",
  "vibrant",
  "witty",
  "zany",
  "zealous",
];
/**
 * Nouns for the name of the flow
 * @constant
 *
 */
export const NOUNS: string[] = [
  "albattani",
  "allen",
  "almeida",
  "archimedes",
  "ardinghelli",
  "aryabhata",
  "austin",
  "babbage",
  "banach",
  "bardeen",
  "bartik",
  "bassi",
  "bell",
  "bhabha",
  "bhaskara",
  "blackwell",
  "bohr",
  "booth",
  "borg",
  "bose",
  "boyd",
  "brahmagupta",
  "brattain",
  "brown",
  "carson",
  "chandrasekhar",
  "colden",
  "cori",
  "cray",
  "curie",
  "darwin",
  "davinci",
  "dijkstra",
  "dubinsky",
  "easley",
  "einstein",
  "elion",
  "engelbart",
  "euclid",
  "euler",
  "fermat",
  "fermi",
  "feynman",
  "franklin",
  "galileo",
  "gates",
  "goldberg",
  "goldstine",
  "goldwasser",
  "golick",
  "goodall",
  "hamilton",
  "hawking",
  "heisenberg",
  "heyrovsky",
  "hodgkin",
  "hoover",
  "hopper",
  "hugle",
  "hypatia",
  "jang",
  "jennings",
  "jepsen",
  "joliot",
  "jones",
  "kalam",
  "kare",
  "keller",
  "khorana",
  "kilby",
  "kirch",
  "knuth",
  "kowalevski",
  "lalande",
  "lamarr",
  "leakey",
  "leavitt",
  "lichterman",
  "liskov",
  "lovelace",
  "lumiere",
  "mahavira",
  "mayer",
  "mccarthy",
  "mcclintock",
  "mclean",
  "mcnulty",
  "meitner",
  "meninsky",
  "mestorf",
  "minsky",
  "mirzakhani",
  "morse",
  "murdock",
  "newton",
  "nobel",
  "noether",
  "northcutt",
  "noyce",
  "panini",
  "pare",
  "pasteur",
  "payne",
  "perlman",
  "pike",
  "poincare",
  "poitras",
  "ptolemy",
  "raman",
  "ramanujan",
  "ride",
  "ritchie",
  "roentgen",
  "rosalind",
  "saha",
  "sammet",
  "shaw",
  "shirley",
  "shockley",
  "sinoussi",
  "snyder",
  "spence",
  "stallman",
  "stonebraker",
  "swanson",
  "swartz",
  "swirles",
  "tesla",
  "thompson",
  "torvalds",
  "turing",
  "varahamihira",
  "visvesvaraya",
  "volhard",
  "wescoff",
  "williams",
  "wilson",
  "wing",
  "wozniak",
  "wright",
  "yalow",
  "yonath",
  "coulomb",
  "degrasse",
  "dewey",
  "edison",
  "eratosthenes",
  "faraday",
  "galton",
  "gauss",
  "herschel",
  "hubble",
  "joule",
  "kaku",
  "kepler",
  "khayyam",
  "lavoisier",
  "maxwell",
  "mendel",
  "mendeleev",
  "ohm",
  "pascal",
  "planck",
  "riemann",
  "schrodinger",
  "sagan",
  "tesla",
  "tyson",
  "volta",
  "watt",
  "weber",
  "wien",
  "zoBell",
  "zuse",
  "carroll",
];
