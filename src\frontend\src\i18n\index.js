import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'  // react-i18next 的初始化插件
import Backend from 'i18next-http-backend'  // 用于动态加载翻译文件
import LanguageDetector from 'i18next-browser-languagedetector'  // 用于检测浏览器语言

// 加载翻译资源（示例用本地文件）
import locales_zh from "./locales/zh/translation.json";
import locales_en from "./locales/en/translation.json";

const resources = {
    en: {
        translation: locales_en
    },
    zh: {
        translation: locales_zh
    }
};
// i18n 初始化配置
i18n
    // 使用 i18next-http-backend 插件
    // 这个插件允许我们从服务器动态加载翻译文件
    //.use(Backend)

    // 使用 i18next-browser-languagedetector 插件
    // 这个插件会自动检测用户的语言环境
    .use(LanguageDetector)

    // 将 i18next 集成到 react 中
    .use(initReactI18next)

    // 初始化 i18next
    .init({
        // 翻译文件的路径配置
        resources,
        // 默认语言
        fallbackLng: 'en',

        // 是否在开发环境打印调试信息
        debug: true,//process.env.NODE_ENV === 'development',



        // 翻译文件的命名空间
        ns: ['translation'],
        defaultNS: 'translation',

        // 插值配置
        interpolation: {
            // 是否转义 HTML 标签
            escapeValue: false,
        },

        // 检测语言的配置
        detection: {
            // 存储语言选择的 key
            lookupLocalStorage: 'i18nextLng',
            // 缓存用户语言选择
            caches: ['localStorage'],
        },
    })

export default i18n

