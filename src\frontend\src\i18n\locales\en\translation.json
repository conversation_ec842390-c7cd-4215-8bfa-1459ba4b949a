{"Get started": "Get started", "New Flow": "New Flow", "Submit": "Submit", "Error": "Error", "empty-folder": "EmptyFolder", "begin-with-a-template-or-start-from-scratch": "Begin with a template, or start from scratch.", "empty-folder-0": "Empty folder", "start-building": "Start building", "new-flow": "New Flow", "plus": "Plus", "folder-deleted-successfully": "Folder deleted successfully.", "error-deleting-folder": "Error deleting folder.", "notifications": "Notifications", "version": "Version", "settings": "Settings", "admin-page": "Admin Page", "export-flow-as-json-file": "Export flow as JSON file.", "no-new-notifications": "No new notifications", "couldnt-establish-a-connection": "Couldn't establish a connection.", "check-if-everything-is-working-properly-and-try-again": "Check if everything is working properly and try again.", "please-wait-a-few-moments-while-the-server-processes-your-request": "Please wait a few moments while the server processes your request.", "server-is-busy": "Server is busy.", "account-created-await-admin-activation": "Account created! Await admin activation.", "your-secret-langflow-api-keys-are-listed-below-do-not-share-your-api-key-with-others-or-expose-it-in-the-browser-or-other-client-side-code": "Your secret Langflow API keys are listed below. Do not share your API key with others, or expose it in the browser or other client-side code.", "this-user-does-not-have-any-keys-assigned-at-the-moment": "This user does not have any keys assigned at the moment.", "the-last-time-this-key-was-used": "The last time this key was used.", "accurate-to-within-the-hour-from-the-most-recent-usage": "Accurate to within the hour from the most recent usage.", "start-a-conversation-and-click-the-agents-memories": "Start a conversation and click the agent's memories", "your-component-is-outdated-click-to-update-data-may-be-lost": "Your component is outdated. Click to update (data may be lost)", "to-inspect-previous-messages": "to inspect previous messages.", "collapse-hidden-outputs": "Collapse hidden outputs", "expand-hidden-outputs": "Expand hidden outputs", "built-sucessfully": "Built sucessfully ✨", "caution-unchecking-this-box-only-removes-api-keys-from-fields-specifically-designated-for-api-keys": "Caution: Unchecking this box only removes API keys from fields specifically designated for API keys.", "save-with-my-api-keys": "Save with my API keys", "type-message-here": "Type message here.", "edit-text": "Edit Text", "customize-your-flow-details-and-settings": "Customize your flow details and settings.", "explore-detailed-logs-of-events-and-transactions-between-components": "Explore detailed logs of events and transactions between components.", "export-your-flow-to-integrate-it-using-this-code": "Export your flow to integrate it using this code.", "interact-with-your-ai-monitor-inputs-outputs-and-memories": "Interact with your AI. Monitor inputs, outputs and memories.", "adjust-components-settings-and-define-parameter-visibility-remember-to-save-your-changes": "Adjust component's settings and define parameter visibility. Remember to save your changes.", "edit-your-python-code-snippet-refer-to-the-langflow-documentation-for-more-information-on-how-to-write-your-own-component": "Edit your Python code snippet. Refer to the Langflow documentation for more information on how to write your own component.", "customize-your-dictionary-adding-or-editing-key-value-pairs-as-needed-supports-adding-new-objects-or-arrays": "Customize your dictionary, adding or editing key-value pairs as needed. Supports adding new objects {} or arrays [].", "create-your-prompt-prompts-can-help-guide-the-behavior-of-a-language-model-use-curly-brackets-to-introduce-variables": "Create your prompt. Prompts can help guide the behavior of a Language Model. Use curly brackets {} to introduce variables.", "chat-cannot-open": "<PERSON><PERSON>", "this-is-not-a-chat-flow": "This is not a chat flow.", "flow-not-built": "Flow not built", "please-build-the-flow-before-chatting": "Please build the flow before chatting.", "edit-text-content": "Edit text content", "import-flows-from-a-json-file-or-choose-from-pre-existing-examples": "Import flows from a JSON file or choose from pre-existing examples.", "no-compatible-components-found": "No compatible components found.", "csv-output": "CSV output", "no-data-available": "No data available", "expand-the-ouptut-to-see-the-pdf": "Expand the ouptut to see the PDF", "error-loading-csv": "Error loading CSV", "error-loading-pdf": "Error loading PDF", "please-check-your-flow-and-try-again": "Please check your flow and try again", "pdf-output": "PDF Output", "run-the-flow-to-see-the-pdf": "Run the flow to see the pdf", "expand-the-view-to-see-the-image": "Expand the view to see the image", "run-the-flow-or-inform-a-valid-url-to-see-your-image": "Run the flow or inform a valid url to see your image", "image-output": "Image output", "generate-the-code-to-integrate-your-flow-into-an-external-application": "Generate the code to integrate your flow into an external application.", "chain-the-words-master-language": "Chain the Words, Master Language!", "language-architect-at-work": "Language Architect at Work!", "empowering-language-engineering": "Empowering Language Engineering.", "craft-language-connections-here": "Craft Language Connections Here.", "create-connect-converse": "Create, Connect, Converse.", "smart-chains-smarter-conversations": "Smart Chains, Smarter Conversations.", "bridging-prompts-for-brilliance": "Bridging Prompts for Brilliance.", "language-models-unleashed": "Language Models, Unleashed.", "promptly-ingenious": "Promptly Ingenious!", "your-hub-for-text-generation": "Your Hub for Text Generation.", "building-linguistic-labyrinths": "Building Linguistic Labyrinths.", "langflow-create-chain-communicate": "Langflow: Create, Chain, Communicate.", "connect-the-dots-craft-language": "Connect the Dots, Craft Language.", "interactive-language-weaving": "Interactive Language Weaving.", "generate-innovate-communicate": "Generate, Innovate, Communicate.", "conversation-catalyst-engine": "Conversation Catalyst Engine.", "language-chainlink-master": "Language Chainlink Master.", "design-dialogues-with-langflow": "Design Dialogues with Lang<PERSON>.", "nurture-nlp-nodes-here": "Nurture NLP Nodes Here.", "conversational-cartography-unlocked": "Conversational Cartography Unlocked.", "design-develop-dialogize": "Design, Develop, Dialogize.", "my-collection": "My Collection", "my-projects": "My Projects", "navigate-through-this-section-to-efficiently-oversee-all-application-users-from-here-you-can-seamlessly-manage-user-accounts": "Navigate through this section to efficiently oversee all application users. From here, you can seamlessly manage user accounts.", "avaliable-input-components": "Avaliable input components:", "avaliable-output-components": "Avaliable output components:", "inputs": "Inputs", "outputs": "Outputs", "langflow-chat": "Langflow Chat", "send-a-message": "Send a message...", "edit-code": "Edit Code", "no-chat-input-variables-found-click-to-run-your-flow": "No chat input variables found. Click to run your flow.", "langflow-store": "Langflow Store", "manage-your-projects-download-and-upload-entire-collections": "Manage your projects. Download and upload entire collections.", "explore-community-shared-flows-and-components": "Explore community-shared flows and components.", "you-dont-have-an-api-key": "You don't have an API key.", "insert-your-langflow-api-key": "Insert your Langflow API key.", "your-api-key-is-not-valid": "Your API key is not valid.", "dont-have-an-api-key-sign-up-at": "Don't have an API key? Sign up at", "build-to-validate-status": "Build to validate status.", "execution-blocked": "Execution blocked", "last-saved": "Last saved:", "last-run": "Last Run:", "starter-projects": "Starter Projects", "some-connections-were-removed-because-they-were-invalid": "Some connections were removed because they were invalid:", "oops-it-seems-theres-no-data-to-display-right-now-please-check-back-later": "Oops! It seems there's no data to display right now. Please check back later.", "no-data-available-0": "No Data Available", "no-column-definitions": "No Column Definitions", "there-are-no-column-definitions-available-for-this-table": "There are no column definitions available for this table.", "please-ensure-your-file-has-one-of-the-following-extensions": "Please ensure your file has one of the following extensions:", "an-unexpected-error-occurred-while-updating-the-component-please-try-again": "An unexpected error occurred while updating the Component. Please try again.", "error-while-updating-the-component": "Error while updating the Component", "no-input-message-provided": "No input message provided.", "message-empty": "Message empty.", "all": "All", "flows": "Flows", "components": "Components", "receiving-input": "Receiving input", "type-something": "Type something...", "used-as-a-tool": "Used as a tool", "api-key-saved-successfully": "API key saved successfully", "playground": "Playground", "streaming-not-supported": "Streaming not supported", "endpoint-not-available": "Endpoint not available", "building": "Building...", "controls": "Controls", "search-components-on-sidebar": "Search Components on Sidebar", "minimize": "Minimize", "code": "Code", "copy": "Copy", "duplicate": "Duplicate", "component-share": "Component Share", "docs": "Docs", "changes-save": "Changes Save", "save-component": "Save Component", "delete": "Delete", "open-playground": "Open Playground", "undo": "Undo", "redo": "Redo", "redo-alternative": "Redo (alternative)", "group": "Group", "cut": "Cut", "paste": "Paste", "download": "Download", "update": "Update", "freeze": "Freeze", "flow-share": "Flow Share", "play": "Play", "output-inspection": "Output Inspection", "tool-mode": "Tool Mode", "toggle-sidebar": "Toggle Sidebar", "uploaded-successfully": "Uploaded successfully", "folder-uploaded-successfully": "Folder uploaded successfully.", "folder-exported": "Folder Exported", "new-folder": "New Folder", "create-new-folder": "Create New Folder", "my-files": "My Files", "create-new-folder-0": "Create new folder", "drop-your-isemptyfolder-flows-or-components-flowtype-here": "Drop your {{flowType}} here", "flows-or-components": "flows or components", "no-flows-in-this-folder": "No flows in this folder.", "create-a-new-flow": "Create a new flow", "or-browse-the-store": ", or browse the store.", "no-saved-or-custom-components-learn-more-about": "No saved or custom components. Learn more about", "creating-custom-components": "creating custom components", "search-flowtype": "Search {{flowType}}...", "error-on-uploading-your-folder-try-dragging-it-into-an-existing-folder": "Error on uploading your folder, try dragging it into an existing folder.", "langflow-0": "Langflow", "you-need-to-enable-javascript-to-run-this-app": "You need to enable JavaScript to run this app.", "created-by-me": "Created By Me", "liked-by-me": "Liked By Me", "api-key": "API Key", "folders": "Folders", "loading": "Loading...", "general": "General", "global-variables": "Global Variables", "shortcuts": "Shortcuts", "messages": "Messages", "langflow-api-keys": "Langflow API Keys", "manage-the-general-settings-for-langflow": "Manage the general settings for Langflow.", "manage-settings-related-to-langflow-and-your-account": "Manage settings related to Langflow and your account.", "profile-picture": "Profile Picture", "choose-the-image-that-appears-as-your-profile-picture": "Choose the image that appears as your profile picture.", "save": "Save", "oops-looks-like-you-missed-something": "Oops! Looks like you missed something", "the-flow-has-an-incomplete-loop-check-your-connections-and-try-again": "The flow has an incomplete loop. Check your connections and try again.", "please-select-a-valid-file-only-these-file-types-are-allowed": "Please select a valid file. Only these file types are allowed:", "error-occurred-while-uploading-file": "Error occurred while uploading file", "file-uploaded-successfully": "File uploaded successfully", "oops-looks-like-you-missed-some-required-information": "Oops! Looks like you missed some required information:", "there-is-an-error-in-your-function": "There is an error in your function", "there-is-an-error-in-your-imports": "There is an error in your imports", "something-went-wrong-please-try-again": "Something went wrong, please try again", "there-is-something-wrong-with-this-code-please-review-it": "There is something wrong with this code, please review it", "please-build-the-flow-again-before-using-the-chat": "Please build the flow again before using the chat.", "there-was-an-error-sending-the-message": "There was an error sending the message", "there-is-something-wrong-with-this-prompt-please-review-it": "There is something wrong with this prompt, please review it", "there-was-an-error-saving-the-api-key-please-try-again": "There was an error saving the API Key, please try again.", "error-on-delete-user": "Error on delete user", "error-on-edit-user": "Error on edit user", "error-signing-in": "Error signing in", "error-when-adding-new-user": "Error when adding new user", "error-on-delete-key": "Error on delete key", "error-on-delete-keys": "Error on delete keys", "error-uploading-file": "Error uploading file", "invalid-file-type": "Invalid file type", "please-upload-a-json-file": "Please upload a JSON file", "invalid-selection": "Invalid selection", "error-changing-password": "Error changing password", "passwords-do-not-match": "Passwords do not match", "error-saving-changes": "Error saving changes", "error-retrieving-profile-pictures": "Error retrieving profile pictures", "error-signing-up": "Error signing up", "api-key-error": "API Key Error", "you-dont-have-an-api-key-please-add-one-to-use-the-langflow-store": "You don't have an API Key. Please add one to use the Langflow Store.", "your-api-key-is-not-valid-please-add-a-valid-api-key-to-use-the-langflow-store": "Your API Key is not valid. Please add a valid API Key to use the Langflow Store.", "error-getting-components": "Error getting components.", "there-is-no-chatoutput-component-in-the-flow": "There is no ChatOutput Component in the flow.", "warning-critical-data-json-file-may-include-api-keys": "Warning: Critical data, JSON file may include API keys.", "api-key-copied": "API Key copied!", "your-template-does-not-have-any-variables": "Your template does not have any variables.", "code-is-ready-to-run": "Code is ready to run", "prompt-is-ready": "Prompt is ready", "success-your-api-key-has-been-saved": "Success! Your API Key has been saved.", "success-user-deleted": "Success! User deleted!", "success-user-edited": "Success! User edited!", "success-new-user-added": "Success! New user added!", "success-key-deleted": "Success! Key deleted!", "success-keys-deleted": "Success! Keys deleted!", "changes-saved-successfully": "Changes saved successfully!", "the-file-size-is-too-large-please-select-a-file-smaller-than-maxsizemb": "The file size is too large. Please select a file smaller than {{maxSizeMB}}.", "flow-built-successfully": "Flow built successfully", "manage-global-variables-and-assign-them-to-fields": "Manage global variables and assign them to fields.", "add-new": "Add New", "error-deleting-variable": "Error deleting variable", "id-not-found-for-variable-row": "ID not found for variable: {{row}}", "variable-name": "Variable Name", "type": "Type", "generic": "Generic", "credential": "Credential", "apply-to-fields": "Apply To Fields", "variable-name-initialdata-updated-created-successfully": "Variable {{name}} {{status}} successfully", "error-initialdata-updating-creating-variable": "Error {{status}} variable", "an-unexpected-error-occurred-while-initialdata-updating-a-new-creating-variable-please-try-again": "An unexpected error occurred while {{status}} variable. Please try again.", "updating-a-new": "updating a new", "updated": "updated", "created": "created", "updating": "updating", "creating": "creating", "this-variable-will-be-available-for-use-across-your-flows": "This variable will be available for use across your flows.", "update-variable": "Update Variable", "create-variable": "Create Variable", "enter-a-name-for-the-variable": "Enter a name for the variable...", "enter-a-value-for-the-variable": "Enter a value for the variable...", "apply-to-fields-0": "Apply to fields", "choose-a-field-for-the-variable": "Choose a field for the variable...", "selected-fields-will-auto-apply-the-variable-as-a-default-value": "Selected fields will auto-apply the variable as a default value.", "initialdata-update-save-variable": "{{status}} Variable", "Update": "Update", "Save": "Save", "manage-shortcuts-for-quick-access-to-frequently-used-actions": "Manage Shortcuts for quick access to frequently used actions.", "restore": "Rest<PERSON>", "api-key-save-error": "API key save error", "error-saving-key-combination": "Error saving key combination", "this-combination-already-exists": "This combination already exists!", "shortcut-0-shortcut-successfully-changed": "{{shortcut}} shortcut successfully changed", "recording-your-keyboard": "Recording your keyboard", "key-combination": "Key Combination", "apply": "Apply", "reset": "Reset", "inspect-edit-and-remove-messages-to-explore-and-refine-model-behaviors": "Inspect, edit and remove messages to explore and refine model behaviors.", "view-text": "View Text", "empty": "Empty", "finish-editing": "Finish Editing", "start-with-templates-showcasing-langflows-prompting-rag-and-agent-use-cases": "Start with templates showcasing Langflow's Prompting, RAG, and Agent use cases.", "templates": "Templates", "get-started": "Get started", "all-templates": "All templates", "use-cases": "Use Cases", "assistants": "Assistants", "classification": "Classification", "coding": "Coding", "content-generation": "Content Generation", "newspaper": "Newspaper", "methodology": "Methodology", "prompting": "Prompting", "agents": "Agents", "RAG": "RAG", "start-from-scratch": "Start from scratch", "begin-with-a-fresh-flow-to-build-from-scratch": "Begin with a fresh flow to build from scratch.", "blank-flow": "Blank Flow", "search": "Search...", "no-templates-found": "No templates found.", "clear-your-search": "Clear your search", "and-try-a-different-query": "and try a different query.", "options": "Options", "rename": "<PERSON><PERSON>", "upload-a-flow": "Upload a flow", "upload": "Upload", "flowdata-name-exported-successfully": "{{name}} exported successfully", "edit-details": "Edit details", "name": "Name", "character-limit-reached": "Character limit reached", "name-invalid-or-already-exists": "Name invalid or already exists", "flow-name": "Flow name", "description": "Description", "option": " (optional)", "flow-description": "Flow description", "no-description": "No description", "endpoint-name": "Endpoint Name", "invalid-endpoint-name-use-only-letters-numbers-hyphens-and-underscores": "Invalid endpoint name. Use only letters, numbers, hyphens, and underscores ({{maxLength}}  characters max).", "an-alternative-name-to-run-the-endpoint": "An alternative name to run the endpoint", "flowname": "Name", "details": "Details", "dismiss": "<PERSON><PERSON><PERSON>", "changes-saved-successfully-0": "Changes saved successfully", "components-reloaded-successfully": "Components reloaded successfully", "saved": "Saved", "saved-successfully": "Saved successfully", "flow-name-updated-successfully": "Flow name updated successfully", "error-updating-flow-name": "Error updating flow name", "invalid-flow-name": "Invalid flow name", "name-already-exists": "Name already exists", "edit-details-0": "Edit Details", "import": "Import", "export": "Export", "refresh-all": "Refresh All", "enable-auto-saving": "Enable auto-saving", "to-avoid-losing-progress": "to avoid losing progress.", "stop": "Stop", "newflow-is_component-component-flow-duplicated-successfully": "{{type}} duplicated successfully", "component": "Component", "flow": "Flow", "are-you-sure-you-want-to-delete-the-selected": "Are you sure you want to delete the selected", "note-this-action-is-irreversible": "Note: This action is irreversible.", "cancel": "Cancel", "selected-items-deleted-successfully": "Selected items deleted successfully", "please-try-again": "Please try again", "error-deleting-items": "Error deleting items", "edited": "Edited", "deleting-the-selected-flow-will-remove-all-associated-messages": "Deleting the selected flow will remove all associated messages.", "edited-time-ago": "Edited {{time}} ago", "less-than-a-minute": "less than a minute", "year": "year", "years": "years", "month": "month", "months": "months", "day": "day", "days": "days", "hour": "hour", "hours": "hours", "minute": "minute", "minutes": "minutes", "flow-saved-successfully": "Flow saved successfully!", "search-flows": "Search Flows", "search-components": "Search Components", "search-flows-and-components": "Search Flows and Components", "deleting-the-selected-folder-will-remove-all-associated-flows-and-components": "Deleting the selected folder will remove all associated flows and components.", "please-select-at-least-one-file": "Please select at least one file", "search-files": "Search files...", "no-files-found-try-again": "No files found, try again", "upload-or-import-files": "Upload or import files,", "or-visit": "or visit", "my-files-0": "My Files.", "import-from": "Import from...", "upload-failed": "Upload failed,", "try-again": "try again?", "remove": "Remove", "the-file-has-been-deleted-successfully": "The file has been deleted successfully", "delete-file": "Delete File", "remove-file": "Remove File", "are-you-sure-you-want-to-islocal-delete-remove-file-name": "Are you sure you want to {{action}} \"{{name}}\"?", "this-action-cannot-be-undone-the-file-will-be-permanently-deleted": "This action cannot be undone. The file will be permanently deleted.", "this-will-remove-the-file-from-your-list-you-can-add-it-back-later-if-needed": "This will remove the file from your list. You can add it back later if needed.", "file-filesids-length-greater-than-1-s-uploaded-successfully": "File{{padding}} uploaded successfully", "an-error-occurred-while-uploading-the-file": "An error occurred while uploading the file", "drop-files-here": "Drop files here", "click-or-drag-files-here": "Click or drag files here", "upload-file": "Upload File", "upload-files-or-import-from-your-preferred-cloud": "Upload files or import from your preferred cloud.", "drop-file-to-upload": "Drop file{{padding}} to upload", "prompts": "Prompts", "data": "Data", "processing": "Processing", "models": "Models", "vector-stores": "Vector Stores", "embeddings": "Embeddings", "loaders": "Loaders", "chains": "Chains", "link-extractors": "Link Extractors", "memories": "Memories", "output-parsers": "Output Parsers", "prototypes": "Prototypes", "retrievers": "Retrievers", "text-splitters": "Text Splitters", "toolkits": "<PERSON><PERSON><PERSON>", "logic": "Logic", "tools": "Tools", "helpers": "Helpers", "has-unsaved-changes": "has unsaved changes", "exit-anyway": "Exit anyway", "save-and-exit": "Save and Exit", "saving-your-changes": "Saving your changes...", "never": "Never", "unsaved-changes-will-be-permanently-lost": "Unsaved changes will be permanently lost.", "sorry-we-found-an-unexpected-error": "Sorry, we found an unexpected error!", "please-report-errors-with-detailed-tracebacks-on-the": "Please report errors with detailed tracebacks on the", "github-issues": "GitHub Issues", "thank-you": "Thank you!", "report-on-github": "Report on GitHub", "page": "page.", "restart-langflow": "<PERSON><PERSON>", "there-was-an-error-downloading-your-image": "There was an error downloading your image", "go-to-previous-page": "Go to previous page", "go-to-next-page": "Go to next page", "name-installed-successfully": "{{name}} Installed Successfully.", "error-installing-the-name": "Error installing the {{name}}", "error-liking-name": "Error liking {{name}}.", "private": "Private", "likes": "<PERSON>s", "downloads": "Downloads", "like": "Like", "please-review-your-api-key": "Please review your API key.", "install-locally": "Install Locally", "account-settings": "Account <PERSON><PERSON>", "logout": "Logout", "new": "New", "logs": "Logs", "auto-saving-is-disabled": "Auto-saving is disabled", "notifications-and-errors": "Notifications and errors", "downloaded": "Downloaded", "installed": "Installed", "error-isstore-downloading-installing-the-name": "Error {{action}} the {{name}}", "successfully": "Successfully.", "name-isstore-downloaded-installed-successfully": "{{name}} {{action}} Successfully.", "this-flow-doesnt-have-a-playground": "This flow doesn't have a playground.", "error-getting-flow-data": "Error getting flow data.", "toynrick": "Toy<PERSON>rick", "drop-your-file-here": "Drop your file here", "choose-an-option": "Choose an option...", "failed-to-save-flow": "Failed to save flow", "flows-variable-undefined": "Flows variable undefined", "publish": "Publish", "api-access": "API access", "embed-into-site": "Embed into site", "active-to-share-a-public-version-of-this-playground": "Active to share a public version of this Playground", "add-a-chat-input-or-chat-output-to-access-your-flow": "Add a Chat Input or Chat Output to access your flow", "shareable-playground": "Shareable Playground", "add-a-chat-input-or-chat-output-to-use-the-playground": "Add a Chat Input or Chat Output to use the playground", "store-api-key-required": "Store API Key Required", "share": "Share", "loading-options": "Loading options", "search-options": "Search options...", "refresh-list": "Refresh list", "new-firstword": "New {{firstWord}}", "no-options-found": "No options found", "invalid-result": "<PERSON><PERSON><PERSON>", "the-filtered-result-contains-values-that-cannot-be-serialized-to-json": "The filtered result contains values that cannot be serialized to JSON", "the-filtered-result-must-be-a-json-object-or-array-not-a-primitive-value": "The filtered result must be a JSON object or array, not a primitive value", "invalid-path": "Invalid Path", "path-transformquery-led-to-undefined-or-null-value": "Path '{{transformQuery}}' led to undefined or null value", "invalid-array-index": "Invalid Array Index", "index-index-is-out-of-bounds-for-array-of-length-result-length": "Index {{index}} is out of bounds for array of length {{len}}", "invalid-property": "Invalid Property", "property-key-does-not-exist-in-array-items": "Property '{{key}}' does not exist in array items", "property-key-does-not-exist-in-object": "Property '{{key}}' does not exist in object", "transform-resulted-in-undefined-value": "Transform resulted in undefined value", "transform-error": "Transform Error", "endpoint-url-copied": "Endpoint URL copied", "type-a-float-number": "Type a float number", "float-number": "Float number", "upload-a-file": "Upload a file...", "select-file": "Select file{{padding}}", "add-new-variable": "Add New Variable", "add-more": "Add More", "integer-number": "Integer number", "type-an-integer-number": "Type an integer number", "type-key": "Type key...", "type-a-value": "Type a value...", "no-values-found": "No values found.", "no-parameters-are-available-for-display": "No parameters are available for display.", "type-your-prompt-here": "Type your prompt here...", "reset-columns": "Reset Columns", "cannot-change-visibility-of-connected-handles": "Cannot change visibility of connected handles", "change-visibility-of-the-field": "Change visibility of the field", "select-an-option": "Select an option", "add-a-new-row": "Add a new row", "select-items-to-duplicate": "Select items to duplicate", "duplicate-selected-items": "Duplicate selected items", "select-items-to-delete": "Select items to delete", "delete-selected-items": "Delete selected items", "open-table": "Open Table", "add-or-edit-data": "Add or edit data", "table": "Table", "open": "Open", "alert": "<PERSON><PERSON>", "button": "<PERSON><PERSON>", "duplicate-request": "Duplicate request:", "request-already-in-progress": "Request already in progress", "failed-to-start-polling": "Failed to start polling", "could-not-load-flows-from-database": "Could not load flows from database", "cant-connect-to-the-same-node": "Can't connect to the same node", "connect": "Connect", "incompatible-with": "Incompatible with", "input-plural-type-plural": "Input{{plural}} type{{plural}}", "output-plural-type-plural": "Output{{plural}} type{{plural}}", "drag": "Drag", "to-connect-compatible": "to connect compatible", "input": "input", "output": "output", "click": "Click", "to-filter-compatible": "to filter compatible {{input}}", "and-components": "and components", "missing-required-fields": "Missing required fields", "show-output": "Show output", "hide-output": "Hide output", "output-cant-be-displayed": "Output can't be displayed", "inspect-output": "Inspect output", "please-build-the-component-first": "Please build the component first", "looping": "Looping", "stop-build": "Stop build", "run-component": "Run component", "update-component": "Update component", "no-output": "NO OUTPUT", "streaming-is-not-supported": "Streaming is not supported", "use-the-playground-to-interact-with-components-that-stream-data": "Use the playground to interact with components that stream data", "inspect-the-output-of-the-component-below": "Inspect the output of the component below.", "component-output": "Component Output", "error-updating-component-code": "Error updating Component code", "there-was-an-error-updating-the-component": "There was an error updating the Component.", "if-the-error-persists-please-report-it-on-our-discord-or-github": "If the error persists, please report it on our Discord or GitHub.", "error-in-component-data-node-display_name": "Error in component {{name}}", "the-component-data-node-display_name-has-no-template": "The component {{name}} has no template.", "please-contact-the-developer-of-the-component-to-fix-this-issue": "Please contact the developer of the component to fix this issue.", "template-not-found-in-the-component": "Template not found in the component", "parameter-not-found-in-the-template": "Parameter not found in the template", "data-id-docs-is-not-available-at-the-moment": "{{id}} docs is not available at the moment.", "pick-color": "Pick Color", "show-more": "Show More", "double-click-to-start-typing-or-enter-markdown": "Double-click to start typing or enter Markdown...", "file-type-not-allowed-allowed-types-types-join": "File type not allowed. Allowed types: {{type}}", "file-type-not-allowed": "File type not allowed", "multiple-files-are-not-allowed": "Multiple files are not allowed", "could-not-create-flow": "Could not create flow", "an-unexpected-error-occurred-please-try-again": "An unexpected error occurred, please try again", "flow-not-found": "Flow not found", "you-cannot-upload-a-component-as-a-flow-or-vice-versa": "You cannot upload a component as a flow or vice versa", "invalid-flow-data": "Invalid flow data", "hello-world": "hello world!", "api-access-requires-an-api-key-you-can": "API access requires an API key. You can", "create-an-api-key": "create an API key", "in-settings": "in settings.", "close": "Close", "check-and-save": "Check & Save", "discard-changes": "Discard Changes", "caution": "Caution", "are-you-sure-you-want-to-exit-without-saving-your-changes": "Are you sure you want to exit without saving your changes?", "error-getting-dictionary": "Error getting dictionary", "check-your-dictionary-format": "Check your dictionary format", "customize-your-dictionary-adding-or-editing-key-value-pairs-as-needed-supports-adding-new": "Customize your dictionary, adding or editing key-value pairs as needed. Supports adding new", "objects": "objects", "or": "or", "arrays": "arrays [].", "edit-dictionary": "Edit Dictionary", "view-dictionary": "View Dictionary", "field-name": "Field Name", "value": "Value", "show": "Show", "inspect-component-executions": "Inspect component executions.", "edit-prompt": "Edit Prompt", "prompt-variables": "Prompt Variables:", "prompt-variables-can-be-created-with-any-chosen-name-inside-curly-brackets-e-g": "Prompt variables can be created with any chosen name inside curly brackets, e.g.", "remove-filter": "Remove filter", "input-0": "Input{{plural}}", "output-0": "Output{{plural}}", "add-note": "Add Note", "components-with-errors": "Components with errors:", "search-0": "Search", "discover-more": "Discover More", "experimental": "Experimental", "bundles": "Bundles", "legacy": "Legacy", "discover-more-components": "Discover more components", "new-custom-component": "New Custom Component", "component-settings": "Component settings", "chat-input-already-added": "Chat input already added", "webhook-already-added": "Webhook already added", "data-id-successfully-overridden": "{{id}} successfully overridden!", "new-component-successfully-saved": "New component successfully saved!", "create-new": "Create New", "replace": "Replace", "it-seems-name-already-exists-do-you-want-to-replace-it-with-the-current-or-create-a-new-one": "It seems  {{name}} already exists. Do you want to replace it with the current or create a new one?", "minimization-only-available-for-components-with-one-handle-or-fewer": "Minimization only available for components with one handle or fewer.", "you-can-not-access-data-id-code": "You can not access {{id}} code", "data-id-saved-successfully": "{{id}} saved successfully", "data-id-docs-is-not-available-at-the-moment-0": "{{id}} docs is not available at the moment.", "freeze-path": "Freeze Path", "expand": "Expand", "ungroup": "Ungroup", "error-updating-components": "Error updating components", "there-was-an-error-updating-the-components": "There was an error updating the components.", "some-edges-were-lost-after-updating-the-components-please-review-the-flow-and-reconnect-them": "Some edges were lost after updating the components. Please review the flow and reconnect them.", "successfully-updated-updatedcount-component-updatedcount-greater-than-1-s": "Successfully updated {{updatedCount}} component{{padding}}", "update-all": "Update All", "ready-to-update": "ready to update", "len-component-is-ready-to-update": "{{len}} component{{is}} ready to update", "is_component-component-flow-uploaded-successfully": "{{type}} uploaded successfully", "import-from-json": "Import from JSON", "no-items-selected": "No items selected", "please-select-items-to-delete": "Please select items to delete", "modified": "Modified", "drop-files-to-upload": "Drop files to upload", "no-files": "No files", "key": "Key", "created-0": "Created", "last-used": "Last Used", "total-uses": "Total Uses", "unknown-error": "Unknown Error", "invalid-components": "Invalid components", "error-polling-build-events": "Error polling build events", "error-starting-build-process": "Error starting build process", "build-job-not-found": "Build job not found", "error-processing-build-events": "Error processing build events", "error-building-component": "Error Building Component", "network-error-please-check-the-connection-to-the-server": "Network error. Please check the connection to the server.", "error-building-flow": "Error Building Flow", "an-unexpected-error-occurred": "An unexpected error occurred", "an-unexpected-error-occurred-while-building-the-component-please-try-again": "An unexpected error occurred while building the Component. Please try again.", "weve-noticed-a-potential-issue-with-a-component-in-the-flow-please-review-it-and-if-necessary-submit-a-bug-report-with-your-exported-flow-file-thank-you-for-your-help": "We've noticed a potential issue with a Component in the flow. Please review it and, if necessary, submit a bug report with your exported flow file. Thank you for your help!", "no-components-found-in-the-flow-please-add-at-least-one-component-to-the-flow": "No components found in the flow. Please add at least one component to the flow.", "displayname-or-or-type-is-missing-getfieldtitle-template-t": "{{type}} is missing {{t}}.", "displayname-or-or-type-getfieldtitle-template-t-contains-duplicate-keys-with-the-same-values": "{{type}} ({{t}}) contains duplicate keys with the same values.", "displayname-or-or-type-getfieldtitle-template-t-field-must-not-be-empty": "{{type}} ({{t}}) field must not be empty.", "please-select-more-than-one-component": "Please select more than one component", "select-non-input-output-components-only": "Select non-input/output components only", "select-only-one-component-with-free-outputs": "Select only one component with free outputs", "select-only-connected-components": "Select only connected components", "untitled-document": "Untitled document", "string-is-blank": "String is blank", "error-in-parser-parser": "Error in parser {{parser}}", "zoom-in": "Zoom In", "zoom-out": "Zoom Out", "fit-to-zoom": "Fit To Zoom", "lock": "Lock", "unlock": "Unlock", "create-chain-communicate": "Create, Chain, Communicate.", "unleashing-linguistic-creativity": "Unleashing Linguistic Creativity.", "graph-your-way-to-great-conversations": "Graph Your Way to Great Conversations.", "the-power-of-language-at-your-fingertips": "The Power of Language at Your Fingertips.", "where-language-meets-logic": "Where Language Meets Logic.", "sculpting-language-with-precision": "Sculpting Language with Precision.", "building-intelligent-interactions": "Building Intelligent Interactions.", "your-passport-to-linguistic-landscapes": "Your Passport to Linguistic Landscapes.", "create-curate-communicate-with-langflow": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Communicate with <PERSON><PERSON>.", "flow-into-the-future-of-language": "Flow into the Future of Language.", "mapping-meaningful-conversations": "Mapping Meaningful Conversations.", "unravel-the-art-of-articulation": "Unravel the Art of Articulation.", "language-engineering-excellence": "Language Engineering Excellence.", "navigate-the-networks-of-conversation": "Navigate the Networks of Conversation.", "crafting-conversations-one-node-at-a-time": "Crafting Conversations, One Node at a Time.", "the-pinnacle-of-prompt-generation": "The Pinnacle of Prompt Generation.", "language-models-mapped-and-mastered": "Language Models, Mapped and Mastered.", "powerful-prompts-perfectly-positioned": "Powerful Prompts, Perfectly Positioned.", "innovation-in-interaction-with-langflow": "Innovation in Interaction with Langflow.", "your-toolkit-for-text-generation": "Your Toolkit for Text Generation.", "unfolding-linguistic-possibilities": "Unfolding Linguistic Possibilities.", "building-powerful-solutions-with-language-models": "Building Powerful Solutions with Language Models.", "uncover-business-opportunities-with-nlp": "Uncover Business Opportunities with NLP.", "harness-the-power-of-conversational-ai": "Harness the Power of Conversational AI.", "transform-your-business-with-smart-dialogues": "Transform Your Business with Smart Dialogues.", "craft-meaningful-interactions-generate-value": "Craft Meaningful Interactions, Generate Value.", "unleashing-business-potential-through-language-engineering": "Unleashing Business Potential through Language Engineering.", "empowering-enterprises-with-intelligent-interactions": "Empowering Enterprises with Intelligent Interactions.", "driving-innovation-in-business-communication": "Driving Innovation in Business Communication.", "catalyzing-business-growth-through-conversational-ai": "Catalyzing Business Growth through Conversational AI.", "text-generation-meets-business-transformation": "Text Generation Meets Business Transformation.", "navigate-the-linguistic-landscape-discover-opportunities": "Navigate the Linguistic Landscape, Discover Opportunities.", "empowering-communication-enabling-opportunities": "Empowering Communication, Enabling Opportunities.", "create-powerful-connections-boost-business-value": "Create Powerful Connections, Boost Business Value.", "advanced-nlp-for-groundbreaking-business-solutions": "Advanced NLP for Groundbreaking Business Solutions.", "innovation-in-interaction-revolution-in-revenue": "Innovation in Interaction, Revolution in Revenue.", "maximize-impact-with-intelligent-conversations": "Maximize Impact with Intelligent Conversations.", "beyond-text-generation-unleashing-business-opportunities": "Beyond Text Generation - Unleashing Business Opportunities.", "unlock-the-power-of-ai-in-your-business-conversations": "Unlock the Power of AI in Your Business Conversations.", "crafting-dialogues-that-drive-business-success": "Crafting Dialogues that Drive Business Success.", "engineered-for-excellence-built-for-business": "Engineered for Excellence, Built for Business.", "editing-messages-will-update-the-memory-but-wont-restart-the-conversation": "Editing messages will update the memory but won't restart the conversation.", "saving": "Saving...", "build-stopped": "Build stopped", "messages-deleted-successfully": "Messages deleted successfully.", "error-deleting-messages": "Error deleting messages.", "messages-updated-successfully": "Messages updated successfully.", "error-updating-messages": "Error updating messages.", "store": "Store", "go-to-langflow-store": "Go to Langflow Store", "Value": "Value", "name-1": "Name*", "value-1": "Value*", "sign-up-for-langflow": "Sign up for Lang<PERSON>", "username": "Username", "please-enter-your-username": "Please enter your username", "password": "Password", "please-enter-a-password": "Please enter a password", "confirm-your-password": "Comfirm your password", "please-confirm-your-password": "Please comfirm your password", "sign-up-0": "Sign up", "already-have-an-account-and-nbsp": "Already have an account and ", "sign-in": "Sign in", "confirm-password-0": "Comfirm password", "attention": "Attention", "are-you-completely-confident-about-the-changes-you-are-making-to-this-user": "Are you completely confident about the changes you are making to this user", "are-you-sure-you-want-to-delete-this-user-this-action-cannot-be-undone": "Are you sure you want to delete this user? this action cannot be undone", "type-your-new-password-and-confirm-it": "Type your new password and confirm it", "confirm-password": "confirm password", "bot": "Bot", "user": "User", "machine": "Machine", "new-chat": "New chat", "test-your-flow-with-a-chat-prompt": "Test your flow with a chat prompt", "send": "Send", "run-flow": "Run Flow", "add-a": "Add a", "chat-input": "Chat Input", "component-to-your-flow-to-send-messages": "component to your flow to send messages.", "drop-here": "Drop here", "attach-image-png-jpg-jpeg": "Attach image (png, jpg, jpeg)", "copy-code": "Copy Code", "enter-file-name": "enter file name", "copied": "Copied!", "flow-running": "Flow running...", "an-error-occured-in-the": "An error occured in the", "component-stopping-your-flow-see-below-for-more-details": "Component, stopping your flow. See below for more details.", "error-details": "Error details:", "steps-to-fix": "Steps to fix:", "check-the-component-settings": "Check the component settings", "field": "Field: ", "ensure-all-required-fields-are-filled": "Ensure all required fields are filled", "re-run-your-flow": "Re-run your flow", "edit-message": "Edit message", "copy-message": "Copy message", "helpful": "Helpful", "not-helpful": "Not helpful", "error-on-streaming": "Error on Streaming", "streaming-failed": "Streaming failed", "default-session": "Default Session", "new-chat-0": "New Chat", "chat": "Cha<PERSON>", "session-deleted-successfully": "Session deleted successfully.", "error-deleting-session": "Error deleting Session.", "hide-sidebar": "Hide sidebar", "theme": "Theme", "built-with-langflow": "Built with Langflow", "retry": "Retry", "Playground": "Playground", "dialog": "Dialog", "select-files": "Select files", "select-prefix-formattedname": "Select {{prefix}} {{name}}", "edit-totitlecase-name": "Edit {{name}}", "running_components": "Running components", "you-can-only-have-one-chat-input-component-in-a-flow": "You can only have one Chat Input component in a flow.", "built_successfully": "built successfully", "basic-prompting": "Basic Prompting", "vector-store-rag": "Vector Store RAG", "simple-agent": "Simple Agent", "Size": "Size", "Agents": "Agents", "message-logs": "Message logs", "session": "Session", "en-US": "en-US", "folder": "folder", "variable": "variable", "duration": "Duration:", "active": "Active", "add-a-new-user": "Add a new user", "beta": "Beta", "admin": "Admin", "category": "Category", "confirm": "Confirm", "content_blocks": "content blocks", "created-at": "created at", "delete-user": "delete user", "dont-have-an-account-and-nbsp": "Don't have an account ?", "files": "Files", "langflow": "LangFlow", "edit": "Edit", "id": "ID", "login": "<PERSON><PERSON>", "new-user": "New user", "name-0": "Name", "please-enter-your-password": "Please enter your password", "no-users-registered": "No users registered", "properties": "Porperties", "sender": "Sender", "session_id": "Seesion id", "sender_name": "Sender name", "search-username": "Search username", "sign-in-to-langflow": "Sign in to Langflow", "sign-up": "Sign up", "superuser": "Superuser", "text": "Text", "timestamp": "timestamp", "type-1": "type*", "updated-at": "updated at", "mcp": "MCP", "websearch": "Web Search", "Functionality": "Functionality", "Keyboard Shortcut": "Keyboard Shortcut", "flow_id": "flow_id", "api-key-error-0": "API key error:", "please-check-your-api-key-and-try-again": "Please check your API key and try again", "api-key-not-valid": "API key not valid", "cancellation-failed-no-active-response-found": "Cancellation failed: no active response found", "voice-settings": "Voice settings", "voice-chat-is-powered-by-openai-you-can-also-add-more-voices-with-elevenlabs": "Voice chat is powered by OpenAI. You can also add more voices with ElevenLabs.", "openai-api-key": "OpenAI API Key", "openai-api-key-is-required-to-use-the-voice-assistant": "OpenAI API key is required to use the voice assistant.", "enter-your-openai-api-key": "Enter your OpenAI API key", "elevenlabs-api-key": "ElevenLabs API Key", "if-you-have-an-elevenlabs-api-key-you-can-select-elevenlabs-voices": "If you have an ElevenLabs API key, you can select ElevenLabs voices.", "enter-your-elevenlabs-api-key": "Enter your ElevenLabs API key", "audio-input": "Audio Input", "select-which-microphone-to-use-for-voice-input": "Select which microphone to use for voice input", "select-microphone": "Select microphone", "no-microphones-found": "No microphones found", "microphone": "Microphone", "voice": "Voice", "you-can-select-elevenlabs-voices-if-you-have-an-elevenlabs-api-key-otherwise-you-can-only-select-openai-voices": "You can select ElevenLabs voices if you have an ElevenLabs API key. Otherwise, you can only select OpenAI voices.", "select": "Select", "preferred-language": "Preferred Language", "select-the-language-for-speech-recognition": "Select the language for speech recognition", "select-language": "Select language", "create-api-key": "Create API Key", "create-a-secret-api-key-to-use-langflow-api": "Create a secret API Key to use Langflow API.", "my-api-key": "My API Key", "generate-api-key": "Generate API Key", "please-save-this-secret-key-somewhere-safe-and-accessible-for-security-reasons": "Please save this secret key somewhere safe and accessible. For security reasons,", "you-wont-be-able-to-view-it-again": "you won't be able to view it again", "through-your-account-if-you-lose-this-secret-key-youll-need-to-generate-a-new-one": "through your account. If you lose this secret key, you'll need to generate a new one.", "optional": "optional", "store-api-key": "Store API Key", "insert-your-api-key": "Insert your API Key", "please-enter-your-api-key": "Please enter your API Key", "manage-access-to-the-langflow-store": "Manage access to the Langflow Store.", "unmute": "Unmute", "mute": "Mute", "no-components-found": "No components found.", "or-filter-and-try-a-different-query": "or filter and try a different query."}