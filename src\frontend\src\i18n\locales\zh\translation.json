{"Get started": "开始", "New Flow": "新流程", "Submit": "提交", "Error": "错误", "langflow": "wiseAgent", "empty-folder": "空文件夹", "begin-with-a-template-or-start-from-scratch": "从模板开始，或从零开始", "new-flow": "新流程", "empty-folder-0": "空文件夹", "start-building": "开始构建", "plus": "添加", "admin-page": "管理页面", "error-deleting-folder": "删除文件夹时出错", "folder-deleted-successfully": "文件夹已成功删除", "notifications": "通知", "settings": "设置", "version": "版本", "export-flow-as-json-file": "将流程导出为 JSON 文件", "no-new-notifications": "无新通知", "account-created-await-admin-activation": "账户已创建，等待管理员激活", "accurate-to-within-the-hour-from-the-most-recent-usage": "自最近一次使用起一小时内准确有效", "adjust-components-settings-and-define-parameter-visibility-remember-to-save-your-changes": "调整组件设置并定义参数可见性。\n请记住保存所做的更改。", "avaliable-input-components": "可用的输入组件：", "avaliable-output-components": "可用的输出组件：", "build-to-validate-status": "构建以验证状态。", "built-sucessfully": "成功构建", "caution-unchecking-this-box-only-removes-api-keys-from-fields-specifically-designated-for-api-keys": "注意：取消选中此框只会从专门为 API 密钥指定的字段中移除 API 密钥。", "chat-cannot-open": "聊天无法打开", "check-if-everything-is-working-properly-and-try-again": "检查一切是否正常运行，然后重试", "collapse-hidden-outputs": "折叠隐藏的输出", "couldnt-establish-a-connection": "无法建立连接。", "create-your-prompt-prompts-can-help-guide-the-behavior-of-a-language-model-use-curly-brackets-to-introduce-variables": "创建你的提示。\n提示可以帮助指导语言模型的行为。\n使用大括号（{}）引入变量。", "csv-output": "CSV 格式输出", "customize-your-dictionary-adding-or-editing-key-value-pairs-as-needed-supports-adding-new-objects-or-arrays": "根据需要自定义字典，添加或编辑键值对。\n支持添加新的对象（{}）或数组（[]）。", "customize-your-flow-details-and-settings": "自定义你的流程详情和设置", "dont-have-an-api-key-sign-up-at": "没有 API 密钥？请在此处注册", "edit-code": "编辑代码", "edit-text": "编辑文本", "edit-text-content": "编辑文本内容", "edit-your-python-code-snippet-refer-to-the-langflow-documentation-for-more-information-on-how-to-write-your-own-component": "编辑您的Python代码段。\n有关如何编写自己的组件的更多信息，请参阅wiseAgent文档。", "error-loading-csv": "加载 CSV 文件时出错", "error-loading-pdf": "加载 PDF 文件时出错", "execution-blocked": "执行被阻止", "expand-hidden-outputs": "展开隐藏的输出", "expand-the-ouptut-to-see-the-pdf": "展开输出以查看 PDF 文件", "expand-the-view-to-see-the-image": "放大视图以查看图像", "explore-community-shared-flows-and-components": "探索社区共享的流程和组件", "explore-detailed-logs-of-events-and-transactions-between-components": "探索组件之间事件和事务的详细日志", "export-your-flow-to-integrate-it-using-this-code": "导出你的流程，以便使用此代码进行集成", "flow-not-built": "流程未构建", "generate-the-code-to-integrate-your-flow-into-an-external-application": "生成代码，以便将你的流程集成到外部应用程序中", "image-output": "图像输出", "import-flows-from-a-json-file-or-choose-from-pre-existing-examples": "从 JSON 文件导入流程，或从已有的示例中选择", "inputs": "输入", "insert-your-langflow-api-key": "插入您的wiseAgent API键。", "interact-with-your-ai-monitor-inputs-outputs-and-memories": "与你的人工智能进行交互。监控输入、输出和记忆", "langflow-chat": "wiseAgent聊天", "langflow-store": "wiseAgent商店", "last-run": "上次运行：", "last-saved": "上次保存：", "manage-your-projects-download-and-upload-entire-collections": "管理你的项目。\n下载并上传整个集合", "my-collection": "我的收藏", "my-projects": "我的项目", "navigate-through-this-section-to-efficiently-oversee-all-application-users-from-here-you-can-seamlessly-manage-user-accounts": "浏览本节内容，以便有效地监督所有应用程序用户。\n从这里，你可以无缝管理用户账户", "no-chat-input-variables-found-click-to-run-your-flow": "未找到聊天输入变量。\n点击以运行你的流程", "no-compatible-components-found": "未找到兼容的组件", "no-data-available": "无可用数据", "outputs": "输出", "pdf-output": "PDF 格式输出", "please-build-the-flow-before-chatting": "聊天前请先构建流程", "please-check-your-flow-and-try-again": "请检查你的流程，然后重试", "please-wait-a-few-moments-while-the-server-processes-your-request": "服务器正在处理你的请求，请稍等片刻", "run-the-flow-or-inform-a-valid-url-to-see-your-image": "运行该流程，或者提供一个有效的网址以查看你的图片", "run-the-flow-to-see-the-pdf": "运行流程以查看 PDF 文件", "save-with-my-api-keys": "使用我的 API 密钥保存", "send-a-message": "发送消息...", "server-is-busy": "服务器繁忙", "some-connections-were-removed-because-they-were-invalid": "由于一些连接无效，已将其移除", "start-a-conversation-and-click-the-agents-memories": "开始对话并点击智能体的记忆", "starter-projects": "入门项目", "the-last-time-this-key-was-used": "此密钥的上次使用时间", "this-is-not-a-chat-flow": "这不是一个聊天流程", "this-user-does-not-have-any-keys-assigned-at-the-moment": "目前，该用户尚未分配任何密钥", "to-inspect-previous-messages": "查看之前的消息", "type-message-here": "在此处输入消息", "you-dont-have-an-api-key": "你没有 API 密钥", "your-api-key-is-not-valid": "你的 API 密钥无效", "your-component-is-outdated-click-to-update-data-may-be-lost": "你的组件已过时。\n点击进行更新（数据可能会丢失）", "your-secret-langflow-api-keys-are-listed-below-do-not-share-your-api-key-with-others-or-expose-it-in-the-browser-or-other-client-side-code": "你的 wiseAgent API 密钥如下所示。请勿与他人共享你的 API 密钥，也不要在浏览器或其他客户端代码中暴露它", "all": "全部", "an-unexpected-error-occurred-while-updating-the-component-please-try-again": "更新组件时发生意外错误。\n请重试", "api-key-saved-successfully": "API 密钥已成功保存", "building": "构建中...", "components": "组件", "endpoint-not-available": "端点不可用", "error-while-updating-the-component": "更新组件时出错", "flows": "流程", "message-empty": "消息为空", "no-column-definitions": "无列定义", "no-data-available-0": "无可用数据", "no-input-message-provided": "未提供输入消息", "oops-it-seems-theres-no-data-to-display-right-now-please-check-back-later": "哎呀！\n似乎目前没有数据可显示。\n请稍后再查看", "playground": "练习场", "please-ensure-your-file-has-one-of-the-following-extensions": "请确保你的文件具有以下扩展名之一：", "receiving-input": "正在接收输入", "streaming-not-supported": "不支持流式传输", "there-are-no-column-definitions-available-for-this-table": "此表没有可用的列定义", "type-something": "输入内容...", "used-as-a-tool": "用作工具", "changes-save": "保存更改", "code": "代码", "component-share": "组件共享", "controls": "控件", "copy": "复制", "cut": "剪切", "delete": "删除", "docs": "文档", "download": "下载", "duplicate": "重复", "flow-share": "流程共享", "freeze": "冻结", "group": "分组", "minimize": "最小化", "open-playground": "打开练习场", "output-inspection": "输出检查", "paste": "粘贴", "play": "运行", "redo": "重做", "redo-alternative": "重做（替代操作）", "save-component": "保存组件", "search-components-on-sidebar": "在侧边栏搜索组件", "toggle-sidebar": "切换侧边栏", "tool-mode": "工具模式", "undo": "撤销", "update": "更新", "create-new-folder": "创建新文件夹", "folder-exported": "文件夹已导出", "folder-uploaded-successfully": "文件夹已成功上传", "my-files": "我的文件", "new-folder": "新文件夹", "uploaded-successfully": "已成功上传", "create-a-new-flow": "创建新流程", "create-new-folder-0": "创建新文件夹", "creating-custom-components": "创建自定义组件", "drop-your-isemptyfolder-flows-or-components-flowtype-here": "在此处放下你的 {{flowType}}", "flows-or-components": "流程或组件", "no-flows-in-this-folder": "此文件夹中没有流程", "no-saved-or-custom-components-learn-more-about": "没有保存的或自定义的组件。\n了解更多关于", "or-browse-the-store": "，或浏览商店。", "search-flowtype": "搜索{{flowType}} ...", "api-key": "API密钥", "created-by-me": "由我创建", "error-on-uploading-your-folder-try-dragging-it-into-an-existing-folder": "上传文件夹时出错，请尝试将其拖动到现有文件夹中", "langflow-0": "wiseAgent", "liked-by-me": "我点赞的", "you-need-to-enable-javascript-to-run-this-app": "你需要启用 JavaScript 才能运行此应用程序", "folders": "文件夹", "loading": "加载中...", "general": "常规设置", "global-variables": "全局变量", "langflow-api-keys": "wiseAgent API密钥", "manage-the-general-settings-for-langflow": "管理wiseAgent的常规设置。", "messages": "消息", "shortcuts": "快捷键", "manage-settings-related-to-langflow-and-your-account": "管理与wiseAgent和你的账户相关的设置。", "choose-the-image-that-appears-as-your-profile-picture": "选择用作你个人资料图片的图像", "profile-picture": "个人资料图片", "save": "保存", "api-key-copied": "API 密钥已复制！", "api-key-error": "API密钥错误", "changes-saved-successfully": "更改已成功保存！", "code-is-ready-to-run": "代码已准备好运行", "error-changing-password": "更改密码时出错", "error-getting-components": "获取组件时出错", "error-occurred-while-uploading-file": "上传文件时发生错误", "error-on-delete-key": "删除密钥时出错", "error-on-delete-keys": "删除密钥时出错", "error-on-delete-user": "删除用户时出错", "error-on-edit-user": "编辑用户时出错", "error-retrieving-profile-pictures": "检索个人资料图片时出错", "error-saving-changes": "保存更改时出错", "error-signing-in": "登录时出错", "error-signing-up": "注册时出错", "error-uploading-file": "上传文件时出错", "error-when-adding-new-user": "添加新用户时出错", "file-uploaded-successfully": "文件已成功上传", "invalid-file-type": "无效的文件类型", "invalid-selection": "无效的选择", "oops-looks-like-you-missed-some-required-information": "哎呀！\n看起来你遗漏了一些必填信息：", "oops-looks-like-you-missed-something": "哎呀！\n看起来你遗漏了某些内容", "passwords-do-not-match": "密码不匹配", "please-build-the-flow-again-before-using-the-chat": "在使用聊天功能之前，请再次构建流程", "please-select-a-valid-file-only-these-file-types-are-allowed": "请选择一个有效的文件。\n仅允许以下文件类型：", "please-upload-a-json-file": "请上传一个 JSON 文件", "prompt-is-ready": "提示已准备好", "something-went-wrong-please-try-again": "出了点问题，请重试", "success-key-deleted": "成功！密钥已删除！", "success-keys-deleted": "成功！密钥已删除！", "success-new-user-added": "成功！已添加新用户！", "success-user-deleted": "成功！用户已删除！", "success-user-edited": "成功！用户已编辑！", "success-your-api-key-has-been-saved": "成功！\n你的 API 密钥已保存", "the-file-size-is-too-large-please-select-a-file-smaller-than-maxsizemb": "文件大小太大。\n请选择一个小于 {{maxSizeMB}}的文件", "the-flow-has-an-incomplete-loop-check-your-connections-and-try-again": "该流程存在不完整的循环。\n检查你的连接，然后重试", "there-is-an-error-in-your-function": "你的函数存在错误", "there-is-an-error-in-your-imports": "你的导入存在错误", "there-is-no-chatoutput-component-in-the-flow": "流程中没有聊天输出组件。", "there-is-something-wrong-with-this-code-please-review-it": "此代码存在问题，请检查", "there-is-something-wrong-with-this-prompt-please-review-it": "此提示存在问题，请检查", "there-was-an-error-saving-the-api-key-please-try-again": "保存 API 密钥时出错，请重试", "there-was-an-error-sending-the-message": "发送消息时出错", "warning-critical-data-json-file-may-include-api-keys": "警告：关键数据，JSON 文件可能包含 API 密钥", "you-dont-have-an-api-key-please-add-one-to-use-the-langflow-store": "你没有API密钥。\n请添加一个以使用wiseAgent商店。", "your-api-key-is-not-valid-please-add-a-valid-api-key-to-use-the-langflow-store": "你的API密钥无效。\n请添加一个有效的API密钥以使用wiseAgent商店。", "your-template-does-not-have-any-variables": "你的模板没有任何变量", "flow-built-successfully": "流程已成功构建", "add-new": "添加新项", "apply-to-fields": "应用到字段", "credential": "凭证", "error-deleting-variable": "删除变量时出错", "generic": "通用的", "id-not-found-for-variable-row": "变量行 {{row}} 找不到 ID", "manage-global-variables-and-assign-them-to-fields": "管理全局变量并将它们分配到字段", "type": "类型", "variable-name": "变量名称", "Save": "保存", "Update": "更新", "an-unexpected-error-occurred-while-initialdata-updating-a-new-creating-variable-please-try-again": "在 {{status}} 变量时发生意外错误。\n请重试", "apply-to-fields-0": "应用到字段", "choose-a-field-for-the-variable": "为变量选择一个字段...", "create-variable": "创建变量", "created": "已创建", "creating": "创建中", "enter-a-name-for-the-variable": "输入变量名称...", "enter-a-value-for-the-variable": "输入变量值...", "error-initialdata-updating-creating-variable": "{{status}} 变量时出错", "initialdata-update-save-variable": "{{status}}变量", "selected-fields-will-auto-apply-the-variable-as-a-default-value": "所选字段将自动将该变量应用为默认值", "this-variable-will-be-available-for-use-across-your-flows": "该变量将在你的所有流程中可用", "update-variable": "更新变量", "updated": "已更新", "updating": "更新中", "updating-a-new": "更新新的", "variable-name-initialdata-updated-created-successfully": "变量 {{name}} 已成功 {{status}}", "api-key-save-error": "API 密钥保存错误", "apply": "应用", "error-saving-key-combination": "保存快捷键组合时出错", "key-combination": "快捷键组合", "manage-shortcuts-for-quick-access-to-frequently-used-actions": "管理快捷方式，以便快速访问常用操作。", "recording-your-keyboard": "正在记录您的键盘操作", "reset": "重置", "restore": "恢复", "shortcut-0-shortcut-successfully-changed": "{{shortcut}} 快捷方式已成功更改", "this-combination-already-exists": "这种组合已存在！", "inspect-edit-and-remove-messages-to-explore-and-refine-model-behaviors": "检查、编辑和删除消息，以探索并优化模型行为。", "RAG": "检索增强生成（RAG）", "agents": "智能体", "all-templates": "所有模板", "assistants": "智能助手", "begin-with-a-fresh-flow-to-build-from-scratch": "从全新的流程开始，从零进行构建。", "blank-flow": "空白流程", "classification": "分类", "coding": "编码", "content-generation": "内容生成", "empty": "空的", "finish-editing": "完成编辑", "get-started": "开始使用", "methodology": "方法论", "newspaper": "报纸", "prompting": "提示工程", "start-from-scratch": "从零开始", "start-with-templates-showcasing-langflows-prompting-rag-and-agent-use-cases": "从展示wiseAgent的提示工程、检索增强生成（RAG）以及智能体用例的模板开始。", "templates": "模板", "use-cases": "用例", "view-text": "查看文本", "and-try-a-different-query": "并尝试不同的查询。", "clear-your-search": "清除您的搜索内容", "no-templates-found": "未找到模板。", "search": "搜索...", "options": "选项", "rename": "重命名", "upload": "上传", "upload-a-flow": "上传一个流程", "edit-details": "编辑详情", "flowdata-name-exported-successfully": "{{name}} 已成功导出", "an-alternative-name-to-run-the-endpoint": "运行该端点的备用名称", "character-limit-reached": "已达到字符限制", "description": "描述", "endpoint-name": "端点名称", "flow-description": "流程描述", "flow-name": "流程名称", "invalid-endpoint-name-use-only-letters-numbers-hyphens-and-underscores": "无效的端点名称。\n请仅使用字母、数字、连字符和下划线（最多 {{maxlength}} 个字符）。", "name": "姓名", "name-invalid-or-already-exists": "名称无效或已存在", "no-description": "无描述", "option": "（可选）", "flowname": "流程名称", "details": "详细信息", "dismiss": "关闭", "changes-saved-successfully-0": "更改已成功保存", "components-reloaded-successfully": "组件已成功重新加载", "edit-details-0": "编辑详细信息", "enable-auto-saving": "启用自动保存功能", "error-updating-flow-name": "更新流程名称时出错", "export": "导出", "flow-name-updated-successfully": "流程名称已成功更新", "import": "导入", "invalid-flow-name": "无效的流程名称", "name-already-exists": "名称已存在", "refresh-all": "全部刷新", "saved": "已保存", "saved-successfully": "已成功保存", "stop": "停止", "to-avoid-losing-progress": "以免丢失进度。", "component": "组件", "flow": "流程", "newflow-is_component-component-flow-duplicated-successfully": "{{type}} 已成功复制", "are-you-sure-you-want-to-delete-the-selected": "您确定要删除所选的", "cancel": "取消", "note-this-action-is-irreversible": "请注意：此操作不可逆。", "deleting-the-selected-flow-will-remove-all-associated-messages": "删除所选流程将移除所有相关联的消息。", "edited": "已编辑", "error-deleting-items": "删除项目时出错", "please-try-again": "请重试", "selected-items-deleted-successfully": "已成功删除所选项目", "edited-time-ago": "在 {{time}} 前编辑过", "day": "天", "days": "天", "hour": "小时", "hours": "小时", "less-than-a-minute": "不到一分钟", "minute": "分钟", "minutes": "分钟", "month": "月", "months": "月", "year": "年", "years": "年", "flow-saved-successfully": "流程已成功保存！", "search-components": "搜索组件", "search-flows": "搜索流程", "search-flows-and-components": "搜索流程和组件", "deleting-the-selected-folder-will-remove-all-associated-flows-and-components": "删除所选文件夹将移除所有相关的流程和组件。", "import-from": "从...导入", "my-files-0": "我的文件。", "no-files-found-try-again": "未找到文件，请重试", "or-visit": "或访问", "please-select-at-least-one-file": "请至少选择一个文件", "search-files": "搜索文件...", "try-again": "再试一次？", "upload-failed": "上传失败，", "upload-or-import-files": "上传或导入文件，", "are-you-sure-you-want-to-islocal-delete-remove-file-name": "您确定要 {{action}} {{name}} 吗？", "delete-file": "删除文件", "remove": "移除", "remove-file": "移除文件", "the-file-has-been-deleted-successfully": "该文件已成功删除", "this-action-cannot-be-undone-the-file-will-be-permanently-deleted": "此操作无法撤销，\n该文件将被永久删除。", "this-will-remove-the-file-from-your-list-you-can-add-it-back-later-if-needed": "这将从您的列表中移除该文件，\n如有需要，您可以稍后再次添加。", "an-error-occurred-while-uploading-the-file": "上传文件时发生错误", "click-or-drag-files-here": "在此处点击或拖动文件", "drop-files-here": "在此处放置文件", "file-filesids-length-greater-than-1-s-uploaded-successfully": "文件已成功上传", "upload-file": "上传文件", "upload-files-or-import-from-your-preferred-cloud": "上传文件或从您偏好的云存储中导入文件。", "chains": "链条", "data": "数据", "drop-file-to-upload": "放下文件以进行上传", "embeddings": "嵌入模型", "helpers": "辅助工具", "link-extractors": "链接提取器", "loaders": "加载器", "logic": "逻辑层", "memories": "记忆", "models": "AI模型", "output-parsers": "输出解析器", "processing": "数据处理", "prompts": "提示词", "prototypes": "原型", "retrievers": "检索器", "text-splitters": "文本拆分器", "toolkits": "工具包", "tools": "工具集", "vector-stores": "向量存储", "exit-anyway": "无论如何退出", "has-unsaved-changes": "存在未保存的更改", "never": "从不", "save-and-exit": "保存并退出", "saving-your-changes": "正在保存您的更改...", "unsaved-changes-will-be-permanently-lost": "未保存的更改将永久丢失。", "account-settings": "账户设置", "active-to-share-a-public-version-of-this-playground": "积极分享这个练习场的公共版本", "add-a-chat-input-or-chat-output-to-access-your-flow": "添加聊天输入或聊天输出以访问您的流程", "add-a-chat-input-or-chat-output-to-use-the-playground": "添加聊天输入或聊天输出以使用练习场", "api-access": "API 访问权限", "auto-saving-is-disabled": "自动保存功能已禁用", "choose-an-option": "选择一个选项...", "downloaded": "已下载", "downloads": "下载内容", "drop-your-file-here": "在此处放下您的文件", "embed-into-site": "嵌入到网站中", "error-getting-flow-data": "获取流程数据时出错。", "error-installing-the-name": "安装 {{name}} 时出错", "error-isstore-downloading-installing-the-name": "{{action}} {{name}} 时出错", "error-liking-name": "给 {{name}} 点赞时出错。", "failed-to-save-flow": "无法保存流程", "flows-variable-undefined": "流程变量未定义", "github-issues": "G<PERSON><PERSON>问题", "go-to-next-page": "转到下一页", "go-to-previous-page": "转到上一页", "install-locally": "本地安装", "installed": "已安装", "like": "点赞", "likes": "点赞数", "logout": "注销账户", "logs": "日志记录", "name-installed-successfully": "{{name}} 已成功安装。", "name-isstore-downloaded-installed-successfully": "{{name}} 已成功 {{action}}。", "new": "新建", "notifications-and-errors": "通知和错误信息", "page": "页面", "please-report-errors-with-detailed-tracebacks-on-the": "请报告错误，在", "please-review-your-api-key": "请检查您的 API 密钥。", "private": "私人的", "publish": "发布", "report-on-github": "在Github上报告", "restart-langflow": "重新启动wiseAgent", "share": "分享", "shareable-playground": "可共享的练习场", "sorry-we-found-an-unexpected-error": "抱歉，我们发现了一个意外的错误！", "store-api-key-required": "存储数据需要 API 密钥", "successfully": "成功。", "thank-you": "感谢您！", "there-was-an-error-downloading-your-image": "下载您的图像时出现错误", "this-flow-doesnt-have-a-playground": "该流程没有对应的练习场。", "toynrick": "玩具积木", "add-a-new-row": "添加新行", "add-more": "添加更多", "add-new-variable": "添加新变量", "add-or-edit-data": "添加或编辑数据", "alert": "警告提示", "and-components": "和组件", "button": "按钮", "cannot-change-visibility-of-connected-handles": "无法更改已连接手柄的可见性", "cant-connect-to-the-same-node": "无法连接到同一个节点", "change-visibility-of-the-field": "更改字段的可见性", "click": "点击", "component-output": "组件输出", "connect": "连接", "could-not-load-flows-from-database": "无法从数据库中加载流程", "delete-selected-items": "删除所选项目", "drag": "拖动", "duplicate-request": "重复请求：", "duplicate-selected-items": "重复选定的项目", "endpoint-url-copied": "端点URL已复制", "failed-to-start-polling": "未能启动轮询操作", "float-number": "浮点数", "hide-output": "隐藏输出", "incompatible-with": "与之不兼容", "index-index-is-out-of-bounds-for-array-of-length-result-length": "索引 {{index}} 超出了长度为 {{len}} 的数组范围", "input": "输入", "input-plural-type-plural": "输入类型", "inspect-output": "检查输出", "inspect-the-output-of-the-component-below": "检查以下组件的输出内容。", "integer-number": "整数", "invalid-array-index": "无效的数组索引", "invalid-path": "无效的路径", "invalid-property": "无效的属性", "invalid-result": "无效的结果", "loading-options": "加载选项", "looping": "循环", "missing-required-fields": "缺少必填字段", "new-firstword": "新建{{firstWord}}", "no-options-found": "未找到选项", "no-output": "无输出", "no-parameters-are-available-for-display": "没有可供显示的参数。", "no-values-found": "未找到值。", "open": "打开", "open-table": "打开表格", "output": "输出", "output-cant-be-displayed": "无法显示输出内容", "output-plural-type-plural": "输出类型", "path-transformquery-led-to-undefined-or-null-value": "路径'{{transformQuery}}'导致未定义或空值", "please-build-the-component-first": "请先构建组件", "property-key-does-not-exist-in-array-items": "属性'{{key}}'在数组项中不存在", "property-key-does-not-exist-in-object": "属性'{{key}}'在对象中不存在", "refresh-list": "刷新列表", "request-already-in-progress": "请求已在处理中", "reset-columns": "重置列设置", "run-component": "运行组件", "search-options": "搜索选项...", "select-an-option": "选择一个选项", "select-file": "选择文件", "select-items-to-delete": "选择要删除的项目", "select-items-to-duplicate": "选择要重复的项目", "show-output": "显示输出", "stop-build": "停止构建", "streaming-is-not-supported": "不支持流式传输", "table": "表格", "the-filtered-result-contains-values-that-cannot-be-serialized-to-json": "过滤结果包含无法序列化为 JSON 格式的值", "the-filtered-result-must-be-a-json-object-or-array-not-a-primitive-value": "过滤结果必须是 JSON 对象或数组，而不是基本类型值", "to-connect-compatible": "连接兼容的", "to-filter-compatible": "过滤与 {{input}} 兼容的内容", "transform-error": "转换错误", "transform-resulted-in-undefined-value": "转换导致未定义的值", "type-a-float-number": "输入一个浮点数", "type-a-value": "输入一个值...", "type-an-integer-number": "输入一个整数", "type-key": "输入键...", "type-your-prompt-here": "在此处输入您的提示词...", "update-component": "更新组件", "upload-a-file": "上传一个文件...", "use-the-playground-to-interact-with-components-that-stream-data": "使用该练习场与处理流式数据的组件进行交互", "data-id-docs-is-not-available-at-the-moment": "{{id}} 目前不可用。", "double-click-to-start-typing-or-enter-markdown": "双击以开始输入内容或输入 Markdown 格式文本...", "error-in-component-data-node-display_name": "组件 {{name}} 中存在错误", "error-updating-component-code": "更新组件代码时出错", "if-the-error-persists-please-report-it-on-our-discord-or-github": "如果该错误持续存在，请在我们的 Discord 或 GitHub 上报告。", "parameter-not-found-in-the-template": "在模板中未找到参数", "pick-color": "选择颜色", "please-contact-the-developer-of-the-component-to-fix-this-issue": "请联系该组件的开发人员来解决此问题。", "show-more": "显示更多", "template-not-found-in-the-component": "在组件中未找到模板", "the-component-data-node-display_name-has-no-template": "组件 {{name}} 没有可用模板。", "there-was-an-error-updating-the-component": "更新组件时出现错误。", "an-unexpected-error-occurred-please-try-again": "发生意外错误，请重试", "api-access-requires-an-api-key-you-can": "API 访问需要一个 API 密钥，\n您可以", "are-you-sure-you-want-to-exit-without-saving-your-changes": "您确定要在不保存更改的情况下退出吗？", "arrays": "数组[]。", "caution": "警告", "check-and-save": "检查并保存", "check-your-dictionary-format": "检查您的字典格式", "close": "关闭", "could-not-create-flow": "无法创建流程", "create-an-api-key": "创建一个 API 密钥", "customize-your-dictionary-adding-or-editing-key-value-pairs-as-needed-supports-adding-new": "根据需要自定义字典，添加或编辑键值对。\n支持添加新的", "discard-changes": "放弃更改", "edit-dictionary": "编辑字典", "edit-prompt": "编辑提示词", "error-getting-dictionary": "获取字典时出错", "field-name": "字段名称", "file-type-not-allowed": "不允许的文件类型", "file-type-not-allowed-allowed-types-types-join": "不允许此文件类型。\n 允许的文件类型为：{{type}}", "flow-not-found": "未找到相应流程", "hello-world": "你好，世界！", "in-settings": "在设置选项中。", "inspect-component-executions": "检查组件的执行情况。", "invalid-flow-data": "流程数据无效", "multiple-files-are-not-allowed": "不允许多个文件同时操作", "objects": "对象", "or": "或者", "prompt-variables": "提示变量：", "prompt-variables-can-be-created-with-any-chosen-name-inside-curly-brackets-e-g": "可以使用大括号 {} 内任意选定的名称来创建提示变量，例如", "show": "显示", "value": "值", "view-dictionary": "查看字典内容", "you-cannot-upload-a-component-as-a-flow-or-vice-versa": "您不能将组件当作流程上传，反之亦然", "add-note": "添加备注", "bundles": "合集", "chat-input-already-added": "聊天输入已添加", "component-settings": "组件设置", "components-with-errors": "存在错误的组件：", "create-new": "新建", "data-id-docs-is-not-available-at-the-moment-0": "{{id}} 目前不可用。", "data-id-saved-successfully": "{{id}} 已成功保存", "data-id-successfully-overridden": "{{id}} 已成功覆盖！", "discover-more": "发现更多", "discover-more-components": "发现更多组件", "experimental": "实验", "freeze-path": "冻结路径", "input-0": "输入", "it-seems-name-already-exists-do-you-want-to-replace-it-with-the-current-or-create-a-new-one": "似乎已存在名为 {{name}} 的项。\n 您想使用当前内容替换它，还是创建一个新的？", "legacy": "过时", "minimization-only-available-for-components-with-one-handle-or-fewer": "仅当组件的手柄数量为一个或更少时，才可用最小化功能。", "new-component-successfully-saved": "新组件已成功保存！", "new-custom-component": "新的自定义组件", "output-0": "输出", "remove-filter": "移除过滤器", "replace": "替换", "search-0": "搜索", "webhook-already-added": "Webhook 已添加", "you-can-not-access-data-id-code": "您无法访问 {{id}} 的代码", "an-unexpected-error-occurred": "发生了意外错误", "an-unexpected-error-occurred-while-building-the-component-please-try-again": "在构建组件时发生了意外错误。\n 请重试。", "build-job-not-found": "未找到构建任务", "created-0": "创建", "displayname-or-or-type-getfieldtitle-template-t-contains-duplicate-keys-with-the-same-values": "{{type}}（{{t}}）中包含具有相同值的重复键。", "displayname-or-or-type-getfieldtitle-template-t-field-must-not-be-empty": "{{type}}（{{t}}）字段不能为空。", "displayname-or-or-type-is-missing-getfieldtitle-template-t": "{{type}}缺少{{t}}。", "drop-files-to-upload": "放置文件以进行上传", "error-building-component": "构建组件时出错", "error-building-flow": "构建流程时出错", "error-in-parser-parser": "解析器 {{parser}} 出现错误", "error-polling-build-events": "轮询构建事件时出错", "error-processing-build-events": "处理构建事件时出错", "error-starting-build-process": "启动构建过程时出错", "error-updating-components": "更新组件时出错", "expand": "展开", "import-from-json": "从 JSON 文件导入", "invalid-components": "无效的组件", "is_component-component-flow-uploaded-successfully": "{{type}} 已成功上传", "key": "密钥", "last-used": "上次使用时间", "len-component-is-ready-to-update": "{{len}} 个组件已准备好进行更新", "modified": "已修改的", "network-error-please-check-the-connection-to-the-server": "网络错误。\n 请检查与服务器的连接。", "no-components-found-in-the-flow-please-add-at-least-one-component-to-the-flow": "在该流程中未找到任何组件。\n请至少向流程中添加一个组件。", "no-files": "没有文件", "no-items-selected": "未选择任何项目", "please-select-items-to-delete": "请选择要删除的项目", "please-select-more-than-one-component": "请选择多个组件", "ready-to-update": "已准备好进行更新", "select-non-input-output-components-only": "仅选择非输入/输出类型的组件", "select-only-connected-components": "仅选择已连接的组件", "select-only-one-component-with-free-outputs": "仅选择一个具有空闲输出端的组件", "some-edges-were-lost-after-updating-the-components-please-review-the-flow-and-reconnect-them": "在更新组件后，部分连接丢失。\n请检查该流程并重新连接它们。", "string-is-blank": "字符串为空", "successfully-updated-updatedcount-component-updatedcount-greater-than-1-s": "已成功更新了 {{updatedCount}} 个组件", "there-was-an-error-updating-the-components": "在更新组件时出现了错误。", "total-uses": "总使用次数", "ungroup": "取消分组", "unknown-error": "未知错误", "untitled-document": "无标题文档", "update-all": "全部更新", "weve-noticed-a-potential-issue-with-a-component-in-the-flow-please-review-it-and-if-necessary-submit-a-bug-report-with-your-exported-flow-file-thank-you-for-your-help": "我们注意到流程中的一个组件可能存在问题。\n请检查该组件，如有必要，请提交带有导出流程文件的错误报告。\n感谢您的帮助！", "fit-to-zoom": "缩放到合适大小", "lock": "锁定", "unlock": "解锁", "zoom-in": "放大", "zoom-out": "缩小", "admin": "管理员", "login": "登录", "password": "密码", "username": "用户名", "dont-have-an-account-and-nbsp": "还没有账户？", "please-enter-your-password": "请输入您的密码", "sign-in": "登录", "please-enter-your-username": "请输入您的用户名", "sign-in-to-langflow": "登录到灵童Agent流程平台", "sign-up": "注册", "active": "是否激活", "add-a-new-user": "添加新用户", "confirm": "确认", "created-at": "创建时间", "delete-user": "删除用户", "edit": "编辑", "id": "编号", "new-user": "新建用户", "no-users-registered": "尚无注册用户", "search-username": "搜索用户名", "superuser": "是否为超级用户", "updated-at": "更新时间", "beta": "测试", "name-0": "名称", "saving": "保存中...", "create-chain-communicate": "创建、链接、沟通。", "unleashing-linguistic-creativity": "释放语言创造力。", "graph-your-way-to-great-conversations": "以图谱绘制畅聊之道。", "the-power-of-language-at-your-fingertips": "语言力量触手可及。", "where-language-meets-logic": "语言与逻辑交汇之处。", "sculpting-language-with-precision": "精雕细琢的语言塑造。", "building-intelligent-interactions": "构建智能交互体验。", "your-passport-to-linguistic-landscapes": "通往语言景观的通行证。", "create-curate-communicate-with-langflow": "用wiseAgent创建、策展、沟通。", "flow-into-the-future-of-language": "融入语言未来的洪流。", "mapping-meaningful-conversations": "绘制有意义对话的蓝图。", "unravel-the-art-of-articulation": "解构精准表达的艺术。", "language-engineering-excellence": "语言工程卓越典范。", "navigate-the-networks-of-conversation": "畅游对话网络世界。", "crafting-conversations-one-node-at-a-time": "逐节点雕琢对话体验。", "the-pinnacle-of-prompt-generation": "提示生成的巅峰之作。", "language-models-mapped-and-mastered": "语言模型的测绘与掌控。", "powerful-prompts-perfectly-positioned": "精准布局的强力提示。", "innovation-in-interaction-with-langflow": "wiseAgent驱动的交互创新。", "your-toolkit-for-text-generation": "文本生成的终极工具箱。", "unfolding-linguistic-possibilities": "展开语言的无限可能。", "building-powerful-solutions-with-language-models": "基于语言模型构建强力解决方案。", "uncover-business-opportunities-with-nlp": "用自然语言处理发掘商业机遇。", "harness-the-power-of-conversational-ai": "驾驭对话式AI的强大动能。", "transform-your-business-with-smart-dialogues": "以智能对话重塑商业格局。", "craft-meaningful-interactions-generate-value": "塑造有意义交互，创造商业价值。", "unleashing-business-potential-through-language-engineering": "通过语言工程释放商业潜能。", "empowering-enterprises-with-intelligent-interactions": "以智能交互赋能企业转型。", "driving-innovation-in-business-communication": "推动商业沟通领域的革新。", "catalyzing-business-growth-through-conversational-ai": "借力对话式AI催化业务增长。", "text-generation-meets-business-transformation": "文本生成与商业变革的完美融合。", "navigate-the-linguistic-landscape-discover-opportunities": "探索语言版图，发现商业机遇。", "empowering-communication-enabling-opportunities": "强化沟通能力，开启机遇之门。", "create-powerful-connections-boost-business-value": "建立强力连接，提升商业价值。", "advanced-nlp-for-groundbreaking-business-solutions": "突破性商业解决方案的先进自然语言处理技术。", "innovation-in-interaction-revolution-in-revenue": "交互创新引领收益革命。", "maximize-impact-with-intelligent-conversations": "通过智能对话最大化商业影响力。", "beyond-text-generation-unleashing-business-opportunities": "超越文本生成，释放商业机遇。", "unlock-the-power-of-ai-in-your-business-conversations": "解锁商业对话中的AI潜能。", "crafting-dialogues-that-drive-business-success": "打造驱动商业成功的对话系统。", "engineered-for-excellence-built-for-business": "为卓越而设计，为商业而构建。", "chain-the-words-master-language": "串联字词，掌握语言！", "language-architect-at-work": "语言架构师正在工作！", "empowering-language-engineering": "赋能语言工程。", "craft-language-connections-here": "在此精心构建语言联系。", "create-connect-converse": "创造、连接、交谈。", "smart-chains-smarter-conversations": "智能链条，更智能的对话。", "bridging-prompts-for-brilliance": "搭建通向卓越的提示桥梁。", "language-models-unleashed": "释放语言模型的力量。", "promptly-ingenious": "巧妙的提示！", "your-hub-for-text-generation": "你的文本生成中心。", "building-linguistic-labyrinths": "构建语言迷宫。", "langflow-create-chain-communicate": "wiseAgent：创建、链接、沟通。", "connect-the-dots-craft-language": "连点成线，精心打造语言。", "interactive-language-weaving": "交互式的语言编织。", "generate-innovate-communicate": "生成、创新、沟通。", "conversation-catalyst-engine": "对话催化引擎。", "language-chainlink-master": "语言链环大师。", "design-dialogues-with-langflow": "使用 wiseAgent 设计对话。", "nurture-nlp-nodes-here": "在此培育自然语言处理节点。", "conversational-cartography-unlocked": "解锁对话绘制图谱的奥秘。", "design-develop-dialogize": "设计、开发、对话化。", "build-stopped": "停止构建", "editing-messages-will-update-the-memory-but-wont-restart-the-conversation": "编辑消息后将更新记忆但不会重启对话", "store": "商店", "go-to-langflow-store": "前往wiseAgent商店", "sender": "发送者", "timestamp": "时间", "sender_name": "发送者名称", "session_id": "会话ID", "text": "文本", "files": "文件", "properties": "属性", "category": "类别", "content_blocks": "内容", "Value": "值", "type-1": "类型*", "name-1": "变量名称*", "value-1": "值*", "sign-up-for-langflow": "注册到wiseAgent", "please-enter-a-password": "请输入密码", "confirm-your-password": "确认你的密码", "please-confirm-your-password": "请确认你的密码", "sign-up-0": "注册", "already-have-an-account-and-nbsp": "已经有一个账户", "confirm-password-0": "确认密码", "attention": "注意", "are-you-completely-confident-about-the-changes-you-are-making-to-this-user": "您对正在对该用户所做的更改完全有把握吗？", "are-you-sure-you-want-to-delete-this-user-this-action-cannot-be-undone": "您确定要删除该用户吗？此操作无法撤销。", "type-your-new-password-and-confirm-it": "输入您的新密码并进行确认。", "confirm-password": "确认密码", "messages-deleted-successfully": "消息已成功删除", "error-deleting-messages": "删除消息时出错", "messages-updated-successfully": "消息已成功更新", "error-updating-messages": "更新消息时出错", "bot": "机器人", "user": "用户", "machine": "机器", "new-chat": "新聊天", "test-your-flow-with-a-chat-prompt": "使用聊天提示来测试你的流程", "send": "发送", "run-flow": "运行流程", "add-a": "添加一个", "chat-input": "聊天输入", "component-to-your-flow-to-send-messages": "将组件添加到你的流程中以发送消息。", "drop-here": "在此处放置", "attach-image-png-jpg-jpeg": "附加文件", "copy-code": "复制代码", "enter-file-name": "输入文件名", "copied": "已复制！", "flow-running": "流程正在运行...", "an-error-occured-in-the": "一个错误发生在", "component-stopping-your-flow-see-below-for-more-details": "组件导致你的流程停止。 更多详细信息请见下文。", "error-details": "错误详情：", "steps-to-fix": "修复步骤：", "check-the-component-settings": "检查组件设置", "field": "字段：", "ensure-all-required-fields-are-filled": "确保所有必填字段都已填写", "re-run-your-flow": "重新运行你的流程", "edit-message": "编辑消息", "copy-message": "复制消息", "helpful": "有帮助的", "not-helpful": "没有帮助的", "error-on-streaming": "流式传输时出错", "streaming-failed": "流式传输失败", "default-session": "默认会话", "new-chat-0": "新聊天", "chat": "聊天", "session-deleted-successfully": "会话已成功删除。", "error-deleting-session": "删除会话时出错。", "hide-sidebar": "隐藏侧边栏", "theme": "主题", "built-with-langflow": "使用 wiseAgent 构建", "retry": "重试", "Playground": "练习场", "dialog": "对话框", "select-files": "选择文件", "select-prefix-formattedname": "请选择", "edit-totitlecase-name": "编辑", "running_components": "组件运行中", "you-can-only-have-one-chat-input-component-in-a-flow": "在一个流程中，你只能有一个聊天输入组件。", "built_successfully": "构建成功", "basic-prompting": "基础提示工程", "vector-store-rag": "向量存储检索增强生成（RAG）", "simple-agent": "简易智能体", "Size": "大小", "Agents": "智能体", "message-logs": "消息日志", "session": "会话", "en-US": "zh-cn", "folder": "文件夹", "variable": "变量", "duration": "用时:", "mcp": "MCP", "websearch": "Web检索", "Functionality": "功能", "Keyboard Shortcut": "键盘快捷键", "flow_id": "流程ID", "api-key-error-0": "API 密钥错误：", "please-check-your-api-key-and-try-again": "请检查您的 API 密钥，然后重试。", "api-key-not-valid": "API 密钥无效。", "cancellation-failed-no-active-response-found": "取消失败：未找到有效的响应。", "voice-settings": "语音设置", "voice-chat-is-powered-by-openai-you-can-also-add-more-voices-with-elevenlabs": "语音聊天由 OpenAI 提供支持。您也可以使用 ElevenLabs 添加更多语音。", "openai-api-key": "OpenAI API 密钥", "openai-api-key-is-required-to-use-the-voice-assistant": "使用语音助手需要 OpenAI API 密钥。", "enter-your-openai-api-key": "输入您的 OpenAI API 密钥", "elevenlabs-api-key": "ElevenLabs API 密钥", "if-you-have-an-elevenlabs-api-key-you-can-select-elevenlabs-voices": "如果您有 ElevenLabs API 密钥，您可以选择 ElevenLabs 的语音。", "enter-your-elevenlabs-api-key": "输入您的 ElevenLabs API 密钥", "audio-input": "音频输入", "select-which-microphone-to-use-for-voice-input": "选择用于语音输入的麦克风", "select-microphone": "选择麦克风", "no-microphones-found": "未找到麦克风", "microphone": "麦克风", "voice": "语音", "you-can-select-elevenlabs-voices-if-you-have-an-elevenlabs-api-key-otherwise-you-can-only-select-openai-voices": "如果您有 ElevenLabs API 密钥，您可以选择 ElevenLabs 的语音。否则，您只能选择 OpenAI 的语音。", "select": "选择", "preferred-language": "首选语言", "select-the-language-for-speech-recognition": "选择用于语音识别的语言", "select-language": "选择语言", "create-api-key": "创建 API 密钥", "create-a-secret-api-key-to-use-langflow-api": "创建一个私密的 API 密钥以使用 wiseAgent API。", "my-api-key": "我的 API 密钥", "generate-api-key": "生成 API 密钥", "please-save-this-secret-key-somewhere-safe-and-accessible-for-security-reasons": "请将此私密密钥保存在安全且可访问的地方。出于安全考虑，", "you-wont-be-able-to-view-it-again": "你将无法再次查看它", "through-your-account-if-you-lose-this-secret-key-youll-need-to-generate-a-new-one": "。如果你丢失此私密密钥，你将需要生成一个新的密钥。", "optional": "可选的", "store-api-key": "存储 API 密钥", "insert-your-api-key": "插入你的 API 密钥", "please-enter-your-api-key": "请输入你的 API 密钥", "manage-access-to-the-langflow-store": "管理对 wiseAgent 商店的访问权限。", "unmute": "取消静音", "mute": "静音", "no-components-found": "未找到组件。", "or-filter-and-try-a-different-query": "或者进行筛选并尝试不同的查询。"}