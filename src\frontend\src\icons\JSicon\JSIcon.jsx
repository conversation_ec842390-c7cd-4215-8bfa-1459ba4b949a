const SvgJSIcon = (props) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
    filter={props.isdark === "true" ? "invert(100%)" : "invert(0%)"}
  >
    <g id="Frame" clipPath="url(#clip0_2046_939)">
      <path id="Vector" d="M16 0H0V16H16V0Z" fill="black" />
      <path
        id="Vector_2"
        d="M10.7479 12.5001C11.0702 13.0263 11.4895 13.4131 12.2311 13.4131C12.854 13.4131 13.252 13.1017 13.252 12.6715C13.252 12.1559 12.8431 11.9733 12.1574 11.6734L11.7815 11.5121C10.6966 11.0499 9.97582 10.4709 9.97582 9.24674C9.97582 8.11912 10.835 7.26071 12.1777 7.26071C13.1337 7.26071 13.8209 7.59341 14.3161 8.46452L13.1453 9.21627C12.8876 8.75405 12.6095 8.57195 12.1777 8.57195C11.7373 8.57195 11.4582 8.85131 11.4582 9.21627C11.4582 9.66731 11.7376 9.84992 12.3827 10.1293L12.7585 10.2903C14.036 10.8381 14.7573 11.3966 14.7573 12.6522C14.7573 14.0059 13.6939 14.7474 12.2658 14.7474C10.8695 14.7474 9.96743 14.082 9.52604 13.2099L10.7479 12.5001ZM5.43664 12.6304C5.67283 13.0494 5.88769 13.4037 6.40426 13.4037C6.89823 13.4037 7.20985 13.2104 7.20985 12.4589V7.34655H8.71334V12.4793C8.71334 14.0361 7.80058 14.7446 6.46826 14.7446C5.26445 14.7446 4.56731 14.1217 4.21277 13.3713L5.43664 12.6304Z"
        fill="white"
      />
    </g>
    <defs>
      <clipPath id="clip0_2046_939">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgJSIcon;
