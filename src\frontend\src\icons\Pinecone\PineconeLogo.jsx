const SvgPineconeLogo = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="none"
    viewBox="0 0 32 35"
    {...props}
  >
    <path
      fill={props.color}
      d="M13.855 34.296c1.077 0 1.95-.85 1.95-1.9 0-1.05-.873-1.901-1.95-1.901-1.076 0-1.95.85-1.95 1.9 0 1.05.874 1.901 1.95 1.901Z"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.118}
      d="m18.414 7.197.837-4.537"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.118}
      d="m22.266 5.585-2.92-3.474-3.971 2.262"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.118}
      d="m14.92 26.553.814-4.536"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.118}
      d="m18.773 24.93-2.943-3.463-3.96 2.274"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.118}
      d="m16.608 17.2.813-4.537"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.118}
      d="m20.459 15.58-2.931-3.452-3.96 2.262"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.01}
      d="m8.329 26.155-3.577 2.426"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.01}
      d="M8.544 30.087 4.32 28.873l.31-4.28"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.01}
      d="m21.321 28.43 2.489 3.498"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.01}
      d="m19.718 32.045 4.39.291 1.245-4.092"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.058}
      d="m25.4 21.33 4.378.77"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.058}
      d="m26.907 25.072 3.398-2.88-2.142-3.836"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.058}
      d="m24.12 12.861 3.9-2.098"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.058}
      d="m24.336 8.84 4.15 1.679-.777 4.303"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.058}
      d="m6.916 18.157-4.39-.747"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.058}
      d="M4.177 21.165 2 17.328l3.362-2.892"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeWidth={2.058}
      d="M11.08 10.613 8.149 7.348"
    />
    <path
      stroke={props.color}
      strokeLinecap="square"
      strokeLinejoin="round"
      strokeWidth={2.058}
      d="m12.29 6.775-4.487.187-.79 4.303"
    />
  </svg>
);
export default SvgPineconeLogo;
