const SvgPowerPoint = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlSpace="preserve"
    viewBox="0 0 1919.95 1786"
    width="1em"
    height="1em"
    {...props}
  >
    <path
      fill="#ED6C47"
      d="M1160.9 982.3 1026.95 0h-10.002C529.872 1.422 135.372 395.922 133.95 882.998V893l1026.95 89.3z"
    />
    <path
      fill="#FF8F6B"
      d="M1036.952 0h-10.002v893l446.5 178.6 446.5-178.6v-10.002C1918.528 395.922 1524.028 1.422 1036.952 0z"
    />
    <path
      fill="#D35230"
      d="M1919.95 893v9.823c-1.398 487.185-395.992 881.779-883.177 883.177h-19.646c-487.185-1.398-881.779-395.992-883.177-883.177V893h1786z"
    />
    <path
      d="M1071.6 438.909v952.831c-.222 33.109-20.286 62.852-50.901 75.458a79.127 79.127 0 0 1-30.809 6.251H344.698c-12.502-14.288-24.557-29.469-35.72-44.65A875.768 875.768 0 0 1 133.95 902.822v-19.646a873.128 873.128 0 0 1 143.773-481.327c9.823-15.181 20.092-30.362 31.255-44.65H989.89c44.986.341 81.37 36.725 81.71 81.71z"
      opacity={0.1}
    />
    <path
      d="M1026.95 483.56v952.831a79.122 79.122 0 0 1-6.251 30.808c-12.606 30.615-42.35 50.679-75.459 50.901H385.329a763.717 763.717 0 0 1-40.632-44.65c-12.502-14.288-24.557-29.469-35.72-44.65a875.77 875.77 0 0 1-175.028-525.977v-19.646A873.128 873.128 0 0 1 277.722 401.85H945.24c44.986.34 81.37 36.724 81.71 81.71z"
      opacity={0.2}
    />
    <path
      d="M1026.95 483.56v863.531c-.34 44.985-36.724 81.369-81.709 81.71H308.978A875.77 875.77 0 0 1 133.95 902.824v-19.646a873.128 873.128 0 0 1 143.773-481.327H945.24c44.986.339 81.37 36.723 81.71 81.709z"
      opacity={0.2}
    />
    <path
      d="M982.3 483.56v863.531c-.34 44.985-36.724 81.369-81.709 81.71H308.978A875.77 875.77 0 0 1 133.95 902.824v-19.646a873.128 873.128 0 0 1 143.773-481.327H900.59c44.986.339 81.37 36.723 81.71 81.709z"
      opacity={0.2}
    />
    <linearGradient
      id="PowerPoint_svg__a"
      x1={170.645}
      x2={811.655}
      y1={1450.101}
      y2={339.899}
      gradientTransform="matrix(1 0 0 -1 0 1788)"
      gradientUnits="userSpaceOnUse"
    >
      <stop
        offset={0}
        style={{
          stopColor: "#ca4c28",
        }}
      />
      <stop
        offset={0.5}
        style={{
          stopColor: "#c5401e",
        }}
      />
      <stop
        offset={1}
        style={{
          stopColor: "#b62f14",
        }}
      />
    </linearGradient>
    <path
      fill="url(#PowerPoint_svg__a)"
      d="M81.843 401.85h818.613c45.201 0 81.843 36.643 81.843 81.843v818.613c0 45.201-36.643 81.844-81.843 81.844H81.843c-45.2 0-81.843-36.643-81.843-81.843V483.693c0-45.2 36.643-81.843 81.843-81.843z"
    />
    <path
      fill="#FFF"
      d="M500.08 620.144a224.99 224.99 0 0 1 149.042 43.668 156.272 156.272 0 0 1 51.883 126.493 176.015 176.015 0 0 1-25.584 94.524 170.963 170.963 0 0 1-72.646 64.207 246.66 246.66 0 0 1-109.259 22.95H389.973v192.441H283.929V620.144H500.08zM389.884 888.848h91.265a118.501 118.501 0 0 0 80.683-24.066 89.3 89.3 0 0 0 27.281-70.413c0-59.98-34.857-89.97-104.57-89.97h-94.658v184.449z"
    />
  </svg>
);
export default SvgPowerPoint;
