const Svg<PERSON><PERSON>baNovaLogo = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    aria-label="sambanova logo"
    role="img"
    fill="none"
    viewBox="0 0 37 37"
    width="48"
    height="48"
    className="fill-foreground"
    {...props}
  >
    <path
      fill="#EE7624"
      d="M36.0662 36.4941H34.0625V11.6579C34.0652 9.11692 33.0744 6.67807 31.3063 4.87376C29.5381 3.06944 27.1363 2.04623 24.6253 2.02749H11.441C8.96465 2.02749 6.58971 3.02289 4.83866 4.79472C3.0876 6.56654 2.10386 8.96965 2.10386 11.4754C2.10386 13.9811 3.0876 16.3843 4.83866 18.1561C6.58971 19.9279 8.96465 20.9233 11.441 20.9233H12.6933C13.2616 20.8414 13.8406 20.884 14.3912 21.0481C14.9419 21.2122 15.4512 21.4942 15.8848 21.8749C16.3183 22.2555 16.666 22.726 16.9044 23.2543C17.1427 23.7827 17.266 24.3568 17.266 24.9376C17.266 25.5184 17.1427 26.0925 16.9044 26.6209C16.666 27.1494 16.3183 27.6198 15.8848 28.0005C15.4512 28.3811 14.9419 28.663 14.3912 28.8272C13.8406 28.9914 13.2616 29.0339 12.6933 28.952H0V26.9245H12.7234C12.9896 26.9439 13.257 26.9076 13.5088 26.8178C13.7606 26.728 13.9914 26.5867 14.1869 26.4027C14.3824 26.2186 14.5383 25.9957 14.6449 25.7481C14.7513 25.5004 14.8064 25.2331 14.8064 24.963C14.8064 24.6929 14.7513 24.4256 14.6449 24.1779C14.5383 23.9303 14.3824 23.7074 14.1869 23.5233C13.9914 23.3393 13.7606 23.198 13.5088 23.1082C13.257 23.0184 12.9896 22.9821 12.7234 23.0015H11.4711C8.44283 23.0122 5.5341 21.8067 3.38343 19.6495C1.23276 17.4924 0.0159055 14.5598 0 11.4957C0.0159045 8.43599 1.23033 5.50756 3.37695 3.35257C5.52358 1.19759 8.42719 -0.00805354 11.451 4.04923e-05H24.6453C27.6819 0.032044 30.5836 1.27375 32.7215 3.45598C34.8594 5.63822 36.0611 8.58511 36.0662 11.6579V36.4941Z"
    />
    <path
      fill="#EE7624"
      d="M32.2893 36.4943H30.2856V11.658C30.2886 10.1366 29.7014 8.67471 28.6505 7.58663C27.5995 6.49854 26.1686 5.87114 24.6653 5.83928H11.441C9.95838 5.83928 8.53648 6.43524 7.4881 7.49605C6.43972 8.55686 5.85075 9.99561 5.85075 11.4959C5.85075 12.9961 6.43972 14.4348 7.4881 15.4956C8.53648 16.5564 9.95838 17.1525 11.441 17.1525H11.5512L12.7735 17.2842C14.7928 17.2923 16.7263 18.1117 18.1486 19.5623C19.5708 21.0128 20.3654 22.9756 20.3574 25.019C20.3494 27.0622 19.5396 29.0186 18.106 30.4578C16.6725 31.897 14.7327 32.7009 12.7134 32.6928H0V30.6654H12.7234C14.206 30.6654 15.628 30.0694 16.6763 29.0086C17.7246 27.9478 18.3136 26.509 18.3136 25.0088C18.3136 23.5086 17.7246 22.0698 16.6763 21.009C15.628 19.9482 14.206 19.3522 12.7234 19.3522H12.6132L11.401 19.2205C10.401 19.2205 9.41097 19.0211 8.4872 18.634C7.56343 18.2468 6.72407 17.6793 6.01704 16.9639C5.31002 16.2485 4.74918 15.3991 4.36653 14.4645C3.9839 13.5297 3.78696 12.5279 3.78696 11.5161C3.78696 10.5044 3.9839 9.50252 4.36653 8.56782C4.74918 7.6331 5.31002 6.78377 6.01704 6.06837C6.72407 5.35296 7.56343 4.78546 8.4872 4.39829C9.41097 4.01111 10.401 3.81183 11.401 3.81183H24.6453C26.6845 3.8464 28.6286 4.69043 30.0586 6.16195C31.4885 7.63347 32.2896 9.6146 32.2893 11.6784V36.4943Z"
    />
    <path
      fill="#EE7624"
      d="M12.7234 36.4941H0V34.4666H12.7234C15.1998 34.4666 17.5747 33.4712 19.3257 31.6994C21.0767 29.9276 22.0605 27.5245 22.0605 25.0187C22.0605 22.513 21.0767 20.1099 19.3257 18.3381C17.5747 16.5663 15.1998 15.5708 12.7234 15.5708H11.4711C10.9028 15.6528 10.3237 15.6102 9.77312 15.4461C9.22251 15.2819 8.71317 14.9999 8.27959 14.6193C7.84601 14.2387 7.49831 13.7682 7.26001 13.2398C7.02172 12.7114 6.8984 12.1373 6.8984 11.5565C6.8984 10.9757 7.02172 10.4016 7.26001 9.87315C7.49831 9.34481 7.84601 8.87431 8.27959 8.49367C8.71317 8.11303 9.22251 7.83109 9.77312 7.66692C10.3237 7.50276 10.9028 7.4602 11.4711 7.54214H24.6453C25.7198 7.57125 26.7406 8.02441 27.4894 8.80481C28.2381 9.58524 28.6556 10.6309 28.6527 11.7187V36.4941H26.649V11.6579C26.6551 11.1199 26.4569 10.6001 26.0952 10.2058C25.7336 9.81162 25.2359 9.57292 24.7054 9.53922H11.441C11.1747 9.51977 10.9073 9.55616 10.6555 9.6459C10.4037 9.73574 10.1729 9.87709 9.97744 10.0611C9.78207 10.2451 9.62616 10.4679 9.51958 10.7157C9.413 10.9633 9.358 11.2306 9.358 11.5007C9.358 11.7709 9.413 12.0381 9.51958 12.2859C9.62616 12.5335 9.78207 12.7563 9.97744 12.9403C10.1729 13.1244 10.4037 13.2658 10.6555 13.3555C10.9073 13.4454 11.1747 13.4817 11.441 13.4623H12.6933C15.7117 13.4583 18.6081 14.6677 20.7452 16.8244C22.8824 18.9813 24.0853 21.9088 24.0892 24.9629C24.0932 28.0172 22.898 30.9479 20.7665 33.1104C18.635 35.2729 15.7418 36.4901 12.7234 36.4941Z"
    />
  </svg>
);
export default SvgSambaNovaLogo;
