const Tavily = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    viewBox="0 0 375 375"
    {...props}
  >
    <defs>
      <clipPath id="a">
        <path d="M109.379 231.133h37.105v37.105H109.38Zm0 0" />
      </clipPath>
      <clipPath id="b">
        <path d="M127.934 231.133c-10.246 0-18.555 8.304-18.555 18.554 0 10.247 8.308 18.551 18.555 18.551 10.246 0 18.55-8.304 18.55-18.55 0-10.25-8.304-18.555-18.55-18.555Zm0 0" />
      </clipPath>
    </defs>
    <path
      fill="none"
      stroke="#f25022"
      strokeLinecap="round"
      strokeWidth={28.360419999999998}
      d="M127.926 239.96V50.165"
    />
    <path
      fill="none"
      stroke="#f25022"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={28.360419999999998}
      d="m85.387 99.793 42.539-56.719 42.539 56.719"
    />
    <path
      fill="none"
      stroke="#ffb901"
      strokeLinecap="round"
      strokeWidth={28.360419999999998}
      d="m141.012 254.168 172.476-.02"
    />
    <path
      fill="none"
      stroke="#ffb901"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={28.360419999999998}
      d="m263.855 211.613 56.723 42.535-56.715 42.547"
    />
    <path
      fill="none"
      stroke="#04a3ec"
      strokeLinecap="round"
      strokeWidth={23.20398}
      d="m117.852 259.344-57.446 66.015"
    />
    <path
      fill="none"
      stroke="#04a3ec"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={23.20398}
      d="m113.32 317.574-56.718 12.16 4.207-57.859"
    />
    <g clipPath="url(#a)">
      <g clipPath="url(#b)">
        <path fill="#32b37f" d="M109.379 231.133h37.105v37.105H109.38Zm0 0" />
      </g>
    </g>
  </svg>
);
export default Tavily;
