const SvgGoogleGenerativeAI = (props) => (
  <svg
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_6067_405)">
      <rect width="24" height="24" fill="#0ADDF8" />
      <path
        d="M14.2453 4.39688C13.9172 4.57031 13.7672 4.83281 13.7953 5.19375C13.8281 5.61563 14.0625 5.83125 14.6953 6.02344C15.2156 6.18281 15.3516 6.35156 15.1406 6.5625C14.9578 6.74531 14.5312 6.67031 14.2922 6.41719L14.1609 6.27656L13.9219 6.40781C13.7953 6.47813 13.6875 6.55313 13.6875 6.56719C13.6875 6.58125 13.7484 6.67969 13.8234 6.78281C14.0297 7.06875 14.3672 7.21875 14.7984 7.21875C15.4781 7.21875 15.8578 6.90469 15.8578 6.34688C15.8578 5.88281 15.6234 5.65313 14.9391 5.44688C14.4516 5.30156 14.3297 5.18906 14.4516 4.99688C14.4937 4.93594 14.5828 4.875 14.6484 4.85625C14.7984 4.81875 15.0937 4.89375 15.2437 5.01094L15.3562 5.10469L15.5766 4.95469C15.6937 4.87031 15.7969 4.78125 15.7969 4.75313C15.7969 4.72969 15.7406 4.65 15.6703 4.57969C15.3141 4.24219 14.6953 4.1625 14.2453 4.39688Z"
        fill="black"
      />
      <path
        d="M3.99373 5.35312C4.00779 6.3 4.01716 6.41719 4.10623 6.60938C4.29841 7.01719 4.64529 7.21875 5.15623 7.21875C5.66716 7.21875 6.01404 7.01719 6.20623 6.60938C6.29529 6.41719 6.30466 6.3 6.31873 5.35312L6.33748 4.3125H6.03279H5.7281L5.70935 5.25937C5.6906 6.3 5.65779 6.44062 5.41404 6.55312C5.14216 6.67969 4.82341 6.59062 4.69685 6.35625C4.63123 6.23906 4.61716 6.0375 4.6031 5.25937L4.58435 4.3125H4.27966H3.97498L3.99373 5.35312Z"
        fill="black"
      />
      <path
        d="M8.71875 5.74219V7.17188H9.02344H9.32812V6.32812C9.32812 5.86406 9.34219 5.48437 9.36563 5.48437C9.38438 5.48906 9.675 5.86406 10.0078 6.32812L10.6172 7.16719L10.9125 7.17188H11.2031V5.74219V4.3125H10.8984H10.5938V5.15625C10.5938 5.62031 10.5797 6 10.5609 6C10.5375 6 10.2422 5.62031 9.90469 5.15625L9.28125 4.3125H9H8.71875V5.74219Z"
        fill="black"
      />
      <path
        d="M18 4.61719V4.92188H18.4219H18.8438V6.04688V7.17188H19.1484H19.4531V6.04688V4.92188H19.875H20.2969V4.61719V4.3125H19.1484H18V4.61719Z"
        fill="black"
      />
      <path
        d="M14.475 10.7954C13.8047 10.9875 13.4157 11.5829 13.4625 12.3516C13.5141 13.1297 14.0157 13.6266 14.7938 13.6782C15.3938 13.7157 15.9 13.4672 16.1297 13.0266L16.2235 12.8438L16.1157 12.8016C15.6375 12.6282 15.6797 12.6235 15.5204 12.811C15.3375 13.0172 15.1125 13.0969 14.8032 13.0688C14.0438 12.9938 13.8094 11.9438 14.4469 11.4704C14.5219 11.4141 14.6766 11.3672 14.8266 11.3532C15.1313 11.3297 15.3704 11.4282 15.5485 11.6485C15.6704 11.7985 15.6704 11.8032 15.8391 11.7329C15.9329 11.6907 16.0547 11.6438 16.1157 11.6204C16.2141 11.5829 16.2188 11.5735 16.1532 11.4422C15.9329 11.0204 15.5625 10.7907 15.0375 10.7579C14.8266 10.7438 14.611 10.7579 14.475 10.7954Z"
        fill="black"
      />
      <path
        d="M3.98438 12.2062V13.6406H4.3125H4.64062V13.2187V12.7969H4.84219H5.03906L5.26406 13.2187L5.48906 13.6406H5.8125C5.99531 13.6406 6.14062 13.6266 6.14062 13.6125C6.14062 13.5938 6.03281 13.3781 5.89688 13.125L5.65781 12.6703L5.84063 12.4875C6.30938 12.0187 6.20156 11.2453 5.625 10.9219C5.42813 10.8141 5.35781 10.8047 4.70156 10.7906L3.98438 10.7719V12.2062ZM5.39531 11.5266C5.49375 11.6437 5.5125 11.8641 5.4375 12C5.37656 12.1125 5.14688 12.1875 4.87031 12.1875H4.64062V11.7844V11.3812L4.97344 11.4C5.25469 11.4141 5.32031 11.4375 5.39531 11.5266Z"
        fill="black"
      />
      <path
        d="M8.76562 11.7328C8.76562 12.8719 8.8125 13.0875 9.1125 13.3875C9.32812 13.6078 9.54844 13.6875 9.94219 13.6875C10.4484 13.6875 10.7953 13.4859 10.9875 13.0781C11.0766 12.8859 11.0859 12.7687 11.1 11.8219L11.1188 10.7812H10.7859H10.4531V11.7422C10.4531 12.7922 10.4297 12.9187 10.1953 13.0219C10.0078 13.1109 9.73125 13.0922 9.59531 12.9844C9.4125 12.8391 9.375 12.6234 9.375 11.6578V10.7812H9.07031H8.76562V11.7328Z"
        fill="black"
      />
      <path
        d="M18.0469 11.0859V11.3906H18.4688H18.8906V12.5156V13.6406H19.2188H19.5469V12.5156V11.3906H19.9688H20.3906V11.0859V10.7812H19.2188H18.0469V11.0859Z"
        fill="black"
      />
      <path
        d="M3.89062 18.2437C3.89062 18.7921 3.91406 19.3171 3.9375 19.4109C4.01719 19.6968 4.18125 19.889 4.44844 20.025C4.88438 20.25 5.54063 20.1843 5.87813 19.8843C5.925 19.8421 6.01406 19.6968 6.075 19.5703C6.18281 19.3453 6.1875 19.289 6.1875 18.2906V17.25H5.8875H5.58281L5.56875 18.2625L5.55469 19.275L5.4 19.4109C5.22656 19.5703 4.92188 19.5984 4.74375 19.4718C4.52344 19.3171 4.5 19.1953 4.5 18.1875V17.25H4.19531H3.89062V18.2437Z"
        fill="black"
      />
      <path
        d="M8.71875 18.675V20.1094H9.02344H9.32812V19.6828V19.2609L9.54844 19.275L9.77344 19.2891L9.98438 19.6969L10.1953 20.1094H10.5375H10.8797L10.6219 19.6125L10.3641 19.1203L10.5328 18.9703C10.7531 18.7781 10.8281 18.5859 10.8281 18.2203C10.8281 17.8266 10.6594 17.55 10.3313 17.3859C10.125 17.2875 10.0266 17.2734 9.4125 17.2594L8.71875 17.2406V18.675ZM10.0312 17.9297C10.3031 18.0703 10.2516 18.4969 9.95156 18.6141C9.88125 18.6375 9.71719 18.6562 9.59063 18.6469L9.35156 18.6328L9.3375 18.2437L9.32344 17.8594H9.60938C9.77344 17.8594 9.95625 17.8875 10.0312 17.9297Z"
        fill="black"
      />
      <path
        d="M13.9219 18.6797V20.1094H14.8125H15.7031V19.8047V19.5H15.1172H14.5312V19.2422V18.9844H15H15.4688V18.6797V18.375H15H14.5312V18.1172V17.8594H15.1172H15.7031V17.5547V17.25H14.8125H13.9219V18.6797Z"
        fill="black"
      />
      <path
        d="M18 18.6797V20.1188L18.7406 20.1C19.5797 20.0766 19.7859 20.0156 20.0813 19.6828C20.3438 19.3969 20.4047 19.2094 20.4094 18.7031C20.4141 18.1922 20.3109 17.8875 20.0484 17.6344C19.7531 17.3485 19.5094 17.2781 18.7172 17.2594L18 17.2406V18.6797ZM19.4062 17.9625C19.6969 18.1172 19.7906 18.3094 19.7719 18.736C19.7484 19.2844 19.5094 19.5 18.9281 19.5H18.6094V18.6797V17.8594H18.9047C19.1156 17.8594 19.2563 17.8875 19.4062 17.9625Z"
        fill="black"
      />
    </g>
    <defs>
      <clipPath id="clip0_6067_405">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export default SvgGoogleGenerativeAI;
