const ThumbDownFilled = (props) => (
  <svg
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M6.74945 13.59L7.49945 10.5H3.12695C2.89409 10.5 2.66442 10.4458 2.45613 10.3416C2.24785 10.2375 2.06667 10.0863 1.92695 9.9C1.78723 9.71371 1.6928 9.49744 1.65115 9.26833C1.60949 9.03922 1.62175 8.80355 1.68695 8.58L3.43445 2.58C3.52533 2.26843 3.71481 1.99473 3.97445 1.8C4.2341 1.60527 4.5499 1.5 4.87445 1.5H14.9995C15.3973 1.5 15.7788 1.65804 16.0601 1.93934C16.3414 2.22064 16.4995 2.60218 16.4995 3V9C16.4995 9.39782 16.3414 9.77936 16.0601 10.0607C15.7788 10.342 15.3973 10.5 14.9995 10.5H12.9295C12.6504 10.5001 12.3769 10.5781 12.1397 10.7252C11.9026 10.8723 11.7111 11.0826 11.587 11.3325L8.99945 16.5C8.64577 16.4956 8.29765 16.4114 7.9811 16.2536C7.66455 16.0957 7.38776 15.8684 7.1714 15.5886C6.95504 15.3088 6.80472 14.9837 6.73165 14.6376C6.65859 14.2915 6.66467 13.9334 6.74945 13.59Z"
      fill="#FEE2E2"
      stroke="#DC2626"
      stroke-width="1.25"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M12.75 10.5V1.5"
      stroke="#DC2626"
      stroke-width="1.25"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export default ThumbDownFilled;
