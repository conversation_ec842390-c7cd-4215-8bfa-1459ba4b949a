import ReactDOM from "react-dom/client";
import reportWebVitals from "./reportWebVitals";

import "./style/classes.css";
// @ts-ignore
import "./style/index.css";
// @ts-ignore
import "./App.css";
import "./style/applies.css";

// @ts-ignore
import App from "./App";

import { I18nextProvider } from 'react-i18next'
import i18n from './i18n'


const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement,
);

//
// root.render(<App />);
root.render(<I18nextProvider i18n={i18n}>
    <App />
</I18nextProvider>);

reportWebVitals();
