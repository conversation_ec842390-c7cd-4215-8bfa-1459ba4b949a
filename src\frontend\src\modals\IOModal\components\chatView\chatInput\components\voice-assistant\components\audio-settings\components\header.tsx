import React from "react";
import IconComponent from "../../../../../../../../../../components/common/genericIconComponent";
import { ICON_STROKE_WIDTH } from "../../../../../../../../../../constants/constants";
import i18n from "@/i18n";

const AudioSettingsHeader = () => {
  return (
    <div
      className="grid gap-1 p-4"
      data-testid="voice-assistant-settings-modal-header"
    >
      <p className="flex items-center gap-2 text-sm text-primary">
        <IconComponent
          name="Settings"
          strokeWidth={ICON_STROKE_WIDTH}
          className="h-4 w-4 text-muted-foreground hover:text-foreground"
        />
        {i18n.t('voice-settings')}
      </p>
      <p className="text-[13px] leading-4 text-muted-foreground">
        {i18n.t('voice-chat-is-powered-by-openai-you-can-also-add-more-voices-with-elevenlabs')}
      </p>
    </div>
  );
};

export default AudioSettingsHeader;
