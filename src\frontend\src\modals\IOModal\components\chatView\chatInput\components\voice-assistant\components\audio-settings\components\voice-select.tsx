import IconComponent from "../../../../../../../../../../components/common/genericIconComponent";
import ShadTooltip from "../../../../../../../../../../components/common/shadTooltipComponent";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../../../../../../../components/ui/select";
import { toTitleCase } from "../../../../../../../../../../utils/utils";
import i18n from "@/i18n";

interface VoiceSelectProps {
  voice: string;
  handleSetVoice: (value: string) => void;
  allVoices: { value: string; name: string }[];
}

const VoiceSelect = ({
  voice,
  handleSetVoice,
  allVoices,
}: VoiceSelectProps) => {
  return (
    <div className="grid w-full items-center gap-2">
      <span className="flex w-full items-center text-sm">
        {i18n.t('voice')}
        <ShadTooltip content={i18n.t('you-can-select-elevenlabs-voices-if-you-have-an-elevenlabs-api-key-otherwise-you-can-only-select-openai-voices')}>
          <div>
            <IconComponent
              name="Info"
              strokeWidth={2}
              className="relative -top-[3px] left-1 h-[14px] w-[14px] text-placeholder"
            />
          </div>
        </ShadTooltip>
      </span>

      <Select value={voice} onValueChange={handleSetVoice}>
        <SelectTrigger className="h-9 w-full">
          <SelectValue placeholder={i18n.t('select')} />
        </SelectTrigger>
        <SelectContent className="max-h-[200px]">
          <SelectGroup>
            {allVoices?.map((voice, index) => (
              <SelectItem value={voice?.value} key={index}>
                <div className="max-w-[220px] truncate text-left">
                  {toTitleCase(voice?.name)}
                </div>
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default VoiceSelect;
