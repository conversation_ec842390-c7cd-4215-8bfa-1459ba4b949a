import { TextShimmer } from "@/components/ui/TextShimmer";
import LogoIcon from "./chatView/chatMessage/components/chat-logo-icon";
import i18n from "@/i18n";

export default function FlowRunningSqueleton() {
  return (
    <div className="flex w-full gap-4 rounded-md p-2">
      <LogoIcon />
      <div className="flex items-center">
        <div>
          <TextShimmer className="" duration={1}>
            {i18n.t('flow-running')}
          </TextShimmer>
        </div>
      </div>
    </div>
  );
}
