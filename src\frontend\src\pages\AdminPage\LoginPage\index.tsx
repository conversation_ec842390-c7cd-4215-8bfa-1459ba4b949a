import LangflowLogo from "@/assets/LangflowLogo.svg?react";
import Wise<PERSON>gentLogo from "@/assets/wiseAgentLogo.svg?react";
import { useLoginUser } from "@/controllers/API/queries/auth";
import { useContext, useState } from "react";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { SIGNIN_ERROR_ALERT } from "../../../constants/alerts_constants";
import { CONTROL_LOGIN_STATE } from "../../../constants/constants";
import { AuthContext } from "../../../contexts/authContext";
import useAlertStore from "../../../stores/alertStore";
import { LoginType } from "../../../types/api";
import {
  inputHandlerEventType,
  loginInputStateType,
} from "../../../types/components";
import { useTranslation, Trans } from 'react-i18next';

export default function LoginAdminPage() {
  const { t } = useTranslation();
  const [inputState, setInputState] =
    useState<loginInputStateType>(CONTROL_LOGIN_STATE);
  const { login } = useContext(AuthContext);

  const { password, username } = inputState;
  const setErrorData = useAlertStore((state) => state.setErrorData);
  function handleInput({
    target: { name, value },
  }: inputHandlerEventType): void {
    setInputState((prev) => ({ ...prev, [name]: value }));
  }

  const { mutate } = useLoginUser();

  function signIn() {
    const user: LoginType = {
      username: username,
      password: password,
    };

    mutate(user, {
      onSuccess: (res) => {
        login(res.access_token, "login", res.refresh_token);
      },
      onError: (error) => {
        setErrorData({
          title: SIGNIN_ERROR_ALERT,
          list: [error["response"]["data"]["detail"]],
        });
      },
    });
  }

  return (
    <div className="flex h-full w-full flex-col items-center justify-center bg-muted">
      <div className="flex w-72 flex-col items-center justify-center gap-2">
        <WiseAgentLogo title="wiseAgent logo" className="h-10 w-10 scale-[1.5]" />
        <span className="mb-6 text-2xl font-semibold text-primary">{t('admin')}</span>
        <Input
          onChange={({ target: { value } }) => {
            handleInput({ target: { name: "username", value } });
          }}
          className="bg-background"
          placeholder={t('username')}
        />
        <Input
          type="password"
          onChange={({ target: { value } }) => {
            handleInput({ target: { name: "password", value } });
          }}
          className="bg-background"
          placeholder={t('password')}
        />
        <Button
          onClick={() => {
            signIn();
          }}
          variant="default"
          className="w-full"
        >
          {t('login')}
        </Button>
      </div>
    </div>
  );
}
