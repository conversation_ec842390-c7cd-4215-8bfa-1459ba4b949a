import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
} from "@/components/ui/sidebar";
import { memo, useMemo } from "react";
import { SidebarGroupProps } from "../../types";
import { BundleItem } from "../bundleItems";
import { useTranslation } from "react-i18next";

export const MemoizedSidebarGroup = memo(
  ({
    BUNDLES,
    search,
    sortedCategories,
    dataFilter,
    nodeColors,
    onDragStart,
    sensitiveSort,
    openCategories,
    setOpenCategories,
    handleKeyDownInput,
    uniqueInputsComponents,
  }: SidebarGroupProps) => {
    const { t } = useTranslation();
    const sortedBundles = useMemo(() => {
      return BUNDLES
        .slice() // 创建副本，不修改原数组
        .sort((a, b) => {
          const referenceArray = search !== "" ? sortedCategories : BUNDLES;
          const indexA = referenceArray.findIndex((value) => value === a.name);
          const indexB = referenceArray.findIndex((value) => value === b.name);
          return indexA - indexB; // 升序排序：index 小的排前面
        });
    }, [BUNDLES, search, sortedCategories]);

    return (
      <SidebarGroup className="p-3">
        <SidebarGroupLabel>{t('bundles')}</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            {sortedBundles.map((item) => (
              <BundleItem
                key={item.name}
                item={item}
                isOpen={openCategories.includes(item.name)}
                onOpenChange={(isOpen) => {
                  setOpenCategories((prev) =>
                    isOpen
                      ? [...prev, item.name]
                      : prev.filter((cat) => cat !== item.name),
                  );
                }}
                dataFilter={dataFilter}
                nodeColors={nodeColors}
                uniqueInputsComponents={uniqueInputsComponents}
                onDragStart={onDragStart}
                sensitiveSort={sensitiveSort}
                handleKeyDownInput={handleKeyDownInput}
              />
            ))}
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    );
  },
);

MemoizedSidebarGroup.displayName = "MemoizedSidebarGroup";

export default MemoizedSidebarGroup;
