import { UniqueInputsComponents } from "../types";
import i18n from "@/i18n";

export const getDisabledTooltip = (
  SBItemName: string,
  uniqueInputsComponents: UniqueInputsComponents,
) => {
  if (SBItemName === "ChatInput" && uniqueInputsComponents.chatInput) {
    return i18n.t('chat-input-already-added');
  }
  if (SBItemName === "Webhook" && uniqueInputsComponents.webhookInput) {
    return i18n.t('webhook-already-added');
  }
  return "";
};
