import { usePostAddFlow } from "@/controllers/API/queries/flows/use-post-add-flow";
import { useFolderStore } from "@/stores/foldersStore";
import { addVersionToDuplicates, createNewFlow } from "@/utils/reactflowUtils";
import { useParams } from "react-router-dom";
import { useTranslation } from 'react-i18next'

type UseDuplicateFlowsParams = {
  selectedFlowsComponentsCards: string[];
  allFlows: any[];
  setSuccessData: (data: { title: string }) => void;
};

const useDuplicateFlows = ({
  selectedFlowsComponentsCards,
  allFlows,
  setSuccessData,
}: UseDuplicateFlowsParams) => {
  const { t } = useTranslation();
  const { mutateAsync: postAddFlow } = usePostAddFlow();
  const { folderId } = useParams();
  const myCollectionId = useFolderStore((state) => state.myCollectionId);

  const handleDuplicate = async () => {
    selectedFlowsComponentsCards.map(async (selectedFlow) => {
      const currentFlow = allFlows.find((flow) => flow.id === selectedFlow);
      const folder_id = folderId ?? myCollectionId ?? "";

      const flowsToCheckNames = allFlows?.filter(
        (f) => f.folder_id === folder_id,
      );

      const newFlow = createNewFlow(currentFlow.data, folder_id, currentFlow);

      const newName = addVersionToDuplicates(newFlow, flowsToCheckNames ?? []);
      newFlow.name = newName;
      newFlow.folder_id = folder_id;

      await postAddFlow(newFlow);
      setSuccessData({
        title: t('newflow-is_component-component-flow-duplicated-successfully', { type: (newFlow.is_component ? t('component') : t('flow'))}),
      });
    });
  };

  return { handleDuplicate };
};

export default useDuplicateFlows;
