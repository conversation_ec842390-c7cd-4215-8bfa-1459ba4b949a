import { useTranslation } from 'react-i18next';
export const timeElapsed = (dateTimeString: string | undefined): string => {
  if (!dateTimeString) {
    return "";
  }

  const { t } = useTranslation();
  const givenDate = new Date(dateTimeString);
  const now = new Date();

  let diffInMs = Math.abs(now.getTime() - givenDate.getTime());

  const minutes = Math.floor(diffInMs / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const months = Math.floor(days / 30); // Approximate
  const years = Math.floor(months / 12);

  if (years > 0) {
    return years === 1 ? `${years} ${t('year')}` : `${years} ${t('years')}`;
  } else if (months > 0) {
    return months === 1 ? `${months} ${t('month')}` : `${months} ${t('months')}`;
  } else if (days > 0) {
    return days === 1 ? `${days} ${t('day')}` : `${days} ${t('days')}`;
  } else if (hours > 0) {
    return hours === 1 ? `${hours} ${t('hour')}` : `${hours} ${t('hours')}`;
  } else if (minutes > 0) {
    return minutes === 1 ? `${minutes} ${t('minute')}` : `${minutes} ${t('minutes')}`;
  } else {
    return `${t('less-than-a-minute')}`;
  }
};
