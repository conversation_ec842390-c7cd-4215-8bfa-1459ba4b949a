import i18n from "@/i18n";
export const getModalPropsApiKey = () => {
  const modalProps = {
    title: i18n.t('create-api-key'),
    description: i18n.t('create-a-secret-api-key-to-use-langflow-api'),
    inputPlaceholder: i18n.t('my-api-key'),
    buttonText: i18n.t('generate-api-key'),
    generatedKeyMessage: (
      <>
        {" "}
        {i18n.t('please-save-this-secret-key-somewhere-safe-and-accessible-for-security-reasons')} <strong>{i18n.t('you-wont-be-able-to-view-it-again')}</strong> {i18n.t('through-your-account-if-you-lose-this-secret-key-youll-need-to-generate-a-new-one')}
      </>
    ),
    showIcon: true,
    inputLabel: (
      <>
        <span className="text-sm">{i18n.t('description')}</span>{" "}
        <span className="text-xs text-muted-foreground">({i18n.t('optional')})</span>
      </>
    ),
  };

  return modalProps;
};
