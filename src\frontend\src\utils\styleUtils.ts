import { AIMLIcon } from "@/icons/AIML";
import { AWSInvertedIcon } from "@/icons/AWSInverted";
import { BWPythonIcon } from "@/icons/BW python";
import { DropboxIcon } from "@/icons/Dropbox";
import { DuckDuckGoIcon } from "@/icons/DuckDuckGo";
import { ExaIcon } from "@/icons/Exa";
import { GleanIcon } from "@/icons/Glean";
import { GoogleDriveIcon } from "@/icons/GoogleDrive";
import { JSIcon } from "@/icons/JSicon";
import { LangwatchIcon } from "@/icons/Langwatch";
import { MilvusIcon } from "@/icons/Milvus";
import { OneDriveIcon } from "@/icons/OneDrive";
import Perplexity from "@/icons/Perplexity/Perplexity";
import { SearchAPIIcon } from "@/icons/SearchAPI";
import { SerpSearchIcon } from "@/icons/SerpSearch";
import { TavilyIcon } from "@/icons/Tavily";
import { UnstructuredIcon } from "@/icons/Unstructured";
import { WikipediaIcon } from "@/icons/Wikipedia";
import YouTubeIcon from "@/icons/Youtube/youtube";
import { ZepMemoryIcon } from "@/icons/ZepMemory";
import { AthenaIcon } from "@/icons/athena/index";
import { freezeAllIcon } from "@/icons/freezeAll";
import { GlobeOkIcon } from "@/icons/globe-ok";
import { GmailIcon } from "@/icons/gmail";
import { ThumbDownIconCustom, ThumbUpIconCustom } from "@/icons/thumbs";
import { TwitterLogoIcon } from "@radix-ui/react-icons";
import {
  AlertCircle,
  AlertTriangle,
  ArrowBigUp,
  ArrowLeft,
  ArrowRight,
  ArrowRightLeft,
  ArrowUpRight,
  ArrowUpToLine,
  AudioLines,
  Bell,
  Binary,
  Blocks,
  BookMarked,
  BookOpenText,
  BookmarkPlus,
  Bot,
  BotMessageSquare,
  Boxes,
  Braces,
  BrainCircuit,
  ChartNetwork,
  Check,
  CheckCheck,
  CheckCircle2,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronRightSquare,
  ChevronUp,
  ChevronsDownUp,
  ChevronsLeft,
  ChevronsRight,
  ChevronsUpDown,
  ChevronsUpDownIcon,
  Circle,
  CircleCheckBig,
  CircleDot,
  CircleOff,
  Clipboard,
  CloudDownload,
  Code,
  Code2,
  CodeXml,
  Cog,
  Columns2,
  Combine,
  Command,
  Compass,
  Copy,
  CopyPlus,
  CornerDownLeft,
  Cpu,
  Database,
  DatabaseZap,
  Delete,
  DollarSign,
  Dot,
  Download,
  DownloadCloud,
  Edit,
  Ellipsis,
  EllipsisVertical,
  Eraser,
  ExternalLink,
  Eye,
  EyeOff,
  File,
  FileChartColumn,
  FileClock,
  FileCode2,
  FileDown,
  FileJson,
  FilePen,
  FileQuestion,
  FileSearch,
  FileSearch2,
  FileSliders,
  FileText,
  FileType,
  FileType2,
  FileUp,
  Filter,
  FlaskConical,
  FolderIcon,
  FolderPlus,
  FolderPlusIcon,
  FolderSync,
  FolderUp,
  FormInput,
  Forward,
  Gift,
  GitBranchPlus,
  GitFork,
  GithubIcon,
  Globe,
  GripVertical,
  Group,
  Hammer,
  Heart,
  HelpCircle,
  Home,
  Image,
  Infinity,
  Info,
  InstagramIcon,
  Key,
  Keyboard,
  Laptop2,
  Layers,
  LayoutGrid,
  LayoutPanelTop,
  Link,
  Link2,
  List,
  ListChecks,
  ListFilter,
  ListOrdered,
  ListX,
  Loader2,
  Lock,
  LockOpen,
  LogIn,
  LogOut,
  LucideSend,
  Maximize2,
  Menu,
  MessageCircle,
  MessageSquare,
  MessageSquareMore,
  MessagesSquare,
  Mic,
  Mic2,
  MicOff,
  Minimize2,
  Minus,
  Monitor,
  Moon,
  MoonIcon,
  MoreHorizontal,
  Network,
  Newspaper,
  NotebookPen,
  OctagonAlert,
  OptionIcon,
  Package2,
  Palette,
  PanelLeftClose,
  PanelLeftOpen,
  PanelRightClose,
  PanelRightOpen,
  Paperclip,
  PaperclipIcon,
  Pen,
  Pencil,
  PencilLine,
  PencilRuler,
  PieChart,
  Pin,
  Plane,
  Play,
  Plus,
  PlusCircle,
  PlusSquare,
  PocketKnife,
  Radio,
  Redo,
  RefreshCcw,
  RefreshCcwDot,
  Repeat,
  Replace,
  RotateCcw,
  Save,
  SaveAll,
  Scan,
  ScanEye,
  Scissors,
  ScreenShare,
  Scroll,
  ScrollText,
  Search,
  Settings,
  Settings2,
  Share,
  Share2,
  Shield,
  Sigma,
  Sliders,
  SlidersHorizontal,
  Snowflake,
  Sparkles,
  Square,
  SquareArrowOutUpRight,
  SquareCode,
  SquarePen,
  SquarePlay,
  StickyNote,
  Store,
  Sun,
  SunIcon,
  Table,
  Tags,
  TerminalIcon,
  TerminalSquare,
  TextCursorInput,
  TextSearch,
  TextSearchIcon,
  ThumbsDown,
  ThumbsUp,
  ToyBrick,
  Trash2,
  Type,
  Undo,
  Ungroup,
  Unplug,
  Upload,
  User,
  UserCog2,
  UserMinus2,
  UserPlus2,
  Users,
  Users2,
  Variable,
  Wand2,
  Workflow,
  Wrench,
  X,
  XCircle,
  Youtube,
  Zap,
  ZoomIn,
  ZoomOut,
} from "lucide-react";
import { FaApple, FaDiscord, FaGithub } from "react-icons/fa";
import { AWSIcon } from "../icons/AWS";
import { AgentQLIcon } from "../icons/AgentQL";
import { AirbyteIcon } from "../icons/Airbyte";
import { AnthropicIcon } from "../icons/Anthropic";
import { ApifyIcon, ApifyWhiteIcon } from "../icons/Apify";
import { ArXivIcon } from "../icons/ArXiv";
import { ArizeIcon } from "../icons/Arize";
import { AssemblyAIIcon } from "../icons/AssemblyAI";
import { AstraDBIcon } from "../icons/AstraDB";
import { AzureIcon } from "../icons/Azure";
import { BingIcon } from "../icons/Bing";
import { BotMessageSquareIcon } from "../icons/BotMessageSquare";
import { CassandraIcon } from "../icons/Cassandra";
import { ChromaIcon } from "../icons/ChromaIcon";
import { ClickhouseIcon } from "../icons/Clickhouse";
import { CloudflareIcon } from "../icons/Cloudflare";
import { CohereIcon } from "../icons/Cohere";
import { ComposioIcon } from "../icons/Composio";
import { ConfluenceIcon } from "../icons/Confluence";
import { CouchbaseIcon } from "../icons/Couchbase";
import { CrewAiIcon } from "../icons/CrewAI";
import { DeepSeekIcon } from "../icons/DeepSeek";
import { ElasticsearchIcon } from "../icons/ElasticsearchStore";
import { EvernoteIcon } from "../icons/Evernote";
import { FBIcon } from "../icons/FacebookMessenger";
import { FirecrawlIcon } from "../icons/Firecrawl";
import { GitBookIcon } from "../icons/GitBook";
import { GitLoaderIcon } from "../icons/GitLoader";
import { GoogleIcon } from "../icons/Google";
import { GoogleGenerativeAIIcon } from "../icons/GoogleGenerativeAI";
import {
  GradientInfinity,
  GradientSave,
  GradientUngroup,
} from "../icons/GradientSparkles";
import { GroqIcon } from "../icons/Groq";
import { HCDIcon } from "../icons/HCD";
import { HuggingFaceIcon } from "../icons/HuggingFace";
import { IFixIcon } from "../icons/IFixIt";
import { IcosaIcon } from "../icons/Icosa";
import { LMStudioIcon } from "../icons/LMStudio";
import { LangChainIcon } from "../icons/LangChain";
import { MaritalkIcon } from "../icons/Maritalk";
import { Mem0 } from "../icons/Mem0";
import { MetaIcon } from "../icons/Meta";
import { MidjourneyIcon } from "../icons/Midjorney";
import { MongoDBIcon } from "../icons/MongoDB";
import { NeedleIcon } from "../icons/Needle";
import { NotDiamondIcon } from "../icons/NotDiamond";
import { NotionIcon } from "../icons/Notion";
import { NovitaIcon } from "../icons/Novita";
import { NvidiaIcon } from "../icons/Nvidia";
import { OlivyaIcon } from "../icons/Olivya";
import { OllamaIcon } from "../icons/Ollama";
import { OpenAiIcon } from "../icons/OpenAi";
import { OpenRouterIcon } from "../icons/OpenRouter";
import { OpenSearch } from "../icons/OpenSearch";
import { PineconeIcon } from "../icons/Pinecone";
import { PostgresIcon } from "../icons/Postgres";
import { PythonIcon } from "../icons/Python";
import { QDrantIcon } from "../icons/QDrant";
import { QianFanChatIcon } from "../icons/QianFanChat";
import { RedisIcon } from "../icons/Redis";
import { SambaNovaIcon } from "../icons/SambaNova";
import ScrapeGraph from "../icons/ScrapeGraphAI/ScrapeGraphAI";
import { SearxIcon } from "../icons/Searx";
import { SerperIcon } from "../icons/Serper";
import { ShareIcon } from "../icons/Share";
import { Share2Icon } from "../icons/Share2";
import SvgSlackIcon from "../icons/Slack/SlackIcon";
import { SpiderIcon } from "../icons/Spider";
import { Streamlit } from "../icons/Streamlit";
import { UpstashSvgIcon } from "../icons/Upstash";
import { VectaraIcon } from "../icons/VectaraIcon";
import { VertexAIIcon } from "../icons/VertexAI";
import { WeaviateIcon } from "../icons/Weaviate";
import SvgWikipedia from "../icons/Wikipedia/Wikipedia";
import SvgWolfram from "../icons/Wolfram/Wolfram";
import { HackerNewsIcon } from "../icons/hackerNews";
import { MistralIcon } from "../icons/mistral";
import { SupabaseIcon } from "../icons/supabase";
import { XAIIcon } from "../icons/xAI";
import { iconsType } from "../types/components";
import { MCPIcon } from "../icons/MCP";
import { WebsearchIcon } from "../icons/Websearch";
import i18n from "i18next";

export const BG_NOISE =
  "url(data:image/png;base64,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)";

export const gradients = [
  "bg-gradient-to-br from-gray-800 via-rose-700 to-violet-900",
  "bg-gradient-to-br from-green-200 via-green-300 to-blue-500",
  "bg-gradient-to-br from-yellow-200 via-yellow-400 to-yellow-700",
  "bg-gradient-to-br from-green-200 via-green-400 to-purple-700",
  "bg-gradient-to-br from-blue-100 via-blue-300 to-blue-500",
  "bg-gradient-to-br from-purple-400 to-yellow-400",
  "bg-gradient-to-br from-red-800 via-yellow-600 to-yellow-500",
  "bg-gradient-to-br from-blue-300 via-green-200 to-yellow-300",
  "bg-gradient-to-br from-blue-700 via-blue-800 to-gray-900",
  "bg-gradient-to-br from-green-300 to-purple-400",
  "bg-gradient-to-br from-yellow-200 via-pink-200 to-pink-400",
  "bg-gradient-to-br from-green-500 to-green-700",
  "bg-gradient-to-br from-rose-400 via-fuchsia-500 to-indigo-500",
  "bg-gradient-to-br from-sky-400 to-blue-500",
  "bg-gradient-to-br from-green-200 via-green-400 to-green-500",
  "bg-gradient-to-br from-red-400 via-gray-300 to-blue-500",
  "bg-gradient-to-br from-gray-900 to-gray-600 bg-gradient-to-r",
  "bg-gradient-to-br from-rose-500 via-red-400 to-red-500",
  "bg-gradient-to-br from-fuchsia-600 to-pink-600",
  "bg-gradient-to-br from-emerald-500 to-lime-600",
  "bg-gradient-to-br from-rose-500 to-indigo-700",
  "bg-gradient-to-br bg-gradient-to-tr from-violet-500 to-orange-300",
  "bg-gradient-to-br from-gray-900 via-purple-900 to-violet-600",
  "bg-gradient-to-br from-yellow-200 via-red-500 to-fuchsia-500",
  "bg-gradient-to-br from-sky-400 to-indigo-900",
  "bg-gradient-to-br from-amber-200 via-violet-600 to-sky-900",
  "bg-gradient-to-br from-amber-700 via-orange-300 to-rose-800",
  "bg-gradient-to-br from-gray-300 via-fuchsia-600 to-orange-600",
  "bg-gradient-to-br from-fuchsia-500 via-red-600 to-orange-400",
  "bg-gradient-to-br from-sky-400 via-rose-400 to-lime-400",
  "bg-gradient-to-br from-lime-600 via-yellow-300 to-red-600",
];

/*
Specifications
#FF3276 -> #F480FF
#1A0250 -> #2F10FE
#98F4FE -> #9BFEAA
#F480FF -> #7528FC
#F480FF -> #9BFEAA
#2F10FE -> #9BFEAA
#BB277F -> #050154
#7528FC -> #9BFEAA
#2F10FE -> #98F4FE
*/
export const flowGradients = [
  "linear-gradient(90deg, #FF3276 0%, #F480FF 100%)",
  "linear-gradient(90deg, #1A0250 0%, #2F10FE 100%)",
  "linear-gradient(90deg, #98F4FE 0%, #9BFEAA 100%)",
  "linear-gradient(90deg, #F480FF 0%, #7528FC 100%)",
  "linear-gradient(90deg, #F480FF 0%, #9BFEAA 100%)",
  "linear-gradient(90deg, #2F10FE 0%, #9BFEAA 100%)",
  "linear-gradient(90deg, #BB277F 0%, #050154 100%)",
  "linear-gradient(90deg, #7528FC 0%, #9BFEAA 100%)",
  "linear-gradient(90deg, #2F10FE 0%, #98F4FE 100%)",
];

export const toolModeGradient =
  "linear-gradient(-60deg,var(--tool-mode-gradient-1) 0%,var(--tool-mode-gradient-2) 100%)";

export const swatchColors = [
  "bg-neon-fuschia text-white",
  "bg-digital-orchid text-plasma-purple",
  "bg-plasma-purple text-digital-orchid",
  "bg-electric-blue text-holo-frost",
  "bg-holo-frost text-electric-blue",
  "bg-terminal-green text-cosmic-void",
];

export const nodeColors: { [char: string]: string } = {
  inputs: "#10B981",
  outputs: "#AA2411",
  data: "#198BF6",
  prompts: "#4367BF",
  models: "#ab11ab",
  model_specs: "#6344BE",
  chains: "#FE7500",
  list: "#9AAE42",
  agents: "#903BBE",
  Olivya: "#00413B",
  tools: "#00fbfc",
  memories: "#F5B85A",
  saved_components: "#a5B85A",
  advanced: "#000000",
  chat: "#198BF6",
  thought: "#272541",
  embeddings: "#42BAA7",
  documentloaders: "#7AAE42",
  vectorstores: "#AA8742",
  vectorsearch: "#AA8742",
  textsplitters: "#B47CB5",
  toolkits: "#DB2C2C",
  wrappers: "#E6277A",
  notion: "#000000",
  Notion: "#000000",
  AssemblyAI: "#213ED7",
  assemblyai: "#213ED7",
  helpers: "#31A3CC",
  prototypes: "#E6277A",
  astra_assistants: "#272541",
  langchain_utilities: "#31A3CC",
  output_parsers: "#E6A627",
  // custom_components: "#ab11ab",
  retrievers: "#e6b25a",
  str: "#4F46E5",
  Text: "#4F46E5",
  unknown: "#9CA3AF",
  Document: "#65a30d",
  Data: "#dc2626",
  Message: "#4f46e5",
  Prompt: "#7c3aed",
  Embeddings: "#10b981",
  BaseLanguageModel: "#c026d3",
  LanguageModel: "#c026d3",
  Agent: "#903BBE",
  AgentExecutor: "#903BBE",
  Tool: "#00fbfc",
};

export const nodeColorsName: { [char: string]: string } = {
  // custom_components: "#ab11ab",
  inputs: "emerald",
  outputs: "red",
  data: "sky",
  prompts: "blue",
  models: "fuchsia",
  model_specs: "violet",
  chains: "orange",
  list: "lime",
  agents: "purple",
  tools: "cyan",
  memories: "amber",
  saved_components: "lime",
  advanced: "slate",
  chat: "sky",
  thought: "zinc",
  embeddings: "teal",
  documentloaders: "lime",
  vectorstores: "yellow",
  vectorsearch: "yellow",
  textsplitters: "fuchsia",
  toolkits: "red",
  wrappers: "rose",
  notion: "slate",
  Notion: "slate",
  AssemblyAI: "blue",
  assemblyai: "blue",
  helpers: "cyan",
  prototypes: "rose",
  astra_assistants: "indigo",
  langchain_utilities: "sky",
  output_parsers: "yellow",
  retrievers: "yellow",
  str: "indigo",
  Text: "indigo",
  unknown: "gray",
  Document: "lime",
  Data: "red",
  Message: "indigo",
  Prompt: "violet",
  Embeddings: "emerald",
  BaseLanguageModel: "fuchsia",
  LanguageModel: "fuchsia",
  Agent: "purple",
  AgentExecutor: "purple",
  Tool: "cyan",
  BaseChatMemory: "cyan",
  BaseChatMessageHistory: "orange",
  Memory: "orange",
  DataFrame: "pink",
};

export const FILE_ICONS = {
  json: {
    icon: "FileJson",
    color: "text-datatype-indigo dark:text-datatype-indigo-foreground",
  },
  csv: {
    icon: "FileChartColumn",
    color: "text-datatype-emerald dark:text-datatype-emerald-foreground",
  },
  txt: {
    icon: "FileType",
    color: "text-datatype-purple dark:text-datatype-purple-foreground",
  },
  pdf: {
    icon: "File",
    color: "text-datatype-red dark:text-datatype-red-foreground",
  },
};

export const SIDEBAR_CATEGORIES = [
  { display_name: i18n.t('saved'), name: "saved_components", icon: "GradientSave" },
  { display_name: i18n.t('inputs'), name: "inputs", icon: "Download" },
  { display_name: i18n.t('outputs'), name: "outputs", icon: "Upload" },
  { display_name: i18n.t('prompts'), name: "prompts", icon: "TerminalSquare" },
  { display_name: i18n.t('data'), name: "data", icon: "Database" },
  { display_name: i18n.t('processing'), name: "processing", icon: "ListFilter" },
  { display_name: i18n.t('models'), name: "models", icon: "BrainCircuit" },
  { display_name: i18n.t('vector-stores'), name: "vectorstores", icon: "Layers" },
  { display_name: i18n.t('embeddings'), name: "embeddings", icon: "Binary" },
  { display_name: i18n.t('agents'), name: "agents", icon: "Bot" },
  { display_name: i18n.t('chains'), name: "chains", icon: "Link" },
  { display_name: i18n.t('loaders'), name: "documentloaders", icon: "Paperclip" },
  { display_name: i18n.t('link-extractors'), name: "link_extractors", icon: "Link2" },
  { display_name: i18n.t('memories'), name: "memories", icon: "Cpu" },
  { display_name: i18n.t('output-parsers'), name: "output_parsers", icon: "Compass" },
  { display_name: i18n.t('prototypes'), name: "prototypes", icon: "FlaskConical" },
  { display_name: i18n.t('retrievers'), name: "retrievers", icon: "FileSearch" },
  { display_name: i18n.t('text-splitters'), name: "textsplitters", icon: "Scissors" },
  { display_name: i18n.t('toolkits'), name: "toolkits", icon: "Package2" },
  { display_name: i18n.t('tools'), name: "tools", icon: "Hammer" },
  { display_name: i18n.t('logic'), name: "logic", icon: "ArrowRightLeft" },
  { display_name: i18n.t('helpers'), name: "helpers", icon: "Wand2" },
  { display_name: i18n.t('mcp'), name: "mcp", icon: "MCP" },
  { display_name: i18n.t('websearch'), name: "websearch", icon: "Websearch" },
];

export const SIDEBAR_BUNDLES = [
  { display_name: "Gmail", name: "gmail", icon: "Gmail" },
  // Add apify
  { display_name: "Apify", name: "apify", icon: "Apify" },
  { display_name: "LangChain", name: "langchain_utilities", icon: "LangChain" },
  { display_name: "AgentQL", name: "agentql", icon: "AgentQL" },
  { display_name: "AssemblyAI", name: "assemblyai", icon: "AssemblyAI" },
  {
    display_name: "DataStax",
    name: "astra_assistants",
    icon: "AstraDB",
  },
  { display_name: "Olivya", name: "olivya", icon: "Olivya" },
  { display_name: "LangWatch", name: "langwatch", icon: "Langwatch" },
  { display_name: "Notion", name: "Notion", icon: "Notion" },
  { display_name: "Needle", name: "needle", icon: "Needle" },
  { display_name: "NVIDIA", name: "nvidia", icon: "NVIDIA" },
  { display_name: "Vectara", name: "vectara", icon: "Vectara" },
  { display_name: "Icosa Computing", name: "icosacomputing", icon: "Icosa" },
  { display_name: "Google", name: "google", icon: "Google" },
  { display_name: "CrewAI", name: "crewai", icon: "CrewAI" },
  { display_name: "NotDiamond", name: "notdiamond", icon: "NotDiamond" },
  { display_name: "Composio", name: "composio", icon: "Composio" },
  { display_name: "Cohere", name: "cohere", icon: "Cohere" },
  { display_name: "Firecrawl", name: "firecrawl", icon: "FirecrawlCrawlApi" },
  { display_name: "Unstructured", name: "unstructured", icon: "Unstructured" },
  { display_name: "Git", name: "git", icon: "GitLoader" },
  { display_name: "Confluence", name: "confluence", icon: "Confluence" },
  { display_name: "Mem0", name: "mem0", icon: "Mem0" },
  { display_name: "Youtube", name: "youtube", icon: "YouTube" },
  { display_name: "ScrapeGraph AI", name: "scrapegraph", icon: "ScrapeGraph" },
];

export const categoryIcons = {
  saved_components: GradientSave,
  inputs: Download,
  outputs: Upload,
  prompts: TerminalSquare,
  data: Database,
  models: BrainCircuit,
  helpers: Wand2,
  vectorstores: Layers,
  embeddings: Binary,
  agents: Bot,
  astra_assistants: Sparkles,
  chains: Link,
  documentloaders: Paperclip,
  langchain_utilities: PocketKnife,
  link_extractors: Link2,
  memories: Cpu,
  output_parsers: Compass,
  prototypes: FlaskConical,
  retrievers: FileSearch,
  textsplitters: Scissors,
  toolkits: Package2,
  tools: Hammer,
  custom: Edit,
  custom_components: GradientInfinity,
};

export const nodeIconsLucide: iconsType = {
  //Category Icons
  inputs: Download,
  outputs: Upload,
  prompts: TerminalSquare,
  data: Database,
  models: BrainCircuit,
  helpers: Wand2,
  vectorstores: Layers,
  embeddings: Binary,
  agents: Bot,
  astra_assistants: Sparkles,
  chains: Link,
  documentloaders: Paperclip,
  langchain_utilities: PocketKnife,
  link_extractors: Link2,
  memories: Cpu,
  output_parsers: Compass,
  prototypes: FlaskConical,
  retrievers: FileSearch,
  textsplitters: Scissors,
  toolkits: Package2,
  tools: Hammer,
  custom_components: GradientInfinity,
  ChatInput: MessagesSquare,
  ChatOutput: MessagesSquare,
  //Integration Icons
  Gmail: GmailIcon,
  LMStudio: LMStudioIcon,
  Notify: Bell,
  ListFlows: Group,
  ClearMessageHistory: FileClock,
  Python: PythonIcon,
  BWPython: BWPythonIcon,
  AzureChatOpenAi: AzureIcon,
  Ollama: OllamaIcon,
  ChatOllama: OllamaIcon,
  AzureOpenAiEmbeddings: AzureIcon,
  Azure: AzureIcon,
  OllamaEmbeddings: OllamaIcon,
  ChatOllamaModel: OllamaIcon,
  FAISS: MetaIcon,
  Maritalk: MaritalkIcon,
  FaissSearch: MetaIcon,
  LangChain: LangChainIcon,
  AzureOpenAiModel: AzureIcon,
  Redis: RedisIcon,
  RedisSearch: RedisIcon,
  PostgresChatMessageHistory: PostgresIcon,
  BaiduQianfan: QianFanChatIcon,
  Vectara: VectaraIcon,
  ArrowUpToLine: ArrowUpToLine,
  Cassandra: CassandraIcon,
  Chroma: ChromaIcon,
  Couchbase: CouchbaseIcon,
  Clickhouse: ClickhouseIcon,
  Cloudflare: CloudflareIcon,
  AirbyteJSONLoader: AirbyteIcon,
  AmazonBedrockEmbeddings: AWSIcon,
  Amazon: AWSIcon,
  AWSInverted: AWSInvertedIcon,
  Anthropic: AnthropicIcon,
  ArXiv: ArXivIcon,
  ChatAnthropic: AnthropicIcon,
  assemblyai: AssemblyAIIcon,
  AgentQL: AgentQLIcon,
  AssemblyAI: AssemblyAIIcon,
  AstraDB: AstraDBIcon,
  BingSearchAPIWrapper: BingIcon,
  BingSearchRun: BingIcon,
  CloudDownload,
  Olivya: OlivyaIcon,
  Bing: BingIcon,
  Cohere: CohereIcon,
  ChevronsUpDownIcon,
  CohereEmbeddings: CohereIcon,
  EverNoteLoader: EvernoteIcon,
  FacebookChatLoader: FBIcon,
  FirecrawlCrawlApi: FirecrawlIcon,
  FirecrawlScrapeApi: FirecrawlIcon,
  FirecrawlMapApi: FirecrawlIcon,
  FirecrawlExtractApi: FirecrawlIcon,
  GitbookLoader: GitBookIcon,
  GoogleSearchAPIWrapper: GoogleIcon,
  GoogleSearchResults: GoogleIcon,
  GoogleSearchRun: GoogleIcon,
  GoogleSearchAPI: GoogleIcon,
  GoogleSerperAPI: GoogleIcon,
  Google: GoogleIcon,
  GoogleGenerativeAI: GoogleGenerativeAIIcon,
  Groq: GroqIcon,
  HCD: HCDIcon,
  HNLoader: HackerNewsIcon,
  Unstructured: UnstructuredIcon,
  Filter: Filter,
  HuggingFaceHub: HuggingFaceIcon,
  HuggingFace: HuggingFaceIcon,
  HuggingFaceEmbeddings: HuggingFaceIcon,
  Icosa: IcosaIcon,
  IFixitLoader: IFixIcon,
  CrewAI: CrewAiIcon,
  NotDiamond: NotDiamondIcon,
  Composio: ComposioIcon,
  Meta: MetaIcon,
  Midjorney: MidjourneyIcon,
  MongoDBAtlasVectorSearch: MongoDBIcon,
  MongoDB: MongoDBIcon,
  MongoDBChatMessageHistory: MongoDBIcon,
  notion: NotionIcon,
  Notion: NotionIcon,
  NotionDirectoryLoader: NotionIcon,
  novita: NovitaIcon,
  Novita: NovitaIcon,
  Needle: NeedleIcon,
  NVIDIA: NvidiaIcon,
  ChatOpenAI: OpenAiIcon,
  AzureChatOpenAI: OpenAiIcon,
  OpenAI: OpenAiIcon,
  OpenRouter: OpenRouterIcon,
  DeepSeek: DeepSeekIcon,
  xAI: XAIIcon,
  OpenAIEmbeddings: OpenAiIcon,
  Pinecone: PineconeIcon,
  Qdrant: QDrantIcon,
  ElasticsearchStore: ElasticsearchIcon,
  Weaviate: WeaviateIcon,
  SambaNova: SambaNovaIcon,
  Searx: SearxIcon,
  SlackDirectoryLoader: SvgSlackIcon,
  SpiderTool: SpiderIcon,
  SupabaseVectorStore: SupabaseIcon,
  Supabase: SupabaseIcon,
  VertexAI: VertexAIIcon,
  ChatVertexAI: VertexAIIcon,
  VertexAIEmbeddings: VertexAIIcon,
  Share3: ShareIcon,
  Share4: Share2Icon,
  WikipediaAPIWrapper: SvgWikipedia,
  WolframAlphaAPIWrapper: SvgWolfram,
  WikipediaQueryRun: SvgWikipedia,
  WolframAlphaQueryRun: SvgWolfram,
  WolframAlphaAPI: SvgWolfram,
  group_components: GradientUngroup,
  Streamlit,
  Discord: FaDiscord,
  MistralAI: MistralIcon,
  Upstash: UpstashSvgIcon,
  Confluence: ConfluenceIcon,
  AIML: AIMLIcon,
  "AI/ML": AIMLIcon,
  GitLoader: GitLoaderIcon,
  athenaIcon: AthenaIcon,
  DuckDuckGo: DuckDuckGoIcon,
  Perplexity,
  TavilyIcon,
  OpenSearch,
  GithubIcon,
  FaGithub,
  FaApple,
  YouTube: YouTubeIcon,
  Milvus: MilvusIcon,
  ExaSearch: ExaIcon,
  ZepMemory: ZepMemoryIcon,
  Langwatch: LangwatchIcon,
  Mem0,
  Glean: GleanIcon,
  GleanAPI: GleanIcon,
  SerpSearch: SerpSearchIcon,
  SearchAPI: SearchAPIIcon,
  Wikipedia: WikipediaIcon,
  Arize: ArizeIcon,
  Apify: ApifyIcon,
  ApifyWhite: ApifyWhiteIcon,
  MCP:MCPIcon,
  Websearch: WebsearchIcon,

  //Node Icons
  model_specs: FileSliders,
  advanced: Laptop2,
  chat: MessageCircle,
  saved_components: GradientSave,
  vectorsearch: TextSearch,
  wrappers: Gift,
  unknown: HelpCircle,
  custom: Edit,
  Keyboard,
  ArrowRight,
  Play,
  BotMessageSquareIcon,
  CheckCheck,
  ListFilter,
  ScrollText,
  Workflow,
  User,
  ScanEye,
  Type,
  FolderIcon,
  X,
  Trash2,
  CircleOff,
  Boxes,
  Network,
  XCircle,
  Info,
  CheckCircle2,
  FileJson,
  FileChartColumn,
  FileType,
  File,
  SquarePen,
  Zap,
  MessagesSquare,
  ExternalLink,
  ChevronsUpDown,
  Check,
  Home,
  Users2,
  SunIcon,
  MoonIcon,
  Bell,
  AlertTriangle,
  ChevronLeft,
  SlidersHorizontal,
  GoogleDrive: GoogleDriveIcon,
  OneDrive: OneDriveIcon,
  Dropbox: DropboxIcon,
  Palette,
  RefreshCcwDot,
  FolderUp,
  SquarePlay,
  LayoutPanelTop,
  Database,
  Blocks,
  ChevronDown,
  ArrowLeft,
  BrainCircuit,
  Wand2,
  Layers,
  Binary,
  Paperclip,
  PocketKnife,
  Scissors,
  Cpu,
  Hammer,
  GradientSave,
  Shield,
  NotebookPen,
  Plus,
  Redo,
  Settings2,
  FileType2,
  Undo,
  FileSearch2,
  ChevronRight,
  Circle,
  CircleDot,
  Clipboard,
  PlusCircle,
  PlusSquare,
  Code2,
  Globe,
  Variable,
  Snowflake,
  Store,
  Download,
  Replace,
  Eraser,
  Lock,
  LockOpen,
  ListX,
  Newspaper,
  Tags,
  CodeXml,
  PieChart,
  LucideSend,
  Sparkles,
  DownloadCloud,
  FileText,
  FolderPlus,
  GitFork,
  FileDown,
  FilePen,
  FileUp,
  Menu,
  Save,
  Search,
  Copy,
  Upload,
  MessageSquare,
  MoreHorizontal,
  UserMinus2,
  UserPlus2,
  Pencil,
  PencilRuler,
  ChevronsRight,
  ChevronsLeft,
  EyeOff,
  Eye,
  UserCog2,
  Key,
  Unplug,
  Group,
  LogIn,
  ChevronUp,
  PencilLine,
  Ungroup,
  BookMarked,
  Minus,
  LogOut,
  BotMessageSquare,
  Square,
  Minimize2,
  Maximize2,
  FormInput,
  ChevronRightSquare,
  Plane,
  Users,
  ListOrdered,
  SaveAll,
  MessageSquareMore,
  Forward,
  Share2,
  Share,
  GitBranchPlus,
  Infinity,
  Loader2,
  BookmarkPlus,
  Heart,
  Package2,
  FileSearch,
  Compass,
  Link2,
  Pin,
  Link,
  ToyBrick,
  RefreshCcw,
  SquareArrowOutUpRight,
  Combine,
  TerminalIcon,
  TerminalSquare,
  TextCursorInput,
  Repeat,
  Sliders,
  ScreenShare,
  Code,
  OctagonAlert,
  Ellipsis,
  Braces,
  FlaskConical,
  AlertCircle,
  Bot,
  EllipsisVertical,
  Delete,
  Command,
  ArrowBigUp,
  PanelRightClose,
  Dot,
  LayoutGrid,
  StickyNote,
  note: StickyNote,
  RotateCcw,
  Wrench,
  GripVertical,
  FolderPlusIcon,
  PaperclipIcon,
  Settings,
  Monitor,
  Moon,
  Sun,
  PanelLeftClose,
  PanelLeftOpen,
  ArrowUpRight,
  Scroll,
  Image,
  CopyPlus,
  Pen,
  TwitterLogoIcon,
  InstagramIcon,
  TextSearchIcon,
  FileQuestion,
  Youtube,
  List,
  SquareCode,
  ListChecks,
  PanelRightOpen,
  CornerDownLeft,
  ChevronsDownUp,
  OptionIcon,
  ChartNetwork,
  Option: OptionIcon,
  FreezeAll: freezeAllIcon,
  Table,
  Scan,
  GlobeOkIcon,
  CircleCheckBig,
  ZoomIn,
  ZoomOut,
  Sigma,
  Radio,
  DatabaseZap,
  Cog,
  ArrowRightLeft,
  FolderSync,
  ThumbsUp,
  ThumbsDown,
  ThumbDownIconCustom,
  ThumbUpIconCustom,
  Serper: SerperIcon,
  javascript: JSIcon,
  FileCode2,
  Columns2,
  ScrapeGraphAI: ScrapeGraph,
  ScrapeGraph: ScrapeGraph,
  ScrapeGraphSmartScraperApi: ScrapeGraph,
  ScrapeGraphMarkdownifyApi: ScrapeGraph,
  Mic,
  MicOff,
  Mic2,
  DollarSign,
  BookOpenText,
  AudioLines,
};
