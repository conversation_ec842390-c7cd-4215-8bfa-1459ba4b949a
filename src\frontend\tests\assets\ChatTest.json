{"id": "9fd63190-2688-4c9f-9f6a-f48e8a4a3bff", "data": {"nodes": [{"id": "ChatOutput-xPeM1", "type": "genericNode", "position": {"x": 231.45405028405742, "y": -109.00715949940081}, "data": {"type": "ChatOutput", "node": {"template": {"code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Optional, Union\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.field_typing import Text\nfrom langflow.schema import Record\n\n\nclass ChatOutput(ChatComponent):\n    display_name = \"Chat Output\"\n    description = \"Display a chat message in the Playground.\"\n    icon = \"ChatOutput\"\n\n    def build(\n        self,\n        sender: Optional[str] = \"Machine\",\n        sender_name: Optional[str] = \"AI\",\n        input_value: Optional[str] = None,\n        session_id: Optional[str] = None,\n        return_record: Optional[bool] = False,\n        record_template: Optional[str] = \"{text}\",\n    ) -> Union[Text, Record]:\n        return super().build_with_record(\n            sender=sender,\n            sender_name=sender_name,\n            input_value=input_value,\n            session_id=session_id,\n            return_record=return_record,\n            record_template=record_template or \"\",\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "fileTypes": [], "file_path": "", "password": false, "name": "input_value", "display_name": "Message", "advanced": false, "input_types": ["Text"], "dynamic": false, "info": "", "load_from_db": false, "title_case": false}, "record_template": {"type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "{text}", "fileTypes": [], "file_path": "", "password": false, "name": "record_template", "display_name": "Record Template", "advanced": true, "dynamic": false, "info": "In case of Message being a Record, this template will be used to convert it to text.", "load_from_db": false, "title_case": false, "input_types": ["Text"]}, "return_record": {"type": "bool", "required": false, "placeholder": "", "list": false, "show": true, "multiline": false, "value": false, "fileTypes": [], "file_path": "", "password": false, "name": "return_record", "display_name": "Return Record", "advanced": true, "dynamic": false, "info": "Return the message as a record containing the sender, sender_name, and session_id.", "load_from_db": false, "title_case": false}, "sender": {"type": "str", "required": false, "placeholder": "", "list": true, "show": true, "multiline": false, "value": "Machine", "fileTypes": [], "file_path": "", "password": false, "options": ["Machine", "User"], "name": "sender", "display_name": "Sender Type", "advanced": true, "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "input_types": ["Text"]}, "sender_name": {"type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": false, "value": "AI", "fileTypes": [], "file_path": "", "password": false, "name": "sender_name", "display_name": "Sender Name", "advanced": false, "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "input_types": ["Text"]}, "session_id": {"type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": false, "fileTypes": [], "file_path": "", "password": false, "name": "session_id", "display_name": "Session ID", "advanced": true, "dynamic": false, "info": "If provided, the message will be stored in the memory.", "load_from_db": false, "title_case": false, "input_types": ["Text"]}, "_type": "CustomComponent"}, "description": "Display a chat message in the Playground.", "icon": "ChatOutput", "base_classes": ["object", "Record", "str", "Text"], "display_name": "Chat Output", "documentation": "", "custom_fields": {"sender": null, "sender_name": null, "input_value": null, "session_id": null, "return_record": null, "record_template": null}, "output_types": ["Text", "Record"], "field_formatters": {}, "frozen": false, "field_order": [], "beta": false}, "id": "ChatOutput-xPeM1"}, "selected": false, "width": 384, "height": 383, "positionAbsolute": {"x": 231.45405028405742, "y": -109.00715949940081}, "dragging": false}, {"id": "ChatInput-XYvUc", "type": "genericNode", "position": {"x": -389.67919096408036, "y": 10.79598792234681}, "data": {"type": "ChatInput", "node": {"template": {"code": {"type": "code", "required": true, "placeholder": "", "list": false, "show": true, "multiline": true, "value": "from typing import Optional, Union\n\nfrom langflow.base.io.chat import ChatComponent\nfrom langflow.field_typing import Text\nfrom langflow.schema import Record\n\n\nclass ChatInput(ChatComponent):\n    display_name = \"Chat Input\"\n    description = \"Get chat inputs from the Playground.\"\n    icon = \"ChatInput\"\n\n    def build_config(self):\n        build_config = super().build_config()\n        build_config[\"input_value\"] = {\n            \"input_types\": [],\n            \"display_name\": \"Message\",\n            \"multiline\": True,\n        }\n\n        return build_config\n\n    def build(\n        self,\n        sender: Optional[str] = \"User\",\n        sender_name: Optional[str] = \"User\",\n        input_value: Optional[str] = None,\n        session_id: Optional[str] = None,\n        return_record: Optional[bool] = False,\n    ) -> Union[Text, Record]:\n        return super().build_no_record(\n            sender=sender,\n            sender_name=sender_name,\n            input_value=input_value,\n            session_id=session_id,\n            return_record=return_record,\n        )\n", "fileTypes": [], "file_path": "", "password": false, "name": "code", "advanced": true, "dynamic": true, "info": "", "load_from_db": false, "title_case": false}, "input_value": {"type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": true, "fileTypes": [], "file_path": "", "password": false, "name": "input_value", "display_name": "Message", "advanced": false, "input_types": [], "dynamic": false, "info": "", "load_from_db": false, "title_case": false}, "return_record": {"type": "bool", "required": false, "placeholder": "", "list": false, "show": true, "multiline": false, "value": false, "fileTypes": [], "file_path": "", "password": false, "name": "return_record", "display_name": "Return Record", "advanced": true, "dynamic": false, "info": "Return the message as a record containing the sender, sender_name, and session_id.", "load_from_db": false, "title_case": false}, "sender": {"type": "str", "required": false, "placeholder": "", "list": true, "show": true, "multiline": false, "value": "User", "fileTypes": [], "file_path": "", "password": false, "options": ["Machine", "User"], "name": "sender", "display_name": "Sender Type", "advanced": true, "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "input_types": ["Text"]}, "sender_name": {"type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": false, "value": "User", "fileTypes": [], "file_path": "", "password": false, "name": "sender_name", "display_name": "Sender Name", "advanced": false, "dynamic": false, "info": "", "load_from_db": false, "title_case": false, "input_types": ["Text"]}, "session_id": {"type": "str", "required": false, "placeholder": "", "list": false, "show": true, "multiline": false, "fileTypes": [], "file_path": "", "password": false, "name": "session_id", "display_name": "Session ID", "advanced": true, "dynamic": false, "info": "If provided, the message will be stored in the memory.", "load_from_db": false, "title_case": false, "input_types": ["Text"]}, "_type": "CustomComponent"}, "description": "Get chat inputs from the Playground.", "icon": "ChatInput", "base_classes": ["object", "Record", "str", "Text"], "display_name": "Chat Input", "documentation": "", "custom_fields": {"sender": null, "sender_name": null, "input_value": null, "session_id": null, "return_record": null}, "output_types": ["Text", "Record"], "field_formatters": {}, "frozen": false, "field_order": [], "beta": false}, "id": "ChatInput-XYvUc"}, "selected": false, "width": 384, "height": 375}], "edges": [{"source": "ChatInput-XYvUc", "sourceHandle": "{œbaseClassesœ:[œobjectœ,œRecordœ,œstrœ,œTextœ],œdataTypeœ:œChatInputœ,œidœ:œChatInput-XYvUcœ}", "target": "ChatOutput-xPeM1", "targetHandle": "{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-xPeM1œ,œinputTypesœ:[œTextœ],œtypeœ:œstrœ}", "data": {"targetHandle": {"fieldName": "input_value", "id": "ChatOutput-xPeM1", "inputTypes": ["Text"], "type": "str"}, "sourceHandle": {"baseClasses": ["object", "Record", "str", "Text"], "dataType": "ChatInput", "id": "ChatInput-XYvUc"}}, "style": {"stroke": "#555"}, "className": "stroke-gray-900 stroke-connection", "id": "reactflow__edge-ChatInput-XYvUc{œbaseClassesœ:[œobjectœ,œRecordœ,œstrœ,œTextœ],œdataTypeœ:œChatInputœ,œidœ:œChatInput-XYvUcœ}-ChatOutput-xPeM1{œfieldNameœ:œinput_valueœ,œidœ:œChatOutput-xPeM1œ,œinputTypesœ:[œTextœ],œtypeœ:œstrœ}"}], "viewport": {"x": 659.7078595480464, "y": 328.05200504526294, "zoom": 0.64075547800662}}, "description": "Unravel the Art of Articulation.", "name": "ChatTest", "last_tested_version": "1.0.0a14", "is_component": false}