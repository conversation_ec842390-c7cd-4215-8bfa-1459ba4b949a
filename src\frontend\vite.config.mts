import react from "@vitejs/plugin-react-swc";
import * as dotenv from "dotenv";
import path from "path";
import { defineConfig, loadEnv } from "vite";
import svgr from "vite-plugin-svgr";
import tsconfigPaths from "vite-tsconfig-paths";
import legacy from '@vitejs/plugin-legacy';
import {
  API_ROUTES,
  BASENAME,
  PORT,
  PROXY_TARGET,
} from "./src/customization/config-constants";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  const envLangflowResult = dotenv.config({
    path: path.resolve(__dirname, "../../.env"),
  });

  const envLangflow = envLangflowResult.parsed || {};

  const apiRoutes = API_ROUTES || ["^/api/v1/", "^/api/v2/", "/health"];

  const target =
    env.VITE_PROXY_TARGET || PROXY_TARGET || "http://127.0.0.1:7860";

  const port = Number(env.VITE_PORT) || PORT || 3000;

  const proxyTargets = apiRoutes.reduce((proxyObj, route) => {
    proxyObj[route] = {
      target: target,
      changeOrigin: true,
      secure: false,
      ws: true,
    };
    return proxyObj;
  }, {});

  return {
    base: BASENAME || "",
    build: {
      outDir: "build",
    },
    define: {
      "process.env.BACKEND_URL": JSON.stringify(
        envLangflow.BACKEND_URL ?? "http://127.0.0.1:7860",
      ),
      "process.env.ACCESS_TOKEN_EXPIRE_SECONDS": JSON.stringify(
        envLangflow.ACCESS_TOKEN_EXPIRE_SECONDS ?? 60,
      ),
      "process.env.CI": JSON.stringify(envLangflow.CI ?? false),
      "process.env.LANGFLOW_AUTO_LOGIN": JSON.stringify(
        envLangflow.LANGFLOW_AUTO_LOGIN ?? true,
      ),
    },
    plugins: [react(), svgr(), tsconfigPaths(),
      // legacy({
      //   targets: ['chrome >= 49', 'ie >= 11'],
      //   modernPolyfills: [
      //     'core-js/modules/esnext.array.to-sorted',
      //     'core-js/modules/esnext.array.to-reversed',
      //   ],
      //   // ⚠️ 关键：不要全局注入 core-js/stable
      //   // additionalLegacyPolyfills: ['core-js/stable'], // ❌ 去掉！
      //   // 改为按需注入
      //   renderLegacyChunks: true,
      //   externalSystemJS: false,
      //   // 只注入必要的 polyfill
      //   polyfills: [
      //     'es.array.iterator',
      //     'es.promise',
      //     'es.object.assign',
      //     'es.symbol',
      //     // 按需添加你实际用到的
      //   ],
      // }),
    ],
    server: {
      port: port,
      proxy: {
        ...proxyTargets,
      },
    },
  };
});
